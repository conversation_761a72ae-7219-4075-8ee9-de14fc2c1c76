<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class BlogPostRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check() && auth()->user()->isAdmin();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $postId = $this->route('post') ? $this->route('post')->id : null;

        return [
            'title' => 'required|string|max:255',
            'slug' => [
                'nullable',
                'string',
                'max:255',
                'regex:/^[a-z0-9]+(?:-[a-z0-9]+)*$/',
                Rule::unique('blog_posts', 'slug')->ignore($postId)
            ],
            'excerpt' => 'required|string|max:500',
            'content' => 'required|string',
            'category_id' => 'required|exists:blog_categories,id',
            'featured_image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:2048',
            'status' => 'required|in:draft,published,scheduled',
            'published_at' => 'nullable|date|after_or_equal:now',
            'is_featured' => 'boolean',
            'tags' => 'nullable|array',
            'tags.*' => 'exists:blog_tags,id',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'title' => 'post title',
            'excerpt' => 'post excerpt',
            'content' => 'post content',
            'category_id' => 'category',
            'featured_image' => 'featured image',
            'published_at' => 'publish date',
            'is_featured' => 'featured status',
            'tags' => 'tags',
            'meta_title' => 'meta title',
            'meta_description' => 'meta description',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'title.required' => 'The post title is required.',
            'title.max' => 'The post title may not be greater than 255 characters.',
            'slug.regex' => 'The slug format is invalid. Use lowercase letters, numbers, and hyphens only.',
            'slug.unique' => 'This slug is already taken. Please choose a different one.',
            'excerpt.required' => 'The post excerpt is required.',
            'excerpt.max' => 'The excerpt may not be greater than 500 characters.',
            'content.required' => 'The post content is required.',
            'category_id.required' => 'Please select a category.',
            'category_id.exists' => 'The selected category is invalid.',
            'featured_image.image' => 'The file must be an image.',
            'featured_image.mimes' => 'The image must be a file of type: jpeg, png, jpg, gif, webp.',
            'featured_image.max' => 'The image may not be greater than 2MB.',
            'status.required' => 'Please select a post status.',
            'status.in' => 'The selected status is invalid.',
            'published_at.date' => 'The publish date must be a valid date.',
            'published_at.after_or_equal' => 'The publish date must be today or in the future.',
            'tags.array' => 'Tags must be an array.',
            'tags.*.exists' => 'One or more selected tags are invalid.',
            'meta_title.max' => 'Meta title may not be greater than 255 characters.',
            'meta_description.max' => 'Meta description may not be greater than 500 characters.',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Convert checkboxes to boolean
        $this->merge([
            'is_featured' => $this->boolean('is_featured'),
        ]);

        // Generate slug if not provided
        if (empty($this->slug) && !empty($this->title)) {
            $this->merge([
                'slug' => \Illuminate\Support\Str::slug($this->title)
            ]);
        }

        // Handle published_at for scheduled posts
        if ($this->status === 'scheduled' && empty($this->published_at)) {
            $this->merge([
                'published_at' => now()->addDay()->format('Y-m-d\TH:i')
            ]);
        } elseif ($this->status === 'published' && empty($this->published_at)) {
            $this->merge([
                'published_at' => now()->format('Y-m-d\TH:i')
            ]);
        }
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Validate published_at is required for scheduled posts
            if ($this->status === 'scheduled' && empty($this->published_at)) {
                $validator->errors()->add('published_at', 'Publish date is required for scheduled posts.');
            }

            // Validate meta title length for SEO
            if (!empty($this->meta_title) && strlen($this->meta_title) > 60) {
                $validator->warnings()->add('meta_title', 'Meta title should be 60 characters or less for optimal SEO.');
            }

            // Validate meta description length for SEO
            if (!empty($this->meta_description) && strlen($this->meta_description) > 160) {
                $validator->warnings()->add('meta_description', 'Meta description should be 160 characters or less for optimal SEO.');
            }
        });
    }
}
