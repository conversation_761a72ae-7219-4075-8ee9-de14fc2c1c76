<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class Attendance extends Model
{
    use HasFactory;

    protected $fillable = [
        'employee_id',
        'date',
        'check_in',
        'check_out',
        'break_start',
        'break_end',
        'total_hours',
        'break_duration',
        'status',
        'notes',
        'check_in_ip',
        'check_out_ip',
        'check_in_location',
        'check_out_location',
        'is_manual',
        'created_by',
    ];

    protected $casts = [
        'date' => 'date',
        'check_in' => 'datetime:H:i',
        'check_out' => 'datetime:H:i',
        'break_start' => 'datetime:H:i',
        'break_end' => 'datetime:H:i',
        'check_in_location' => 'array',
        'check_out_location' => 'array',
        'is_manual' => 'boolean',
    ];

    /**
     * Get the employee that owns the attendance.
     */
    public function employee()
    {
        return $this->belongsTo(Employee::class);
    }

    /**
     * Get the user who created this manual entry.
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Calculate total working hours automatically.
     */
    public function calculateTotalHours()
    {
        if (!$this->check_in || !$this->check_out) {
            return null;
        }

        $checkIn = Carbon::parse($this->check_in);
        $checkOut = Carbon::parse($this->check_out);
        $totalMinutes = $checkOut->diffInMinutes($checkIn);

        // Subtract break duration if available
        if ($this->break_duration) {
            $totalMinutes -= $this->break_duration;
        }

        return max(0, $totalMinutes);
    }

    /**
     * Calculate break duration automatically.
     */
    public function calculateBreakDuration()
    {
        if (!$this->break_start || !$this->break_end) {
            return null;
        }

        $breakStart = Carbon::parse($this->break_start);
        $breakEnd = Carbon::parse($this->break_end);

        return $breakEnd->diffInMinutes($breakStart);
    }

    /**
     * Get formatted total hours.
     */
    public function getFormattedTotalHoursAttribute()
    {
        if (!$this->total_hours) {
            return '0h 0m';
        }

        $hours = floor($this->total_hours / 60);
        $minutes = $this->total_hours % 60;

        return $hours . 'h ' . $minutes . 'm';
    }

    /**
     * Get formatted break duration.
     */
    public function getFormattedBreakDurationAttribute()
    {
        if (!$this->break_duration) {
            return '0m';
        }

        $hours = floor($this->break_duration / 60);
        $minutes = $this->break_duration % 60;

        if ($hours > 0) {
            return $hours . 'h ' . $minutes . 'm';
        }

        return $minutes . 'm';
    }

    /**
     * Check if employee is currently on break.
     */
    public function getIsOnBreakAttribute()
    {
        return $this->break_start && !$this->break_end;
    }

    /**
     * Check if employee is currently working.
     */
    public function getIsWorkingAttribute()
    {
        return $this->check_in && !$this->check_out && !$this->is_on_break;
    }

    /**
     * Get status color for UI.
     */
    public function getStatusColorAttribute()
    {
        return match($this->status) {
            'present' => 'green',
            'late' => 'yellow',
            'absent' => 'red',
            'half_day' => 'blue',
            'sick' => 'purple',
            'vacation' => 'indigo',
            default => 'gray',
        };
    }

    /**
     * Scope for today's attendance.
     */
    public function scopeToday($query)
    {
        return $query->whereDate('date', today());
    }

    /**
     * Scope for current month.
     */
    public function scopeCurrentMonth($query)
    {
        return $query->whereYear('date', now()->year)
                    ->whereMonth('date', now()->month);
    }

    /**
     * Scope for specific date range.
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('date', [$startDate, $endDate]);
    }

    /**
     * Boot method to handle model events.
     */
    protected static function boot()
    {
        parent::boot();

        static::saving(function ($attendance) {
            // Auto-calculate total hours and break duration
            if ($attendance->check_in && $attendance->check_out) {
                $attendance->total_hours = $attendance->calculateTotalHours();
            }

            if ($attendance->break_start && $attendance->break_end) {
                $attendance->break_duration = $attendance->calculateBreakDuration();
            }
        });
    }
}
