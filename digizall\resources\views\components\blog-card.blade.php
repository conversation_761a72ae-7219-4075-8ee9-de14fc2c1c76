@props([
    'post',
    'featured' => false,
    'showExcerpt' => true,
    'showCategory' => true,
    'showAuthor' => true,
    'showReadingTime' => true
])

<article class="group bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1" data-aos="fade-up">
    @if($featured)
        <div class="absolute top-4 left-4 z-10">
            <span class="bg-primary-600 text-white px-3 py-1 rounded-full text-xs font-semibold">
                Featured
            </span>
        </div>
    @endif

    <!-- Featured Image -->
    <div class="relative overflow-hidden">
        <a href="{{ $post->url }}" class="block">
            <img src="{{ $post->featured_image_url }}" 
                 alt="{{ $post->title }}" 
                 class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300">
        </a>
        
        <!-- Category Badge -->
        @if($showCategory && $post->category)
            <div class="absolute top-4 right-4">
                <span class="inline-block px-3 py-1 rounded-full text-xs font-semibold text-white" 
                      style="background-color: {{ $post->category->color }}">
                    {{ $post->category->name }}
                </span>
            </div>
        @endif
    </div>

    <!-- Content -->
    <div class="p-6">
        <!-- Meta Info -->
        <div class="flex items-center text-sm text-gray-500 mb-3">
            <time datetime="{{ $post->published_at->toISOString() }}">
                {{ $post->formatted_published_date }}
            </time>
            
            @if($showReadingTime && $post->reading_time)
                <span class="mx-2">•</span>
                <span>{{ $post->reading_time_text }}</span>
            @endif
            
            @if($showAuthor && $post->author)
                <span class="mx-2">•</span>
                <span>by {{ $post->author->name }}</span>
            @endif
        </div>

        <!-- Title -->
        <h3 class="text-xl font-bold text-gray-900 mb-3 group-hover:text-primary-600 transition-colors">
            <a href="{{ $post->url }}" class="hover:underline">
                {{ $post->title }}
            </a>
        </h3>

        <!-- Excerpt -->
        @if($showExcerpt)
            <p class="text-gray-600 leading-relaxed mb-4">
                {{ $post->excerpt }}
            </p>
        @endif

        <!-- Tags -->
        @if($post->tags->count() > 0)
            <div class="flex flex-wrap gap-2 mb-4">
                @foreach($post->tags->take(3) as $tag)
                    <span class="inline-block px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">
                        #{{ $tag->name }}
                    </span>
                @endforeach
            </div>
        @endif

        <!-- Read More Link -->
        <div class="flex items-center justify-between">
            <a href="{{ $post->url }}" class="inline-flex items-center text-primary-600 hover:text-primary-700 font-semibold transition-colors group">
                Read More
                <i class="fas fa-arrow-right ml-2 transform group-hover:translate-x-1 transition-transform"></i>
            </a>
            
            <!-- Views Count -->
            @if($post->views_count > 0)
                <div class="flex items-center text-sm text-gray-500">
                    <i class="fas fa-eye mr-1"></i>
                    <span>{{ number_format($post->views_count) }}</span>
                </div>
            @endif
        </div>
    </div>
</article>
