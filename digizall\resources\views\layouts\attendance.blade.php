<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>@yield('title', 'Attendance Portal') - DIGIZALL</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Styles -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])

    <style>
        .nav-link.active {
            background-color: #3b82f6;
            color: white;
        }
        .nav-link:hover {
            background-color: #e5e7eb;
        }
        .nav-link.active:hover {
            background-color: #2563eb;
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .status-working { background-color: #10b981; }
        .status-break { background-color: #f59e0b; }
        .status-offline { background-color: #6b7280; }
    </style>

    @stack('styles')
</head>
<body class="bg-gray-50">
    <div class="min-h-screen">
        <!-- Top Navigation -->
        <nav class="bg-white shadow-sm border-b border-gray-200">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-16">
                    <!-- Logo and Navigation -->
                    <div class="flex">
                        <div class="flex-shrink-0 flex items-center">
                            <h1 class="text-2xl font-bold text-primary-600">DIGIZALL</h1>
                            <span class="ml-2 text-sm text-gray-500">Attendance Portal</span>
                        </div>
                        
                        <!-- Navigation Links -->
                        <div class="hidden sm:ml-6 sm:flex sm:space-x-8">
                            <a href="{{ route('attendance.dashboard') }}" 
                               class="nav-link inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-md {{ request()->routeIs('attendance.dashboard*') ? 'active' : '' }}">
                                <i class="fas fa-tachometer-alt mr-2"></i>
                                Dashboard
                            </a>
                            
                            <a href="{{ route('attendance.history') }}" 
                               class="nav-link inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-md {{ request()->routeIs('attendance.history*') ? 'active' : '' }}">
                                <i class="fas fa-history mr-2"></i>
                                History
                            </a>

                            @if(auth()->user()->is_admin)
                                <a href="{{ route('attendance.employees.index') }}" 
                                   class="nav-link inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-md {{ request()->routeIs('attendance.employees*') ? 'active' : '' }}">
                                    <i class="fas fa-users mr-2"></i>
                                    Employees
                                </a>
                                
                                <a href="{{ route('attendance.reports') }}" 
                                   class="nav-link inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-md {{ request()->routeIs('attendance.reports*') ? 'active' : '' }}">
                                    <i class="fas fa-chart-bar mr-2"></i>
                                    Reports
                                </a>
                            @endif
                        </div>
                    </div>

                    <!-- Right Side -->
                    <div class="flex items-center space-x-4">
                        <!-- Current Time -->
                        <div class="text-sm text-gray-600" id="current-time">
                            <i class="fas fa-clock mr-1"></i>
                            <span id="time-display"></span>
                        </div>

                        <!-- Status Indicator -->
                        @php
                            $employee = \App\Models\Employee::where('user_id', auth()->id())->first();
                        @endphp
                        @if($employee)
                            <div class="flex items-center text-sm">
                                <span class="status-indicator status-{{ $employee->current_status === 'Working' ? 'working' : ($employee->current_status === 'On break' ? 'break' : 'offline') }}"></span>
                                <span class="text-gray-700">{{ $employee->current_status }}</span>
                            </div>
                        @endif

                        <!-- User Menu -->
                        <div class="relative" x-data="{ open: false }">
                            <button @click="open = !open" class="flex items-center text-sm text-gray-700 hover:text-gray-900">
                                <div class="w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center text-white font-semibold mr-2">
                                    {{ substr(auth()->user()->name, 0, 1) }}
                                </div>
                                <span>{{ auth()->user()->name }}</span>
                                <i class="fas fa-chevron-down ml-1"></i>
                            </button>

                            <div x-show="open" @click.away="open = false" 
                                 x-transition:enter="transition ease-out duration-200"
                                 x-transition:enter-start="opacity-0 transform scale-95"
                                 x-transition:enter-end="opacity-100 transform scale-100"
                                 class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50">
                                
                                @if(auth()->user()->is_admin)
                                    <a href="{{ route('admin.dashboard') }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <i class="fas fa-cog mr-2"></i>
                                        Admin Panel
                                    </a>
                                @endif
                                
                                <a href="{{ route('home') }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    <i class="fas fa-globe mr-2"></i>
                                    Main Website
                                </a>
                                
                                <div class="border-t border-gray-100"></div>
                                
                                <form method="POST" action="{{ route('logout') }}">
                                    @csrf
                                    <button type="submit" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <i class="fas fa-sign-out-alt mr-2"></i>
                                        Logout
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Page Content -->
        <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
            <!-- Flash Messages -->
            @if(session('success'))
                <div class="mb-6 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded" role="alert">
                    <div class="flex items-center">
                        <i class="fas fa-check-circle mr-2"></i>
                        <span>{{ session('success') }}</span>
                    </div>
                </div>
            @endif

            @if(session('error'))
                <div class="mb-6 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded" role="alert">
                    <div class="flex items-center">
                        <i class="fas fa-exclamation-circle mr-2"></i>
                        <span>{{ session('error') }}</span>
                    </div>
                </div>
            @endif

            @if(session('warning'))
                <div class="mb-6 bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded" role="alert">
                    <div class="flex items-center">
                        <i class="fas fa-exclamation-triangle mr-2"></i>
                        <span>{{ session('warning') }}</span>
                    </div>
                </div>
            @endif

            @yield('content')
        </main>
    </div>

    <!-- Scripts -->
    <script>
        // Update current time
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('en-US', { 
                hour12: false,
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            document.getElementById('time-display').textContent = timeString;
        }

        // Update time every second
        setInterval(updateTime, 1000);
        updateTime(); // Initial call

        // CSRF token for AJAX requests
        window.Laravel = {
            csrfToken: '{{ csrf_token() }}'
        };

        // Set up CSRF token for all AJAX requests
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });
    </script>

    @stack('scripts')
</body>
</html>
