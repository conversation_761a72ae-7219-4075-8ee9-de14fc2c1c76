@extends('layouts.app')

@section('title', $metaTags->meta_title ?? $post->meta_title ?? $post->title . ' - DIGIZALL Blog')
@section('meta_description', $metaTags->meta_description ?? $post->meta_description ?? $post->excerpt)
@section('meta_keywords', $metaTags->meta_keywords ?? $post->tags->pluck('name')->implode(', '))

@if($metaTags)
    @section('og_title', $metaTags->og_title)
    @section('og_description', $metaTags->og_description)
    @section('og_image', $metaTags->og_image_url)
@else
    @section('og_title', $post->title)
    @section('og_description', $post->excerpt)
    @section('og_image', $post->featured_image_url)
@endif

@section('content')

<!-- Article Header -->
<article class="bg-white">
    <!-- Featured Image -->
    <div class="relative h-96 lg:h-[500px] overflow-hidden">
        <img src="{{ $post->featured_image_url }}" 
             alt="{{ $post->title }}" 
             class="w-full h-full object-cover">
        <div class="absolute inset-0 bg-black bg-opacity-40"></div>
        
        <!-- Article Meta Overlay -->
        <div class="absolute bottom-0 left-0 right-0 p-8 text-white">
            <div class="container-custom">
                <!-- Breadcrumb -->
                <nav class="mb-4" data-aos="fade-up">
                    <ol class="flex items-center space-x-2 text-sm">
                        <li><a href="{{ route('home') }}" class="text-gray-200 hover:text-white">Home</a></li>
                        <li><i class="fas fa-chevron-right text-gray-400 mx-2"></i></li>
                        <li><a href="{{ route('blog.index') }}" class="text-gray-200 hover:text-white">Blog</a></li>
                        @if($post->category)
                            <li><i class="fas fa-chevron-right text-gray-400 mx-2"></i></li>
                            <li><a href="{{ route('blog.category', $post->category->slug) }}" class="text-gray-200 hover:text-white">{{ $post->category->name }}</a></li>
                        @endif
                        <li><i class="fas fa-chevron-right text-gray-400 mx-2"></i></li>
                        <li class="text-white">{{ Str::limit($post->title, 50) }}</li>
                    </ol>
                </nav>

                <!-- Category Badge -->
                @if($post->category)
                    <div class="mb-4" data-aos="fade-up" data-aos-delay="100">
                        <span class="inline-block px-3 py-1 rounded-full text-xs font-semibold text-white" 
                              style="background-color: {{ $post->category->color }}">
                            {{ $post->category->name }}
                        </span>
                    </div>
                @endif

                <!-- Title -->
                <h1 class="heading-primary text-white mb-4" data-aos="fade-up" data-aos-delay="200">
                    {{ $post->title }}
                </h1>

                <!-- Meta Information -->
                <div class="flex flex-wrap items-center gap-6 text-sm text-gray-200" data-aos="fade-up" data-aos-delay="300">
                    <div class="flex items-center">
                        <img src="{{ $post->author->avatar_url }}" 
                             alt="{{ $post->author->name }}" 
                             class="w-8 h-8 rounded-full mr-2">
                        <span>{{ $post->author->name }}</span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-calendar mr-2"></i>
                        <time datetime="{{ $post->published_at->toISOString() }}">
                            {{ $post->formatted_published_date }}
                        </time>
                    </div>
                    @if($post->reading_time)
                        <div class="flex items-center">
                            <i class="fas fa-clock mr-2"></i>
                            <span>{{ $post->reading_time_text }}</span>
                        </div>
                    @endif
                    <div class="flex items-center">
                        <i class="fas fa-eye mr-2"></i>
                        <span>{{ number_format($post->views_count) }} views</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Article Content -->
    <div class="section-padding">
        <div class="container-custom">
            <div class="grid grid-cols-1 lg:grid-cols-4 gap-12">
                <!-- Main Content -->
                <div class="lg:col-span-3">
                    <!-- Article Excerpt -->
                    <div class="mb-8" data-aos="fade-up">
                        <p class="text-xl text-gray-600 leading-relaxed font-medium">
                            {{ $post->excerpt }}
                        </p>
                    </div>

                    <!-- Article Body -->
                    <div class="prose prose-lg max-w-none mb-12" data-aos="fade-up" data-aos-delay="100">
                        {!! nl2br(e($post->content)) !!}
                    </div>

                    <!-- Tags -->
                    @if($post->tags->count() > 0)
                        <div class="mb-8" data-aos="fade-up" data-aos-delay="200">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Tags</h3>
                            <div class="flex flex-wrap gap-2">
                                @foreach($post->tags as $tag)
                                    <a href="{{ route('blog.tag', $tag->slug) }}" 
                                       class="inline-block px-3 py-1 bg-gray-100 text-gray-600 rounded-full text-sm hover:bg-primary-100 hover:text-primary-600 transition-colors">
                                        #{{ $tag->name }}
                                    </a>
                                @endforeach
                            </div>
                        </div>
                    @endif

                    <!-- Share Buttons -->
                    <div class="border-t border-gray-200 pt-8 mb-12" data-aos="fade-up" data-aos-delay="300">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Share this article</h3>
                        <div class="flex space-x-4">
                            <a href="https://www.facebook.com/sharer/sharer.php?u={{ urlencode(url()->current()) }}" 
                               target="_blank" 
                               class="flex items-center justify-center w-10 h-10 bg-blue-600 text-white rounded-full hover:bg-blue-700 transition-colors">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                            <a href="https://twitter.com/intent/tweet?url={{ urlencode(url()->current()) }}&text={{ urlencode($post->title) }}" 
                               target="_blank" 
                               class="flex items-center justify-center w-10 h-10 bg-blue-400 text-white rounded-full hover:bg-blue-500 transition-colors">
                                <i class="fab fa-twitter"></i>
                            </a>
                            <a href="https://www.linkedin.com/sharing/share-offsite/?url={{ urlencode(url()->current()) }}" 
                               target="_blank" 
                               class="flex items-center justify-center w-10 h-10 bg-blue-700 text-white rounded-full hover:bg-blue-800 transition-colors">
                                <i class="fab fa-linkedin-in"></i>
                            </a>
                            <button onclick="copyToClipboard('{{ url()->current() }}')" 
                                    class="flex items-center justify-center w-10 h-10 bg-gray-600 text-white rounded-full hover:bg-gray-700 transition-colors">
                                <i class="fas fa-link"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Author Bio -->
                    <div class="bg-gray-50 rounded-xl p-8 mb-12" data-aos="fade-up" data-aos-delay="400">
                        <div class="flex items-start space-x-6">
                            <img src="{{ $post->author->avatar_url }}" 
                                 alt="{{ $post->author->name }}" 
                                 class="w-20 h-20 rounded-full">
                            <div class="flex-1">
                                <h3 class="text-xl font-bold text-gray-900 mb-2">{{ $post->author->name }}</h3>
                                @if($post->author->position)
                                    <p class="text-primary-600 font-medium mb-3">{{ $post->author->position }}</p>
                                @endif
                                <p class="text-gray-600">
                                    {{ $post->author->name }} is a skilled professional at DIGIZALL, contributing valuable insights and expertise to our blog.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sidebar -->
                <div class="lg:col-span-1">
                    <!-- Table of Contents (if needed) -->
                    <div class="bg-white rounded-xl shadow-lg p-6 mb-8 sticky top-8" data-aos="fade-up">
                        <h3 class="text-lg font-bold text-gray-900 mb-4">Quick Actions</h3>
                        <div class="space-y-3">
                            <a href="{{ route('blog.index') }}" class="block text-gray-600 hover:text-primary-600 transition-colors">
                                <i class="fas fa-arrow-left mr-2"></i>
                                Back to Blog
                            </a>
                            @if($post->category)
                                <a href="{{ route('blog.category', $post->category->slug) }}" class="block text-gray-600 hover:text-primary-600 transition-colors">
                                    <i class="fas fa-folder mr-2"></i>
                                    More in {{ $post->category->name }}
                                </a>
                            @endif
                            <a href="{{ route('contact') }}" class="block text-gray-600 hover:text-primary-600 transition-colors">
                                <i class="fas fa-envelope mr-2"></i>
                                Contact Us
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</article>

<!-- Related Posts -->
@if($relatedPosts->count() > 0)
    <section class="section-padding bg-gray-50">
        <div class="container-custom">
            <div class="text-center mb-16" data-aos="fade-up">
                <h2 class="heading-secondary mb-4">Related Articles</h2>
                <p class="text-body">
                    Discover more insights and tips from our blog.
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                @foreach($relatedPosts as $relatedPost)
                    <div data-aos="fade-up" data-aos-delay="{{ $loop->index * 100 }}">
                        <x-blog-card :post="$relatedPost" />
                    </div>
                @endforeach
            </div>
        </div>
    </section>
@endif

<!-- CTA Section -->
<section class="section-padding bg-gradient-to-r from-primary-600 to-secondary-600 text-white">
    <div class="container-custom">
        <div class="text-center" data-aos="fade-up">
            <h2 class="heading-secondary text-white mb-6">
                Ready to Transform Your Business?
            </h2>
            <p class="text-xl text-gray-200 mb-8 max-w-2xl mx-auto">
                Let's discuss how our digital solutions can help you achieve your goals.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="{{ route('quote') }}" class="btn-primary bg-white text-primary-600 hover:bg-gray-100 px-8 py-4 text-lg font-semibold">
                    Get Free Quote
                    <i class="fas fa-arrow-right ml-2"></i>
                </a>
                <a href="{{ route('contact') }}" class="btn-secondary border-2 border-white text-white hover:bg-white hover:text-primary-600 px-8 py-4 text-lg font-semibold">
                    Contact Us
                </a>
            </div>
        </div>
    </div>
</section>

@endsection

@push('scripts')
<script>
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // Show success message
        const button = event.target.closest('button');
        const originalIcon = button.innerHTML;
        button.innerHTML = '<i class="fas fa-check"></i>';
        button.classList.add('bg-green-600');
        button.classList.remove('bg-gray-600');

        setTimeout(() => {
            button.innerHTML = originalIcon;
            button.classList.remove('bg-green-600');
            button.classList.add('bg-gray-600');
        }, 2000);
    });
}
</script>
@endpush
