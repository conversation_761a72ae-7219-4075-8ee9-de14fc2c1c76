<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Services\PerformanceMonitoringService;
use App\Services\DatabaseOptimizationService;
use App\Services\CacheService;
use Illuminate\Support\Facades\Artisan;

class PerformanceController extends Controller
{
    public function __construct()
    {
        $this->middleware('admin');
    }

    /**
     * Display performance dashboard
     */
    public function index()
    {
        $report = PerformanceMonitoringService::generateReport();
        $databaseHealth = DatabaseOptimizationService::getHealthScore();
        $cacheStats = CacheService::getStats();

        return view('admin.performance.index', compact('report', 'databaseHealth', 'cacheStats'));
    }

    /**
     * Run performance optimization
     */
    public function optimize(Request $request)
    {
        $type = $request->get('type', 'all');

        try {
            switch ($type) {
                case 'cache':
                    Artisan::call('performance:optimize', ['--cache' => true]);
                    break;
                case 'database':
                    Artisan::call('performance:optimize', ['--database' => true]);
                    break;
                case 'images':
                    Artisan::call('performance:optimize', ['--images' => true]);
                    break;
                default:
                    Artisan::call('performance:optimize', ['--all' => true]);
            }

            return response()->json([
                'success' => true,
                'message' => 'Performance optimization completed successfully',
                'output' => Artisan::output()
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Optimization failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get real-time performance metrics
     */
    public function metrics()
    {
        $metrics = [
            'recent_requests' => PerformanceMonitoringService::getRecentMetrics(10),
            'daily_summary' => PerformanceMonitoringService::getDailySummary(),
            'system_resources' => PerformanceMonitoringService::getSystemResources(),
            'database_health' => DatabaseOptimizationService::getHealthScore(),
        ];

        return response()->json($metrics);
    }

    /**
     * Get performance trends
     */
    public function trends()
    {
        $trends = PerformanceMonitoringService::getPerformanceTrends();

        return response()->json($trends);
    }

    /**
     * Get slow queries
     */
    public function slowQueries()
    {
        $slowQueries = PerformanceMonitoringService::getSlowQueriesSummary();

        return response()->json($slowQueries);
    }

    /**
     * Clear performance data
     */
    public function clearData()
    {
        try {
            PerformanceMonitoringService::cleanup();

            return response()->json([
                'success' => true,
                'message' => 'Performance data cleared successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to clear data: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Export performance report
     */
    public function export()
    {
        $report = PerformanceMonitoringService::generateReport();

        $filename = 'performance-report-' . now()->format('Y-m-d-H-i-s') . '.json';

        return response()->json($report)
                        ->header('Content-Disposition', 'attachment; filename="' . $filename . '"');
    }
}
