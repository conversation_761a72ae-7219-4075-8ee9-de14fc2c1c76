@props([
    'featuredPosts' => [],
    'categories' => [],
    'tags' => [],
    'showSearch' => true,
    'showFeatured' => true,
    'showCategories' => true,
    'showTags' => true,
    'showNewsletter' => true
])

<div class="space-y-8">
    <!-- Search Widget -->
    @if($showSearch)
        <div class="bg-white rounded-xl shadow-lg p-6" data-aos="fade-up">
            <h3 class="text-xl font-bold text-gray-900 mb-4">Search Articles</h3>
            <form method="GET" action="{{ route('blog.index') }}" class="relative">
                <input type="text" 
                       name="search" 
                       value="{{ request('search') }}"
                       placeholder="Search articles..." 
                       class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <i class="fas fa-search text-gray-400"></i>
                </div>
                <button type="submit" class="absolute inset-y-0 right-0 pr-3 flex items-center">
                    <span class="bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700 transition-colors text-sm">
                        Search
                    </span>
                </button>
            </form>
        </div>
    @endif

    <!-- Featured Posts -->
    @if($showFeatured && count($featuredPosts) > 0)
        <div class="bg-white rounded-xl shadow-lg p-6" data-aos="fade-up">
            <h3 class="text-xl font-bold text-gray-900 mb-6">Featured Posts</h3>
            <div class="space-y-6">
                @foreach($featuredPosts as $featuredPost)
                    <article class="flex space-x-4">
                        <div class="flex-shrink-0">
                            <img src="{{ $featuredPost->featured_image_url }}" 
                                 alt="{{ $featuredPost->title }}" 
                                 class="w-16 h-16 object-cover rounded-lg">
                        </div>
                        <div class="flex-1 min-w-0">
                            <h4 class="text-sm font-semibold text-gray-900 line-clamp-2 mb-1">
                                <a href="{{ $featuredPost->url }}" class="hover:text-primary-600 transition-colors">
                                    {{ $featuredPost->title }}
                                </a>
                            </h4>
                            <div class="flex items-center text-xs text-gray-500 space-x-2">
                                <time datetime="{{ $featuredPost->published_at->toISOString() }}">
                                    {{ $featuredPost->formatted_published_date }}
                                </time>
                                @if($featuredPost->reading_time)
                                    <span>•</span>
                                    <span>{{ $featuredPost->reading_time_text }}</span>
                                @endif
                            </div>
                        </div>
                    </article>
                @endforeach
            </div>
        </div>
    @endif

    <!-- Categories -->
    @if($showCategories && count($categories) > 0)
        <div class="bg-white rounded-xl shadow-lg p-6" data-aos="fade-up" data-aos-delay="100">
            <h3 class="text-xl font-bold text-gray-900 mb-6">Categories</h3>
            <div class="space-y-3">
                @foreach($categories as $category)
                    <a href="{{ route('blog.category', $category->slug) }}" 
                       class="flex items-center justify-between text-gray-600 hover:text-primary-600 transition-colors group">
                        <div class="flex items-center">
                            <div class="w-3 h-3 rounded-full mr-3" style="background-color: {{ $category->color }}"></div>
                            <span>{{ $category->name }}</span>
                        </div>
                        <span class="bg-gray-100 group-hover:bg-primary-100 text-gray-600 group-hover:text-primary-600 px-2 py-1 rounded-full text-xs transition-colors">
                            {{ $category->published_posts_count ?? $category->posts_count ?? 0 }}
                        </span>
                    </a>
                @endforeach
            </div>
            <div class="mt-4 pt-4 border-t border-gray-200">
                <a href="{{ route('blog.index') }}" class="text-primary-600 hover:text-primary-700 font-medium text-sm">
                    View All Categories →
                </a>
            </div>
        </div>
    @endif

    <!-- Popular Tags -->
    @if($showTags && count($tags) > 0)
        <div class="bg-white rounded-xl shadow-lg p-6" data-aos="fade-up" data-aos-delay="200">
            <h3 class="text-xl font-bold text-gray-900 mb-6">Popular Tags</h3>
            <div class="flex flex-wrap gap-2">
                @foreach($tags as $tag)
                    <a href="{{ route('blog.tag', $tag->slug) }}" 
                       class="inline-block px-3 py-1 bg-gray-100 text-gray-600 rounded-full text-sm hover:bg-primary-100 hover:text-primary-600 transition-colors">
                        #{{ $tag->name }}
                        @if(isset($tag->published_posts_count) || isset($tag->posts_count))
                            <span class="ml-1 text-xs opacity-75">
                                ({{ $tag->published_posts_count ?? $tag->posts_count ?? 0 }})
                            </span>
                        @endif
                    </a>
                @endforeach
            </div>
        </div>
    @endif

    <!-- Newsletter Signup -->
    @if($showNewsletter)
        <div class="bg-gradient-to-br from-primary-600 to-secondary-600 rounded-xl p-6 text-white" data-aos="fade-up" data-aos-delay="300">
            <h3 class="text-xl font-bold mb-4">Stay Updated</h3>
            <p class="text-primary-100 mb-6 text-sm">
                Subscribe to our newsletter for the latest articles and insights delivered to your inbox.
            </p>
            <form class="space-y-3">
                <input type="email" 
                       placeholder="Enter your email" 
                       class="w-full px-4 py-3 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-white">
                <button type="submit" 
                        class="w-full bg-white text-primary-600 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                    Subscribe Now
                </button>
            </form>
            <p class="text-xs text-primary-200 mt-3">
                No spam, unsubscribe at any time.
            </p>
        </div>
    @endif

    <!-- Recent Comments (if needed) -->
    @if(isset($recentComments) && count($recentComments) > 0)
        <div class="bg-white rounded-xl shadow-lg p-6" data-aos="fade-up" data-aos-delay="400">
            <h3 class="text-xl font-bold text-gray-900 mb-6">Recent Comments</h3>
            <div class="space-y-4">
                @foreach($recentComments as $comment)
                    <div class="flex space-x-3">
                        <img src="{{ $comment->author_avatar }}" 
                             alt="{{ $comment->author_name }}" 
                             class="w-8 h-8 rounded-full">
                        <div class="flex-1 min-w-0">
                            <p class="text-sm text-gray-600 line-clamp-2">{{ $comment->content }}</p>
                            <div class="flex items-center text-xs text-gray-500 mt-1">
                                <span class="font-medium">{{ $comment->author_name }}</span>
                                <span class="mx-1">on</span>
                                <a href="{{ $comment->post_url }}" class="text-primary-600 hover:text-primary-700">
                                    {{ Str::limit($comment->post_title, 30) }}
                                </a>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    @endif

    <!-- Archive (if needed) -->
    @if(isset($archives) && count($archives) > 0)
        <div class="bg-white rounded-xl shadow-lg p-6" data-aos="fade-up" data-aos-delay="500">
            <h3 class="text-xl font-bold text-gray-900 mb-6">Archive</h3>
            <div class="space-y-2">
                @foreach($archives as $archive)
                    <a href="{{ $archive->url }}" 
                       class="flex items-center justify-between text-gray-600 hover:text-primary-600 transition-colors">
                        <span>{{ $archive->month_year }}</span>
                        <span class="text-xs text-gray-500">({{ $archive->posts_count }})</span>
                    </a>
                @endforeach
            </div>
        </div>
    @endif
</div>
