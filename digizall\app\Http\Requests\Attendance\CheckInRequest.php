<?php

namespace App\Http\Requests\Attendance;

use Illuminate\Foundation\Http\FormRequest;

class CheckInRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check() && (auth()->user()->isEmployee() || auth()->user()->isHR() || auth()->user()->isAdmin());
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'location' => 'nullable|string|max:255',
            'notes' => 'nullable|string|max:500',
            'latitude' => 'nullable|numeric|between:-90,90',
            'longitude' => 'nullable|numeric|between:-180,180',
            'ip_address' => 'nullable|ip',
            'user_agent' => 'nullable|string|max:500',
            'work_from_home' => 'boolean',
            'project_id' => 'nullable|exists:projects,id',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'location' => 'check-in location',
            'notes' => 'check-in notes',
            'latitude' => 'latitude coordinate',
            'longitude' => 'longitude coordinate',
            'ip_address' => 'IP address',
            'user_agent' => 'browser information',
            'work_from_home' => 'work from home status',
            'project_id' => 'project',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'location.max' => 'Location may not be greater than 255 characters.',
            'notes.max' => 'Notes may not be greater than 500 characters.',
            'latitude.numeric' => 'Latitude must be a valid number.',
            'latitude.between' => 'Latitude must be between -90 and 90.',
            'longitude.numeric' => 'Longitude must be a valid number.',
            'longitude.between' => 'Longitude must be between -180 and 180.',
            'ip_address.ip' => 'IP address must be a valid IP address.',
            'user_agent.max' => 'Browser information may not be greater than 500 characters.',
            'project_id.exists' => 'The selected project is invalid.',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Auto-fill IP address and user agent if not provided
        $this->merge([
            'ip_address' => $this->ip_address ?? request()->ip(),
            'user_agent' => $this->user_agent ?? request()->userAgent(),
            'work_from_home' => $this->boolean('work_from_home'),
        ]);

        // Set default location if working from home
        if ($this->work_from_home && empty($this->location)) {
            $this->merge([
                'location' => 'Work From Home'
            ]);
        }
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            $user = auth()->user();

            // Check if user already checked in today
            $existingCheckIn = \App\Models\AttendanceLog::where('user_id', $user->id)
                ->whereDate('check_in_time', today())
                ->whereNull('check_out_time')
                ->first();

            if ($existingCheckIn) {
                $validator->errors()->add('check_in', 'You have already checked in today at ' . $existingCheckIn->check_in_time->format('g:i A'));
            }

            // Validate location coordinates if provided
            if (($this->latitude && !$this->longitude) || (!$this->latitude && $this->longitude)) {
                $validator->errors()->add('coordinates', 'Both latitude and longitude are required when providing location coordinates.');
            }

            // Check if check-in is within allowed time range (if configured)
            $currentTime = now()->format('H:i');
            $allowedStartTime = config('attendance.allowed_check_in_start', '06:00');
            $allowedEndTime = config('attendance.allowed_check_in_end', '12:00');

            if ($currentTime < $allowedStartTime || $currentTime > $allowedEndTime) {
                $validator->warnings()->add('time', "Check-in is outside normal hours ({$allowedStartTime} - {$allowedEndTime}). Please add a note explaining the reason.");
            }

            // Require notes for late check-ins
            $standardStartTime = config('attendance.standard_start_time', '09:00');
            if ($currentTime > $standardStartTime && empty($this->notes)) {
                $validator->warnings()->add('notes', 'Please provide a note for late check-in.');
            }
        });
    }
}
