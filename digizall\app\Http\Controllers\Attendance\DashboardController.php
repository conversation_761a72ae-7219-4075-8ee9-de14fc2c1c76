<?php

namespace App\Http\Controllers\Attendance;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Employee;
use App\Models\Attendance;
use App\Models\Department;
use Carbon\Carbon;

class DashboardController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function index()
    {
        $user = auth()->user();
        $employee = Employee::where('user_id', $user->id)->first();

        // If user is not an employee, redirect to admin view
        if (!$employee) {
            return $this->adminDashboard();
        }

        return $this->employeeDashboard($employee);
    }

    private function employeeDashboard($employee)
    {
        // Get today's attendance
        $todayAttendance = $employee->todayAttendance;

        // Get current month statistics
        $currentMonth = now();
        $monthlyAttendances = $employee->attendances()
            ->whereYear('date', $currentMonth->year)
            ->whereMonth('date', $currentMonth->month)
            ->get();

        $stats = [
            'total_days' => $currentMonth->daysInMonth,
            'working_days' => $this->getWorkingDaysInMonth($currentMonth),
            'present_days' => $monthlyAttendances->where('status', 'present')->count(),
            'absent_days' => $monthlyAttendances->where('status', 'absent')->count(),
            'late_days' => $monthlyAttendances->where('status', 'late')->count(),
            'total_hours' => $monthlyAttendances->sum('total_hours'),
        ];

        // Get recent attendance records
        $recentAttendances = $employee->attendances()
            ->orderBy('date', 'desc')
            ->limit(7)
            ->get();

        // Get upcoming holidays/events (placeholder)
        $upcomingEvents = [];

        return view('attendance.employee.dashboard', compact(
            'employee',
            'todayAttendance',
            'stats',
            'recentAttendances',
            'upcomingEvents'
        ));
    }

    private function adminDashboard()
    {
        // Check if user has admin privileges
        if (!auth()->user()->is_admin) {
            abort(403, 'Access denied. You need to be registered as an employee or admin.');
        }

        // Get overall statistics
        $totalEmployees = Employee::active()->count();
        $totalDepartments = Department::active()->count();

        // Today's attendance summary
        $today = today();
        $todayAttendances = Attendance::whereDate('date', $today)->get();

        $todayStats = [
            'total_employees' => $totalEmployees,
            'checked_in' => $todayAttendances->whereNotNull('check_in')->count(),
            'checked_out' => $todayAttendances->whereNotNull('check_out')->count(),
            'on_break' => $todayAttendances->where('break_start', '!=', null)
                                         ->where('break_end', null)->count(),
            'absent' => $totalEmployees - $todayAttendances->count(),
        ];

        // Department-wise attendance
        $departmentStats = Department::active()
            ->withCount(['activeEmployees'])
            ->get()
            ->map(function ($department) use ($today) {
                $presentToday = Attendance::whereDate('date', $today)
                    ->whereHas('employee', function ($query) use ($department) {
                        $query->where('department_id', $department->id);
                    })
                    ->count();

                return [
                    'name' => $department->name,
                    'total_employees' => $department->active_employees_count,
                    'present_today' => $presentToday,
                    'absent_today' => $department->active_employees_count - $presentToday,
                ];
            });

        // Recent activities
        $recentActivities = Attendance::with(['employee.user'])
            ->whereDate('date', $today)
            ->orderBy('updated_at', 'desc')
            ->limit(10)
            ->get();

        return view('attendance.admin.dashboard', compact(
            'todayStats',
            'departmentStats',
            'recentActivities'
        ));
    }

    private function getWorkingDaysInMonth($date)
    {
        $startOfMonth = $date->copy()->startOfMonth();
        $endOfMonth = $date->copy()->endOfMonth();
        $workingDays = 0;

        for ($day = $startOfMonth; $day->lte($endOfMonth); $day->addDay()) {
            // Exclude weekends (Saturday = 6, Sunday = 0)
            if (!in_array($day->dayOfWeek, [0, 6])) {
                $workingDays++;
            }
        }

        return $workingDays;
    }
}
