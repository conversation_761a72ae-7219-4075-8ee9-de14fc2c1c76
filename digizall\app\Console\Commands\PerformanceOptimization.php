<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\CacheService;
use App\Services\DatabaseOptimizationService;
use App\Services\ImageOptimizationService;
use App\Services\PerformanceMonitoringService;
use Illuminate\Support\Facades\Artisan;

class PerformanceOptimization extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'performance:optimize
                            {--cache : Only optimize caches}
                            {--database : Only optimize database}
                            {--images : Only optimize images}
                            {--all : Run all optimizations (default)}
                            {--report : Generate performance report}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Comprehensive performance optimization for the application';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🚀 Starting Performance Optimization...');
        $startTime = microtime(true);

        $options = $this->options();
        $runAll = $options['all'] || (!$options['cache'] && !$options['database'] && !$options['images'] && !$options['report']);

        if ($options['report']) {
            $this->generatePerformanceReport();
            return 0;
        }

        if ($options['cache'] || $runAll) {
            $this->optimizeCaches();
        }

        if ($options['database'] || $runAll) {
            $this->optimizeDatabase();
        }

        if ($options['images'] || $runAll) {
            $this->optimizeImages();
        }

        if ($runAll) {
            $this->runSystemOptimizations();
        }

        $duration = round((microtime(true) - $startTime) * 1000, 2);
        $this->info("✅ Performance optimization completed in {$duration}ms");

        return 0;
    }

    /**
     * Optimize application caches
     */
    private function optimizeCaches()
    {
        $this->info('🔄 Optimizing Caches...');

        // Clear all caches
        $this->line('  • Clearing existing caches...');
        Artisan::call('cache:clear');
        Artisan::call('config:clear');
        Artisan::call('route:clear');
        Artisan::call('view:clear');

        // Rebuild optimized caches
        $this->line('  • Building optimized caches...');
        Artisan::call('config:cache');
        Artisan::call('route:cache');
        Artisan::call('view:cache');
        Artisan::call('optimize');

        // Warm up application caches
        $this->line('  • Warming up application caches...');
        CacheService::warmUp();

        $this->info('✅ Cache optimization completed');
    }

    /**
     * Optimize database
     */
    private function optimizeDatabase()
    {
        $this->info('🗄️  Optimizing Database...');

        // Run database optimizations
        $this->line('  • Optimizing database tables...');
        $optimizedTables = DatabaseOptimizationService::optimizeTables();
        $this->line("    Optimized {$optimizedTables} tables");

        $this->line('  • Analyzing database tables...');
        $analyzedTables = DatabaseOptimizationService::analyzeTables();
        $this->line("    Analyzed {$analyzedTables} tables");

        // Clean up old data
        $this->line('  • Cleaning up old data...');
        $cleanedRecords = DatabaseOptimizationService::cleanupOldData();
        $this->line("    Cleaned {$cleanedRecords} old records");

        // Check for optimization recommendations
        $this->line('  • Checking for optimization opportunities...');
        $recommendations = DatabaseOptimizationService::checkMissingIndexes();

        if (count($recommendations) > 0) {
            $this->warn("    Found " . count($recommendations) . " optimization recommendations:");
            foreach (array_slice($recommendations, 0, 5) as $rec) {
                $this->line("      - {$rec['table']}.{$rec['column']}: {$rec['reason']}");
            }
        } else {
            $this->line("    No optimization recommendations found");
        }

        // Get database health score
        $health = DatabaseOptimizationService::getHealthScore();
        $this->line("  • Database health score: {$health['score']}/100 (Grade: {$health['grade']})");

        $this->info('✅ Database optimization completed');
    }

    /**
     * Optimize images
     */
    private function optimizeImages()
    {
        $this->info('🖼️  Optimizing Images...');

        // Compress existing images
        $this->line('  • Compressing existing images...');
        $result = ImageOptimizationService::compressExistingImages();

        $this->line("    Compressed {$result['compressed_count']} images");
        $this->line("    Total savings: {$result['total_savings_mb']} MB");

        // Clean up unused image sizes
        $this->line('  • Cleaning up unused image variants...');
        $deleted = ImageOptimizationService::cleanupUnusedSizes();
        $this->line("    Deleted {$deleted} unused image files");

        $this->info('✅ Image optimization completed');
    }

    /**
     * Run system-level optimizations
     */
    private function runSystemOptimizations()
    {
        $this->info('⚙️  Running System Optimizations...');

        // Optimize autoloader
        $this->line('  • Optimizing Composer autoloader...');
        exec('composer dump-autoload --optimize --no-dev --quiet 2>&1', $output, $returnCode);

        if ($returnCode === 0) {
            $this->line('    Autoloader optimized successfully');
        } else {
            $this->warn('    Autoloader optimization failed');
        }

        // Clear expired cache entries
        $this->line('  • Clearing expired cache entries...');
        CacheService::clearAll();

        // Clean up performance monitoring data
        $this->line('  • Cleaning up old performance data...');
        PerformanceMonitoringService::cleanup();

        $this->info('✅ System optimization completed');
    }

    /**
     * Generate performance report
     */
    private function generatePerformanceReport()
    {
        $this->info('📊 Generating Performance Report...');

        $report = PerformanceMonitoringService::generateReport();

        $this->table(['Metric', 'Value'], [
            ['Today\'s Requests', $report['summary']['request_count'] ?? 'N/A'],
            ['Average Response Time', ($report['summary']['avg_duration_ms'] ?? 'N/A') . ' ms'],
            ['Average Memory Usage', ($report['summary']['avg_memory_mb'] ?? 'N/A') . ' MB'],
            ['Cache Hit Rate', ($report['summary']['cache_hit_rate'] ?? 'N/A') . '%'],
            ['Slow Requests', $report['summary']['slow_requests'] ?? 'N/A'],
        ]);

        // Database health
        $health = DatabaseOptimizationService::getHealthScore();
        $this->line("\n📈 Database Health Score: {$health['score']}/100 (Grade: {$health['grade']})");

        if (!empty($health['issues'])) {
            $this->warn("\n⚠️  Issues Found:");
            foreach ($health['issues'] as $issue) {
                $this->line("  • {$issue}");
            }
        }

        // System resources
        $resources = $report['system_resources'];
        $this->line("\n💾 System Resources:");
        $this->line("  • Memory: {$resources['memory_usage']['current_mb']} MB / {$resources['memory_usage']['limit_mb']} MB");
        $this->line("  • Disk: {$resources['disk_usage']['used_percentage']}% used ({$resources['disk_usage']['free_gb']} GB free)");

        // Recent slow queries
        if (!empty($report['slow_queries'])) {
            $this->warn("\n🐌 Recent Slow Queries:");
            foreach (array_slice($report['slow_queries'], 0, 3) as $query) {
                $this->line("  • {$query['duration_ms']}ms: " . \Str::limit($query['sql'], 60));
            }
        }

        $this->info("\n✅ Performance report generated");
    }
}
