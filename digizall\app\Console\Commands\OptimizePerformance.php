<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;

class OptimizePerformance extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:optimize-performance';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Optimize application performance by running various optimization commands';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting performance optimization...');

        // Clear all caches
        $this->info('Clearing caches...');
        Artisan::call('cache:clear');
        Artisan::call('config:clear');
        Artisan::call('route:clear');
        Artisan::call('view:clear');

        // Optimize for production
        $this->info('Optimizing for production...');
        Artisan::call('config:cache');
        Artisan::call('route:cache');
        Artisan::call('view:cache');

        // Optimize autoloader
        $this->info('Optimizing autoloader...');
        Artisan::call('optimize');

        // Queue optimization
        $this->info('Optimizing queue...');
        Artisan::call('queue:restart');

        $this->info('Performance optimization completed successfully!');

        return 0;
    }
}
