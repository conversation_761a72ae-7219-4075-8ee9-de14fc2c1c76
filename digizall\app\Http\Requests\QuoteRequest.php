<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class QuoteRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'nullable|string|max:20',
            'company' => 'nullable|string|max:255',
            'website' => 'nullable|url|max:255',
            'service' => 'required|string|max:255',
            'budget' => 'required|string|max:50',
            'timeline' => 'required|string|max:50',
            'project_type' => 'required|string|max:100',
            'description' => 'required|string|max:2000',
            'features' => 'nullable|array',
            'features.*' => 'string|max:255',
            'additional_info' => 'nullable|string|max:1000',
            'preferred_contact' => 'required|string|in:email,phone,whatsapp',
            'marketing_consent' => 'boolean',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'name.required' => 'Please enter your full name.',
            'email.required' => 'Please enter your email address.',
            'email.email' => 'Please enter a valid email address.',
            'service.required' => 'Please select a service.',
            'budget.required' => 'Please select your budget range.',
            'timeline.required' => 'Please select your preferred timeline.',
            'project_type.required' => 'Please select your project type.',
            'description.required' => 'Please provide a description of your project.',
            'description.max' => 'Project description cannot exceed 2000 characters.',
            'preferred_contact.required' => 'Please select your preferred contact method.',
            'preferred_contact.in' => 'Please select a valid contact method.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'name' => 'full name',
            'email' => 'email address',
            'phone' => 'phone number',
            'company' => 'company name',
            'website' => 'website URL',
            'service' => 'service type',
            'budget' => 'budget range',
            'timeline' => 'project timeline',
            'project_type' => 'project type',
            'description' => 'project description',
            'features' => 'required features',
            'additional_info' => 'additional information',
            'preferred_contact' => 'preferred contact method',
        ];
    }
}
