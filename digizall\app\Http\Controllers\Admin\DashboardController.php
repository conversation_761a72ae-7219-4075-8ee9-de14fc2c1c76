<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Service;
use App\Models\BlogPost;
use App\Models\QuoteRequest;
use App\Models\ContactMessage;
use App\Models\Testimonial;
use Carbon\Carbon;

class DashboardController extends Controller
{
    public function __construct()
    {
        $this->middleware('admin');
    }

    public function index()
    {
        // Get dashboard statistics
        $stats = [
            'total_users' => User::count(),
            'total_services' => Service::count(),
            'total_blog_posts' => BlogPost::count(),
            'total_quotes' => QuoteRequest::count(),
            'total_contacts' => ContactMessage::count(),
            'total_testimonials' => Testimonial::count(),
        ];

        // Recent activity
        $recentQuotes = QuoteRequest::with(['user'])
            ->latest()
            ->limit(5)
            ->get();

        $recentContacts = ContactMessage::latest()
            ->limit(5)
            ->get();

        $recentBlogPosts = BlogPost::with(['author', 'category'])
            ->latest()
            ->limit(5)
            ->get();

        // Monthly statistics
        $monthlyQuotes = QuoteRequest::selectRaw('MONTH(created_at) as month, COUNT(*) as count')
            ->whereYear('created_at', Carbon::now()->year)
            ->groupBy('month')
            ->orderBy('month')
            ->get()
            ->pluck('count', 'month')
            ->toArray();

        $monthlyContacts = ContactMessage::selectRaw('MONTH(created_at) as month, COUNT(*) as count')
            ->whereYear('created_at', Carbon::now()->year)
            ->groupBy('month')
            ->orderBy('month')
            ->get()
            ->pluck('count', 'month')
            ->toArray();

        // Fill missing months with 0
        for ($i = 1; $i <= 12; $i++) {
            if (!isset($monthlyQuotes[$i])) {
                $monthlyQuotes[$i] = 0;
            }
            if (!isset($monthlyContacts[$i])) {
                $monthlyContacts[$i] = 0;
            }
        }

        ksort($monthlyQuotes);
        ksort($monthlyContacts);

        // Pending items that need attention
        $pendingQuotes = QuoteRequest::where('status', 'pending')->count();
        $pendingContacts = ContactMessage::where('status', 'pending')->count();

        return view('admin.dashboard', compact(
            'stats',
            'recentQuotes',
            'recentContacts',
            'recentBlogPosts',
            'monthlyQuotes',
            'monthlyContacts',
            'pendingQuotes',
            'pendingContacts'
        ));
    }
}
