<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\BlogPost;
use App\Models\BlogCategory;
use App\Models\BlogTag;
use App\Models\MetaTag;

class BlogController extends Controller
{
    /**
     * Display a listing of blog posts.
     */
    public function index(Request $request)
    {
        $query = BlogPost::published()
            ->with(['category', 'author', 'tags'])
            ->orderBy('published_at', 'desc');

        // Search functionality
        if ($request->has('search') && $request->search) {
            $searchTerm = $request->search;
            $query->where(function($q) use ($searchTerm) {
                $q->where('title', 'like', "%{$searchTerm}%")
                  ->orWhere('excerpt', 'like', "%{$searchTerm}%")
                  ->orWhere('content', 'like', "%{$searchTerm}%");
            });
        }

        // Category filter
        if ($request->has('category') && $request->category) {
            $query->whereHas('category', function($q) use ($request) {
                $q->where('slug', $request->category);
            });
        }

        // Tag filter
        if ($request->has('tag') && $request->tag) {
            $query->whereHas('tags', function($q) use ($request) {
                $q->where('slug', $request->tag);
            });
        }

        $posts = $query->paginate(9);

        // Get featured posts for sidebar
        $featuredPosts = BlogPost::published()
            ->featured()
            ->limit(3)
            ->get();

        // Get categories for sidebar
        $categories = BlogCategory::active()
            ->withCount(['publishedPosts'])
            ->orderBy('name')
            ->get();

        // Get popular tags
        $popularTags = BlogTag::active()
            ->withCount(['publishedPosts'])
            ->orderBy('published_posts_count', 'desc')
            ->limit(10)
            ->get();

        // Get meta tags
        $metaTags = MetaTag::getForPage('blog');

        return view('blog.index', compact(
            'posts',
            'featuredPosts',
            'categories',
            'popularTags',
            'metaTags'
        ));
    }

    /**
     * Display the specified blog post.
     */
    public function show($slug)
    {
        $post = BlogPost::published()
            ->with(['category', 'author', 'tags'])
            ->where('slug', $slug)
            ->firstOrFail();

        // Increment views
        $post->incrementViews();

        // Get related posts
        $relatedPosts = $post->getRelatedPosts(3);

        // Get meta tags
        $metaTags = MetaTag::getForBlogPost($slug);

        return view('blog.show', compact('post', 'relatedPosts', 'metaTags'));
    }

    /**
     * Display posts by category.
     */
    public function category($slug)
    {
        $category = BlogCategory::active()
            ->where('slug', $slug)
            ->firstOrFail();

        $posts = BlogPost::published()
            ->byCategory($slug)
            ->with(['category', 'author', 'tags'])
            ->orderBy('published_at', 'desc')
            ->paginate(9);

        // Get other categories
        $categories = BlogCategory::active()
            ->where('id', '!=', $category->id)
            ->withCount(['publishedPosts'])
            ->orderBy('name')
            ->get();

        return view('blog.category', compact('category', 'posts', 'categories'));
    }

    /**
     * Display posts by tag.
     */
    public function tag($slug)
    {
        $tag = BlogTag::active()
            ->where('slug', $slug)
            ->firstOrFail();

        $posts = BlogPost::published()
            ->byTag($slug)
            ->with(['category', 'author', 'tags'])
            ->orderBy('published_at', 'desc')
            ->paginate(9);

        // Get other tags
        $tags = BlogTag::active()
            ->where('id', '!=', $tag->id)
            ->withCount(['publishedPosts'])
            ->orderBy('published_posts_count', 'desc')
            ->limit(10)
            ->get();

        return view('blog.tag', compact('tag', 'posts', 'tags'));
    }
}
