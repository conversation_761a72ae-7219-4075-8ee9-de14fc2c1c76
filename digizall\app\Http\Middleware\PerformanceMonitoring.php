<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Services\PerformanceMonitoringService;
use Illuminate\Support\Facades\DB;

class PerformanceMonitoring
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Skip monitoring for certain routes
        if ($this->shouldSkipMonitoring($request)) {
            return $next($request);
        }

        // Start monitoring
        $requestId = PerformanceMonitoringService::startRequest();

        // Add request ID to request for use in other parts of the application
        $request->attributes->set('performance_request_id', $requestId);

        // Listen for database queries
        $this->listenForQueries($requestId);

        // Process the request
        $response = $next($request);

        // End monitoring and get metrics
        $metrics = PerformanceMonitoringService::endRequest($requestId);

        // Add performance headers to response (in debug mode)
        if (config('app.debug') && $metrics) {
            $response->headers->set('X-Response-Time', $metrics['duration_ms'] . 'ms');
            $response->headers->set('X-Memory-Usage', $metrics['memory_usage_mb'] . 'MB');
            $response->headers->set('X-Query-Count', $metrics['query_count']);
            $response->headers->set('X-Cache-Hits', $metrics['cache_hits']);
            $response->headers->set('X-Cache-Misses', $metrics['cache_misses']);
        }

        return $response;
    }

    /**
     * Determine if monitoring should be skipped for this request
     */
    private function shouldSkipMonitoring(Request $request): bool
    {
        $skipRoutes = [
            'telescope*',
            'horizon*',
            '_debugbar*',
            'livewire*',
        ];

        $path = $request->path();

        foreach ($skipRoutes as $pattern) {
            if (fnmatch($pattern, $path)) {
                return true;
            }
        }

        // Skip static assets
        if (preg_match('/\.(css|js|png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf|eot)$/i', $path)) {
            return true;
        }

        // Skip AJAX requests for certain endpoints
        if ($request->ajax() && in_array($path, ['api/status', 'api/health'])) {
            return true;
        }

        return false;
    }

    /**
     * Listen for database queries during the request
     */
    private function listenForQueries(string $requestId): void
    {
        DB::listen(function ($query) use ($requestId) {
            PerformanceMonitoringService::logQuery(
                $requestId,
                $query->sql,
                $query->bindings,
                $query->time
            );
        });
    }
}
