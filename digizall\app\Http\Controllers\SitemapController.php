<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Service;
use App\Models\BlogPost;
use Carbon\Carbon;

class SitemapController extends Controller
{
    /**
     * Generate XML sitemap
     */
    public function index()
    {
        $urls = collect();

        // Static pages
        $staticPages = [
            ['url' => route('home'), 'priority' => '1.0', 'changefreq' => 'weekly'],
            ['url' => route('services.index'), 'priority' => '0.9', 'changefreq' => 'weekly'],
            ['url' => route('blog.index'), 'priority' => '0.8', 'changefreq' => 'daily'],
            ['url' => route('about'), 'priority' => '0.7', 'changefreq' => 'monthly'],
            ['url' => route('contact'), 'priority' => '0.7', 'changefreq' => 'monthly'],
            ['url' => route('testimonials'), 'priority' => '0.6', 'changefreq' => 'monthly'],
            ['url' => route('quote.index'), 'priority' => '0.8', 'changefreq' => 'monthly'],
        ];

        foreach ($staticPages as $page) {
            $urls->push([
                'url' => $page['url'],
                'lastmod' => now()->toISOString(),
                'priority' => $page['priority'],
                'changefreq' => $page['changefreq'],
            ]);
        }

        // Services
        $services = Service::active()->get();
        foreach ($services as $service) {
            $urls->push([
                'url' => route('services.show', $service->slug),
                'lastmod' => $service->updated_at->toISOString(),
                'priority' => '0.8',
                'changefreq' => 'monthly',
            ]);
        }

        // Blog posts
        $blogPosts = BlogPost::published()->get();
        foreach ($blogPosts as $post) {
            $urls->push([
                'url' => route('blog.show', $post->slug),
                'lastmod' => $post->updated_at->toISOString(),
                'priority' => '0.7',
                'changefreq' => 'monthly',
            ]);
        }

        // Blog categories
        $categories = BlogPost::published()
                             ->select('category')
                             ->distinct()
                             ->whereNotNull('category')
                             ->pluck('category');

        foreach ($categories as $category) {
            $urls->push([
                'url' => route('blog.index', ['category' => $category]),
                'lastmod' => now()->toISOString(),
                'priority' => '0.6',
                'changefreq' => 'weekly',
            ]);
        }

        return response()->view('sitemap.xml', compact('urls'))
                        ->header('Content-Type', 'application/xml');
    }

    /**
     * Generate robots.txt
     */
    public function robots()
    {
        $content = "User-agent: *\n";
        $content .= "Allow: /\n";
        $content .= "Disallow: /admin/\n";
        $content .= "Disallow: /app/\n";
        $content .= "Disallow: /api/\n";
        $content .= "Disallow: /storage/\n";
        $content .= "\n";
        $content .= "Sitemap: " . route('sitemap') . "\n";

        return response($content)->header('Content-Type', 'text/plain');
    }
}
