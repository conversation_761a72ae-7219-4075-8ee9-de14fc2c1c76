<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>New Contact Message - DIGIZALL</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
        .container { max-width: 700px; margin: 0 auto; padding: 20px; }
        .header { background: #28a745; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
        .content { background: #ffffff; padding: 30px; border: 1px solid #e1e5e9; }
        .footer { background: #f8f9fa; padding: 15px; text-align: center; border-radius: 0 0 8px 8px; border: 1px solid #e1e5e9; border-top: none; }
        .alert { background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .client-info { background: #e8f5e8; padding: 20px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #28a745; }
        .message-content { background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0; border: 1px solid #dee2e6; }
        h1, h2, h3 { margin-top: 0; }
        .logo { font-size: 20px; font-weight: bold; }
        table { width: 100%; border-collapse: collapse; margin: 15px 0; }
        th, td { padding: 8px 12px; text-align: left; border-bottom: 1px solid #dee2e6; }
        th { background: #f8f9fa; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="logo">📧 DIGIZALL CONTACT ALERT</div>
            <h1>New Contact Message</h1>
            <p>{{ now()->format('F d, Y - g:i A') }}</p>
        </div>

        <!-- Content -->
        <div class="content">
            <div class="alert">
                <h2>📬 New Message Received</h2>
                <p>A new contact message has been submitted through the website contact form. Please review and respond promptly.</p>
            </div>

            <!-- Client Information -->
            <div class="client-info">
                <h3>👤 Contact Information</h3>
                <table>
                    <tr><th>Name:</th><td>{{ $contact->name }}</td></tr>
                    <tr><th>Email:</th><td><a href="mailto:{{ $contact->email }}">{{ $contact->email }}</a></td></tr>
                    @if($contact->phone)
                        <tr><th>Phone:</th><td><a href="tel:{{ $contact->phone }}">{{ $contact->phone }}</a></td></tr>
                    @endif
                    @if($contact->company)
                        <tr><th>Company:</th><td>{{ $contact->company }}</td></tr>
                    @endif
                    <tr><th>Subject:</th><td>{{ ucwords(str_replace('-', ' ', $contact->subject)) }}</td></tr>
                    <tr><th>Preferred Contact:</th><td>{{ ucfirst($contact->preferred_contact ?? 'email') }}</td></tr>
                    <tr><th>Marketing Consent:</th><td>{{ $contact->marketing_consent ? 'Yes' : 'No' }}</td></tr>
                </table>
            </div>

            <!-- Message Content -->
            <div class="message-content">
                <h3>💬 Message Content</h3>
                <div style="background: #fff; padding: 15px; border: 1px solid #dee2e6; border-radius: 5px; white-space: pre-wrap; min-height: 100px;">{{ $contact->message }}</div>
            </div>

            <!-- Technical Information -->
            <div style="background: #f1f3f4; padding: 15px; border-radius: 5px; margin: 20px 0;">
                <h4>🔧 Technical Information</h4>
                <p><strong>IP Address:</strong> {{ $contact->ip_address }}</p>
                <p><strong>User Agent:</strong> {{ $contact->user_agent }}</p>
                <p><strong>Submitted:</strong> {{ $contact->created_at->format('F d, Y - g:i A T') }}</p>
            </div>

            <!-- Quick Actions -->
            <div style="text-align: center; margin: 30px 0; padding: 20px; background: #e3f2fd; border-radius: 5px;">
                <h3>🚀 Quick Actions</h3>
                <p>
                    <a href="mailto:{{ $contact->email }}?subject=Re: {{ ucwords(str_replace('-', ' ', $contact->subject)) }}" 
                       style="display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 5px;">
                        📧 Reply via Email
                    </a>
                    @if($contact->phone)
                        <a href="tel:{{ $contact->phone }}" 
                           style="display: inline-block; padding: 10px 20px; background: #28a745; color: white; text-decoration: none; border-radius: 5px; margin: 5px;">
                            📞 Call Contact
                        </a>
                    @endif
                    @if($contact->preferred_contact === 'whatsapp' && $contact->phone)
                        <a href="https://wa.me/{{ str_replace(['+', '-', ' '], '', $contact->phone) }}" target="_blank"
                           style="display: inline-block; padding: 10px 20px; background: #25d366; color: white; text-decoration: none; border-radius: 5px; margin: 5px;">
                            💬 WhatsApp
                        </a>
                    @endif
                </p>
            </div>

            <!-- Subject Category Analysis -->
            <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px;">
                <h4>📊 Message Category</h4>
                @php
                    $category = $contact->subject;
                    $urgency = 'Normal';
                    $department = 'General';
                    
                    switch($category) {
                        case 'support':
                            $urgency = 'High';
                            $department = 'Support Team';
                            break;
                        case 'web-development':
                        case 'digital-marketing':
                        case 'seo-services':
                            $urgency = 'Medium';
                            $department = 'Sales Team';
                            break;
                        case 'general-inquiry':
                            $urgency = 'Normal';
                            $department = 'Customer Service';
                            break;
                        default:
                            $urgency = 'Normal';
                            $department = 'General';
                    }
                @endphp
                <p><strong>Category:</strong> {{ ucwords(str_replace('-', ' ', $category)) }}</p>
                <p><strong>Suggested Department:</strong> {{ $department }}</p>
                <p><strong>Urgency Level:</strong> <span style="color: {{ $urgency === 'High' ? '#dc3545' : ($urgency === 'Medium' ? '#ffc107' : '#28a745') }}; font-weight: bold;">{{ $urgency }}</span></p>
                <p><strong>Recommended Response Time:</strong> 
                    @if($urgency === 'High')
                        Within 4 hours
                    @elseif($urgency === 'Medium')
                        Within 12 hours
                    @else
                        Within 24 hours
                    @endif
                </p>
            </div>

            <!-- Response Template Suggestion -->
            <div style="background: #e8f5e8; padding: 15px; border-radius: 5px; margin: 15px 0;">
                <h4>💡 Suggested Response Approach</h4>
                @switch($category)
                    @case('support')
                        <p>This appears to be a support request. Prioritize immediate assistance and troubleshooting.</p>
                        @break
                    @case('web-development')
                    @case('digital-marketing')
                    @case('seo-services')
                        <p>This is a service inquiry. Consider scheduling a consultation call and sending relevant portfolio examples.</p>
                        @break
                    @case('general-inquiry')
                        <p>General inquiry - provide comprehensive information about our services and next steps.</p>
                        @break
                    @default
                        <p>Standard inquiry - respond with relevant information and offer to schedule a consultation.</p>
                @endswitch
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p><strong>DIGIZALL Admin Panel</strong></p>
            <p>This is an automated notification. Please respond to the contact promptly.</p>
        </div>
    </div>
</body>
</html>
