<?php

namespace App\Services;

use Illuminate\Support\Facades\Storage;
use Illuminate\Http\UploadedFile;
use Intervention\Image\Facades\Image;

class ImageOptimizationService
{
    const QUALITY_HIGH = 90;
    const QUALITY_MEDIUM = 75;
    const QUALITY_LOW = 60;

    const SIZES = [
        'thumbnail' => [150, 150],
        'small' => [300, 300],
        'medium' => [600, 600],
        'large' => [1200, 1200],
        'hero' => [1920, 1080],
    ];

    /**
     * Optimize and resize uploaded image
     */
    public static function optimizeUpload(UploadedFile $file, $directory = 'images', $sizes = ['original', 'medium', 'thumbnail'])
    {
        $filename = self::generateFilename($file);
        $results = [];

        foreach ($sizes as $size) {
            if ($size === 'original') {
                $optimized = self::optimizeImage($file->getPathname());
                $path = Storage::disk('public')->putFileAs($directory, $optimized, $filename);
                $results['original'] = $path;
            } else {
                $resized = self::resizeImage($file->getPathname(), $size);
                $sizeFilename = self::addSizeToFilename($filename, $size);
                $path = Storage::disk('public')->putFileAs($directory, $resized, $sizeFilename);
                $results[$size] = $path;
            }
        }

        return $results;
    }

    /**
     * Generate unique filename
     */
    private static function generateFilename(UploadedFile $file)
    {
        $extension = $file->getClientOriginalExtension();
        $name = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
        $slug = \Str::slug($name);
        $timestamp = time();
        
        return "{$slug}-{$timestamp}.{$extension}";
    }

    /**
     * Add size suffix to filename
     */
    private static function addSizeToFilename($filename, $size)
    {
        $info = pathinfo($filename);
        return $info['filename'] . '-' . $size . '.' . $info['extension'];
    }

    /**
     * Optimize image without resizing
     */
    public static function optimizeImage($imagePath, $quality = self::QUALITY_MEDIUM)
    {
        $image = Image::make($imagePath);
        
        // Auto-orient based on EXIF data
        $image->orientate();
        
        // Optimize based on format
        $format = $image->mime();
        
        switch ($format) {
            case 'image/jpeg':
                $image->encode('jpg', $quality);
                break;
            case 'image/png':
                // For PNG, use compression level instead of quality
                $image->encode('png', 9);
                break;
            case 'image/webp':
                $image->encode('webp', $quality);
                break;
            default:
                $image->encode('jpg', $quality);
        }
        
        return $image;
    }

    /**
     * Resize image to specific size
     */
    public static function resizeImage($imagePath, $sizeName, $quality = self::QUALITY_MEDIUM)
    {
        if (!isset(self::SIZES[$sizeName])) {
            throw new \InvalidArgumentException("Unknown size: {$sizeName}");
        }

        [$width, $height] = self::SIZES[$sizeName];
        
        $image = Image::make($imagePath);
        $image->orientate();
        
        // Resize with aspect ratio constraint
        $image->resize($width, $height, function ($constraint) {
            $constraint->aspectRatio();
            $constraint->upsize(); // Prevent upsizing
        });
        
        // If the image is smaller than the target size, center it on a white background
        if ($image->width() < $width || $image->height() < $height) {
            $canvas = Image::canvas($width, $height, '#ffffff');
            $canvas->insert($image, 'center');
            $image = $canvas;
        }
        
        // Optimize
        $format = $image->mime();
        switch ($format) {
            case 'image/jpeg':
                $image->encode('jpg', $quality);
                break;
            case 'image/png':
                $image->encode('png', 9);
                break;
            case 'image/webp':
                $image->encode('webp', $quality);
                break;
            default:
                $image->encode('jpg', $quality);
        }
        
        return $image;
    }

    /**
     * Convert image to WebP format
     */
    public static function convertToWebP($imagePath, $quality = self::QUALITY_MEDIUM)
    {
        $image = Image::make($imagePath);
        $image->orientate();
        $image->encode('webp', $quality);
        
        return $image;
    }

    /**
     * Generate responsive image set
     */
    public static function generateResponsiveSet($imagePath, $directory = 'images')
    {
        $filename = basename($imagePath, '.' . pathinfo($imagePath, PATHINFO_EXTENSION));
        $results = [];

        foreach (self::SIZES as $sizeName => $dimensions) {
            $resized = self::resizeImage($imagePath, $sizeName);
            $sizeFilename = "{$filename}-{$sizeName}.jpg";
            $path = Storage::disk('public')->put("{$directory}/{$sizeFilename}", $resized->stream());
            
            $results[$sizeName] = [
                'path' => $path,
                'url' => Storage::disk('public')->url("{$directory}/{$sizeFilename}"),
                'width' => $dimensions[0],
                'height' => $dimensions[1],
            ];

            // Also generate WebP version
            $webp = self::convertToWebP($imagePath);
            $webpFilename = "{$filename}-{$sizeName}.webp";
            $webpPath = Storage::disk('public')->put("{$directory}/{$webpFilename}", $webp->stream());
            
            $results[$sizeName]['webp'] = [
                'path' => $webpPath,
                'url' => Storage::disk('public')->url("{$directory}/{$webpFilename}"),
            ];
        }

        return $results;
    }

    /**
     * Get image dimensions
     */
    public static function getImageDimensions($imagePath)
    {
        $image = Image::make($imagePath);
        
        return [
            'width' => $image->width(),
            'height' => $image->height(),
            'aspect_ratio' => $image->width() / $image->height(),
        ];
    }

    /**
     * Compress existing images in storage
     */
    public static function compressExistingImages($directory = 'images', $quality = self::QUALITY_MEDIUM)
    {
        $files = Storage::disk('public')->allFiles($directory);
        $compressed = 0;
        $totalSavings = 0;

        foreach ($files as $file) {
            $fullPath = Storage::disk('public')->path($file);
            
            if (self::isImage($fullPath)) {
                $originalSize = filesize($fullPath);
                
                $optimized = self::optimizeImage($fullPath, $quality);
                Storage::disk('public')->put($file, $optimized->stream());
                
                $newSize = filesize($fullPath);
                $savings = $originalSize - $newSize;
                
                if ($savings > 0) {
                    $compressed++;
                    $totalSavings += $savings;
                }
            }
        }

        return [
            'compressed_count' => $compressed,
            'total_savings_bytes' => $totalSavings,
            'total_savings_mb' => round($totalSavings / 1024 / 1024, 2),
        ];
    }

    /**
     * Check if file is an image
     */
    private static function isImage($filePath)
    {
        $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $filePath);
        finfo_close($finfo);
        
        return in_array($mimeType, $allowedTypes);
    }

    /**
     * Generate image srcset for responsive images
     */
    public static function generateSrcSet($baseUrl, $sizes = ['small', 'medium', 'large'])
    {
        $srcset = [];
        
        foreach ($sizes as $size) {
            if (isset(self::SIZES[$size])) {
                $width = self::SIZES[$size][0];
                $url = str_replace('.jpg', "-{$size}.jpg", $baseUrl);
                $srcset[] = "{$url} {$width}w";
            }
        }
        
        return implode(', ', $srcset);
    }

    /**
     * Get optimized image URL with fallback
     */
    public static function getOptimizedUrl($originalUrl, $size = 'medium', $format = 'jpg')
    {
        $info = pathinfo($originalUrl);
        $optimizedUrl = $info['dirname'] . '/' . $info['filename'] . '-' . $size . '.' . $format;
        
        // Check if optimized version exists
        $path = str_replace(Storage::disk('public')->url(''), '', $optimizedUrl);
        
        if (Storage::disk('public')->exists($path)) {
            return $optimizedUrl;
        }
        
        // Fallback to original
        return $originalUrl;
    }

    /**
     * Clean up unused image sizes
     */
    public static function cleanupUnusedSizes($directory = 'images')
    {
        $files = Storage::disk('public')->allFiles($directory);
        $deleted = 0;
        
        foreach ($files as $file) {
            $filename = basename($file);
            
            // Check if it's a size variant (contains size suffix)
            foreach (array_keys(self::SIZES) as $size) {
                if (strpos($filename, "-{$size}.") !== false) {
                    // Check if original file exists
                    $originalFile = str_replace("-{$size}.", '.', $file);
                    
                    if (!Storage::disk('public')->exists($originalFile)) {
                        Storage::disk('public')->delete($file);
                        $deleted++;
                    }
                }
            }
        }
        
        return $deleted;
    }
}
