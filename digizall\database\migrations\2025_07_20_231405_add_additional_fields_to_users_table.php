<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('phone')->nullable()->after('email');
            $table->string('avatar')->nullable()->after('phone');
            $table->string('employee_id')->nullable()->unique()->after('avatar');
            $table->string('department')->nullable()->after('employee_id');
            $table->string('position')->nullable()->after('department');
            $table->date('hire_date')->nullable()->after('position');
            $table->decimal('hourly_rate', 8, 2)->nullable()->after('hire_date');
            $table->time('work_start_time')->default('09:00:00')->after('hourly_rate');
            $table->time('work_end_time')->default('17:00:00')->after('work_start_time');
            $table->boolean('is_active')->default(true)->after('work_end_time');
            $table->timestamp('last_login_at')->nullable()->after('is_active');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'phone', 'avatar', 'employee_id', 'department', 'position',
                'hire_date', 'hourly_rate', 'work_start_time', 'work_end_time',
                'is_active', 'last_login_at'
            ]);
        });
    }
};
