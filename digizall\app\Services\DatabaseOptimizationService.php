<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Artisan;

class DatabaseOptimizationService
{
    /**
     * Optimize database tables
     */
    public static function optimizeTables()
    {
        $tables = self::getAllTables();
        
        foreach ($tables as $table) {
            DB::statement("OPTIMIZE TABLE {$table}");
        }
        
        return count($tables);
    }

    /**
     * Analyze database tables
     */
    public static function analyzeTables()
    {
        $tables = self::getAllTables();
        
        foreach ($tables as $table) {
            DB::statement("ANALYZE TABLE {$table}");
        }
        
        return count($tables);
    }

    /**
     * Get all table names
     */
    private static function getAllTables()
    {
        $tables = [];
        $results = DB::select('SHOW TABLES');
        
        foreach ($results as $result) {
            $tableArray = (array) $result;
            $tables[] = array_values($tableArray)[0];
        }
        
        return $tables;
    }

    /**
     * Get database size information
     */
    public static function getDatabaseSize()
    {
        $database = config('database.connections.mysql.database');
        
        $result = DB::selectOne("
            SELECT 
                ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb,
                ROUND(SUM(data_length) / 1024 / 1024, 2) AS data_mb,
                ROUND(SUM(index_length) / 1024 / 1024, 2) AS index_mb
            FROM information_schema.tables 
            WHERE table_schema = ?
        ", [$database]);
        
        return $result;
    }

    /**
     * Get table size information
     */
    public static function getTableSizes()
    {
        $database = config('database.connections.mysql.database');
        
        return DB::select("
            SELECT 
                table_name,
                ROUND(((data_length + index_length) / 1024 / 1024), 2) AS size_mb,
                ROUND((data_length / 1024 / 1024), 2) AS data_mb,
                ROUND((index_length / 1024 / 1024), 2) AS index_mb,
                table_rows
            FROM information_schema.TABLES 
            WHERE table_schema = ?
            ORDER BY (data_length + index_length) DESC
        ", [$database]);
    }

    /**
     * Get slow queries (if enabled)
     */
    public static function getSlowQueries($limit = 10)
    {
        try {
            return DB::select("
                SELECT 
                    sql_text,
                    exec_count,
                    avg_timer_wait / 1000000000 as avg_time_seconds,
                    sum_timer_wait / 1000000000 as total_time_seconds
                FROM performance_schema.events_statements_summary_by_digest 
                WHERE schema_name = ?
                ORDER BY avg_timer_wait DESC 
                LIMIT ?
            ", [config('database.connections.mysql.database'), $limit]);
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * Check for missing indexes
     */
    public static function checkMissingIndexes()
    {
        $recommendations = [];
        
        // Check for foreign keys without indexes
        $foreignKeys = self::getForeignKeysWithoutIndexes();
        foreach ($foreignKeys as $fk) {
            $recommendations[] = [
                'type' => 'missing_index',
                'table' => $fk->table_name,
                'column' => $fk->column_name,
                'reason' => 'Foreign key without index',
                'suggestion' => "ADD INDEX idx_{$fk->table_name}_{$fk->column_name} ({$fk->column_name})"
            ];
        }
        
        // Check for commonly queried columns without indexes
        $commonColumns = ['email', 'slug', 'status', 'created_at', 'updated_at'];
        foreach (self::getAllTables() as $table) {
            foreach ($commonColumns as $column) {
                if (Schema::hasColumn($table, $column) && !self::hasIndex($table, $column)) {
                    $recommendations[] = [
                        'type' => 'suggested_index',
                        'table' => $table,
                        'column' => $column,
                        'reason' => 'Commonly queried column',
                        'suggestion' => "ADD INDEX idx_{$table}_{$column} ({$column})"
                    ];
                }
            }
        }
        
        return $recommendations;
    }

    /**
     * Get foreign keys without indexes
     */
    private static function getForeignKeysWithoutIndexes()
    {
        $database = config('database.connections.mysql.database');
        
        return DB::select("
            SELECT 
                kcu.table_name,
                kcu.column_name,
                kcu.constraint_name
            FROM information_schema.key_column_usage kcu
            LEFT JOIN information_schema.statistics s 
                ON kcu.table_name = s.table_name 
                AND kcu.column_name = s.column_name
                AND s.table_schema = kcu.table_schema
            WHERE kcu.table_schema = ?
                AND kcu.referenced_table_name IS NOT NULL
                AND s.column_name IS NULL
        ", [$database]);
    }

    /**
     * Check if table has index on column
     */
    private static function hasIndex($table, $column)
    {
        $database = config('database.connections.mysql.database');
        
        $result = DB::selectOne("
            SELECT COUNT(*) as count
            FROM information_schema.statistics 
            WHERE table_schema = ? 
                AND table_name = ? 
                AND column_name = ?
        ", [$database, $table, $column]);
        
        return $result->count > 0;
    }

    /**
     * Clean up old data
     */
    public static function cleanupOldData()
    {
        $cleaned = 0;
        
        // Clean up old failed jobs (older than 7 days)
        if (Schema::hasTable('failed_jobs')) {
            $deleted = DB::table('failed_jobs')
                        ->where('failed_at', '<', now()->subDays(7))
                        ->delete();
            $cleaned += $deleted;
        }
        
        // Clean up old sessions (older than 30 days)
        if (Schema::hasTable('sessions')) {
            $deleted = DB::table('sessions')
                        ->where('last_activity', '<', now()->subDays(30)->timestamp)
                        ->delete();
            $cleaned += $deleted;
        }
        
        // Clean up old password resets (older than 1 day)
        if (Schema::hasTable('password_reset_tokens')) {
            $deleted = DB::table('password_reset_tokens')
                        ->where('created_at', '<', now()->subDay())
                        ->delete();
            $cleaned += $deleted;
        }
        
        return $cleaned;
    }

    /**
     * Get database connection info
     */
    public static function getConnectionInfo()
    {
        try {
            $variables = DB::select("SHOW VARIABLES LIKE 'max_connections'");
            $status = DB::select("SHOW STATUS LIKE 'Threads_connected'");
            $version = DB::selectOne("SELECT VERSION() as version");
            
            return [
                'version' => $version->version ?? 'Unknown',
                'max_connections' => $variables[0]->Value ?? 'Unknown',
                'current_connections' => $status[0]->Value ?? 'Unknown',
            ];
        } catch (\Exception $e) {
            return [
                'version' => 'Unknown',
                'max_connections' => 'Unknown',
                'current_connections' => 'Unknown',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Run full database optimization
     */
    public static function runFullOptimization()
    {
        $results = [];
        
        // Optimize tables
        $results['optimized_tables'] = self::optimizeTables();
        
        // Analyze tables
        $results['analyzed_tables'] = self::analyzeTables();
        
        // Clean up old data
        $results['cleaned_records'] = self::cleanupOldData();
        
        // Get recommendations
        $results['recommendations'] = self::checkMissingIndexes();
        
        return $results;
    }

    /**
     * Get database health score
     */
    public static function getHealthScore()
    {
        $score = 100;
        $issues = [];
        
        // Check database size
        $size = self::getDatabaseSize();
        if ($size->size_mb > 1000) { // > 1GB
            $score -= 10;
            $issues[] = 'Database size is large (> 1GB)';
        }
        
        // Check for missing indexes
        $missingIndexes = self::checkMissingIndexes();
        if (count($missingIndexes) > 0) {
            $score -= (count($missingIndexes) * 5);
            $issues[] = count($missingIndexes) . ' potential index optimizations found';
        }
        
        // Check slow queries
        $slowQueries = self::getSlowQueries(5);
        if (count($slowQueries) > 0) {
            $score -= 15;
            $issues[] = 'Slow queries detected';
        }
        
        return [
            'score' => max(0, $score),
            'grade' => self::getGrade($score),
            'issues' => $issues,
        ];
    }

    /**
     * Get grade based on score
     */
    private static function getGrade($score)
    {
        if ($score >= 90) return 'A';
        if ($score >= 80) return 'B';
        if ($score >= 70) return 'C';
        if ($score >= 60) return 'D';
        return 'F';
    }
}
