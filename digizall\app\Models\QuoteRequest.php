<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class QuoteRequest extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'email',
        'phone',
        'company',
        'service_id',
        'budget_range',
        'message',
        'status',
        'admin_notes',
        'responded_at'
    ];

    protected $casts = [
        'responded_at' => 'datetime'
    ];

    /**
     * Get the service that owns the quote request.
     */
    public function service()
    {
        return $this->belongsTo(Service::class);
    }

    /**
     * Scope for new quote requests.
     */
    public function scopeNew($query)
    {
        return $query->where('status', 'new');
    }

    /**
     * Scope for pending quote requests.
     */
    public function scopePending($query)
    {
        return $query->whereIn('status', ['new', 'in_progress']);
    }

    /**
     * Scope for completed quote requests.
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Get status badge color.
     */
    public function getStatusBadgeColorAttribute(): string
    {
        return match($this->status) {
            'new' => 'bg-blue-100 text-blue-800',
            'in_progress' => 'bg-yellow-100 text-yellow-800',
            'quoted' => 'bg-purple-100 text-purple-800',
            'completed' => 'bg-green-100 text-green-800',
            'cancelled' => 'bg-red-100 text-red-800',
            default => 'bg-gray-100 text-gray-800'
        };
    }

    /**
     * Get formatted status.
     */
    public function getFormattedStatusAttribute(): string
    {
        return ucwords(str_replace('_', ' ', $this->status));
    }

    /**
     * Mark as responded.
     */
    public function markAsResponded(): void
    {
        $this->update([
            'responded_at' => now(),
            'status' => 'quoted'
        ]);
    }

    /**
     * Check if responded.
     */
    public function isResponded(): bool
    {
        return !is_null($this->responded_at);
    }
}
