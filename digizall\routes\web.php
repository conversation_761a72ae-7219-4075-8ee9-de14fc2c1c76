<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\ServicesController;
use App\Http\Controllers\BlogController;
use App\Http\Controllers\StaticPagesController;
use App\Http\Controllers\QuoteController;
use App\Http\Controllers\ContactController;
use App\Http\Controllers\Auth\LoginController;
use App\Http\Controllers\Auth\RegisterController;

// Admin Controllers
use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\Admin\ServicesController as AdminServicesController;
use App\Http\Controllers\Admin\BlogController as AdminBlogController;
use App\Http\Controllers\Admin\QuotesController;
use App\Http\Controllers\Admin\ContactsController;
use App\Http\Controllers\Admin\PerformanceController;

// Attendance Controllers
use App\Http\Controllers\Attendance\DashboardController as AttendanceDashboardController;
use App\Http\Controllers\Attendance\AttendanceController;
use App\Http\Controllers\Attendance\EmployeeController;
use App\Http\Controllers\SitemapController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

// Home Page
Route::get('/', [HomeController::class, 'index'])->name('home');

// Authentication Routes
Route::middleware('guest')->group(function () {
    Route::get('/login', [LoginController::class, 'showLoginForm'])->name('login');
    Route::post('/login', [LoginController::class, 'login']);
    Route::get('/register', [RegisterController::class, 'showRegistrationForm'])->name('register');
    Route::post('/register', [RegisterController::class, 'register']);
});

Route::post('/logout', [LoginController::class, 'logout'])->name('logout')->middleware('auth');

// Services Routes
Route::get('/services', [ServicesController::class, 'index'])->name('services.index');
Route::get('/services/category/{category}', [ServicesController::class, 'category'])->name('services.category');
Route::get('/services/{slug}', [ServicesController::class, 'show'])->name('services.show');
// Static Pages Routes
Route::get('/about', [StaticPagesController::class, 'about'])->name('about');
Route::get('/contact', [StaticPagesController::class, 'contact'])->name('contact');
Route::get('/portfolio', [StaticPagesController::class, 'portfolio'])->name('portfolio');
Route::get('/testimonials', [StaticPagesController::class, 'testimonials'])->name('testimonials');
Route::get('/privacy-policy', [StaticPagesController::class, 'privacyPolicy'])->name('privacy-policy');
Route::get('/terms-of-service', [StaticPagesController::class, 'termsOfService'])->name('terms-of-service');
Route::get('/sitemap', [StaticPagesController::class, 'sitemap'])->name('sitemap');
// Blog Routes
Route::get('/blog', [BlogController::class, 'index'])->name('blog.index');
Route::get('/blog/category/{slug}', [BlogController::class, 'category'])->name('blog.category');
Route::get('/blog/tag/{slug}', [BlogController::class, 'tag'])->name('blog.tag');
Route::get('/blog/{slug}', [BlogController::class, 'show'])->name('blog.show');
// Quote Routes
Route::get('/quote', [QuoteController::class, 'index'])->name('quote');
Route::post('/quote', [QuoteController::class, 'store'])->name('quote.store');
Route::get('/quote/success', [QuoteController::class, 'success'])->name('quote.success');

// Contact Form Routes
Route::post('/contact', [ContactController::class, 'store'])->name('contact.store');
Route::get('/contact/success', [ContactController::class, 'success'])->name('contact.success');

// Authentication Routes
Auth::routes();

// Admin Routes
Route::prefix('admin')->name('admin.')->middleware(['auth', 'admin'])->group(function () {
    // Dashboard
    Route::get('/', [DashboardController::class, 'index'])->name('dashboard');
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard.index');

    // Services Management
    Route::resource('services', AdminServicesController::class);

    // Blog Management
    Route::resource('blog', AdminBlogController::class);
    Route::post('blog/{blog}/publish', [AdminBlogController::class, 'publish'])->name('blog.publish');
    Route::post('blog/{blog}/unpublish', [AdminBlogController::class, 'unpublish'])->name('blog.unpublish');

    // Quotes Management
    Route::resource('quotes', QuotesController::class)->except(['create', 'store', 'edit', 'update']);
    Route::post('quotes/{quote}/status', [QuotesController::class, 'updateStatus'])->name('quotes.status');
    Route::post('quotes/{quote}/notes', [QuotesController::class, 'addNotes'])->name('quotes.notes');
    Route::get('quotes/export', [QuotesController::class, 'export'])->name('quotes.export');

    // Contacts Management
    Route::resource('contacts', ContactsController::class)->except(['create', 'store', 'edit', 'update']);
    Route::post('contacts/{contact}/status', [ContactsController::class, 'updateStatus'])->name('contacts.status');
    Route::post('contacts/{contact}/notes', [ContactsController::class, 'addNotes'])->name('contacts.notes');
    Route::get('contacts/export', [ContactsController::class, 'export'])->name('contacts.export');

    // Performance Management
    Route::get('performance', [PerformanceController::class, 'index'])->name('performance.index');
    Route::post('performance/optimize', [PerformanceController::class, 'optimize'])->name('performance.optimize');
    Route::get('performance/metrics', [PerformanceController::class, 'metrics'])->name('performance.metrics');
    Route::get('performance/trends', [PerformanceController::class, 'trends'])->name('performance.trends');
    Route::get('performance/slow-queries', [PerformanceController::class, 'slowQueries'])->name('performance.slow-queries');
    Route::delete('performance/clear-data', [PerformanceController::class, 'clearData'])->name('performance.clear-data');
    Route::get('performance/export', [PerformanceController::class, 'export'])->name('performance.export');
});

// Attendance Portal Routes (app.digizall.com)
Route::prefix('app')->name('attendance.')->middleware('auth')->group(function () {
    // Dashboard
    Route::get('/', [AttendanceDashboardController::class, 'index'])->name('dashboard');
    Route::get('/dashboard', [AttendanceDashboardController::class, 'index'])->name('dashboard.index');

    // Attendance Actions
    Route::post('/check-in', [AttendanceController::class, 'checkIn'])->name('check-in');
    Route::post('/check-out', [AttendanceController::class, 'checkOut'])->name('check-out');
    Route::post('/start-break', [AttendanceController::class, 'startBreak'])->name('start-break');
    Route::post('/end-break', [AttendanceController::class, 'endBreak'])->name('end-break');
    Route::get('/status', [AttendanceController::class, 'status'])->name('status');

    // Attendance History
    Route::get('/history', [AttendanceController::class, 'history'])->name('history');

    // Employee Management (Admin only)
    Route::middleware('admin')->group(function () {
        Route::resource('employees', EmployeeController::class);
        Route::get('reports', [AttendanceController::class, 'reports'])->name('reports');
        Route::get('reports/export', [AttendanceController::class, 'exportReports'])->name('reports.export');
    });
});

// SEO Routes
Route::get('/sitemap.xml', [SitemapController::class, 'index'])->name('sitemap');
Route::get('/robots.txt', [SitemapController::class, 'robots'])->name('robots');
