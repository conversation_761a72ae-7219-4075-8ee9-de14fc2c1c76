@extends('layouts.admin')

@section('title', 'Contact Message Details')
@section('page-title', 'Contact Message Details')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-semibold text-gray-900">Contact Message</h1>
            <p class="text-gray-600">From {{ $contact->name }} - {{ $contact->created_at->format('M j, Y g:i A') }}</p>
        </div>
        <div class="flex space-x-2">
            <a href="mailto:{{ $contact->email }}?subject=Re: {{ $contact->subject }}" 
               class="inline-flex items-center px-4 py-2 bg-green-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-green-700">
                <i class="fas fa-reply mr-2"></i>
                Reply via Email
            </a>
            <a href="{{ route('admin.contacts.index') }}" 
               class="inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Messages
            </a>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Message Content -->
            <div class="bg-white rounded-lg shadow p-6">
                <div class="border-b border-gray-200 pb-4 mb-4">
                    <h2 class="text-xl font-semibold text-gray-900">{{ $contact->subject }}</h2>
                    <div class="mt-2 flex items-center space-x-4 text-sm text-gray-500">
                        <span>From: {{ $contact->name }}</span>
                        <span>•</span>
                        <span>{{ $contact->created_at->format('M j, Y g:i A') }}</span>
                        <span>•</span>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                            {{ $contact->status === 'pending' ? 'bg-yellow-100 text-yellow-800' : 
                               ($contact->status === 'read' ? 'bg-blue-100 text-blue-800' : 
                               ($contact->status === 'replied' ? 'bg-green-100 text-green-800' : 'bg-purple-100 text-purple-800')) }}">
                            {{ ucfirst($contact->status) }}
                        </span>
                    </div>
                </div>

                <div class="prose max-w-none">
                    <p class="text-gray-900 whitespace-pre-line">{{ $contact->message }}</p>
                </div>
            </div>

            <!-- Admin Notes -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Admin Notes</h3>
                
                @if($contact->admin_notes)
                    <div class="bg-gray-50 rounded-lg p-4 mb-4">
                        <div class="text-sm text-gray-900 whitespace-pre-line">{{ $contact->admin_notes }}</div>
                    </div>
                @endif

                <form method="POST" action="{{ route('admin.contacts.add-notes', $contact) }}">
                    @csrf
                    <div class="space-y-4">
                        <div>
                            <label for="notes" class="block text-sm font-medium text-gray-700 mb-1">Add Notes</label>
                            <textarea id="notes" name="notes" rows="4" 
                                      class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                      placeholder="Add internal notes about this contact..."></textarea>
                            @error('notes')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                        <button type="submit" 
                                class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700">
                            <i class="fas fa-plus mr-2"></i>
                            Add Notes
                        </button>
                    </div>
                </form>
            </div>

            <!-- Response History -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Response History</h3>
                
                <div class="space-y-4">
                    <div class="flex items-start space-x-3">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-envelope text-white text-xs"></i>
                            </div>
                        </div>
                        <div class="flex-1">
                            <div class="text-sm">
                                <span class="font-medium text-gray-900">Message Received</span>
                                <span class="text-gray-500">{{ $contact->created_at->format('M j, Y g:i A') }}</span>
                            </div>
                            <div class="text-sm text-gray-500 mt-1">
                                Contact form submission from {{ $contact->name }}
                            </div>
                        </div>
                    </div>

                    @if($contact->replied_at)
                        <div class="flex items-start space-x-3">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-reply text-white text-xs"></i>
                                </div>
                            </div>
                            <div class="flex-1">
                                <div class="text-sm">
                                    <span class="font-medium text-gray-900">Replied</span>
                                    <span class="text-gray-500">{{ $contact->replied_at->format('M j, Y g:i A') }}</span>
                                </div>
                                <div class="text-sm text-gray-500 mt-1">
                                    Response sent to customer
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Contact Information -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Contact Information</h3>
                
                <div class="space-y-3">
                    <div>
                        <span class="text-sm font-medium text-gray-500">Name:</span>
                        <div class="text-sm text-gray-900">{{ $contact->name }}</div>
                    </div>
                    
                    <div>
                        <span class="text-sm font-medium text-gray-500">Email:</span>
                        <div class="text-sm text-gray-900">
                            <a href="mailto:{{ $contact->email }}" class="text-blue-600 hover:text-blue-800">
                                {{ $contact->email }}
                            </a>
                        </div>
                    </div>
                    
                    @if($contact->phone)
                        <div>
                            <span class="text-sm font-medium text-gray-500">Phone:</span>
                            <div class="text-sm text-gray-900">
                                <a href="tel:{{ $contact->phone }}" class="text-blue-600 hover:text-blue-800">
                                    {{ $contact->phone }}
                                </a>
                            </div>
                        </div>
                    @endif
                    
                    <div>
                        <span class="text-sm font-medium text-gray-500">Preferred Contact:</span>
                        <div class="text-sm text-gray-900 capitalize">{{ $contact->preferred_contact }}</div>
                    </div>
                    
                    <div>
                        <span class="text-sm font-medium text-gray-500">Submitted:</span>
                        <div class="text-sm text-gray-900">{{ $contact->created_at->format('M j, Y g:i A') }}</div>
                    </div>
                    
                    <div>
                        <span class="text-sm font-medium text-gray-500">Time Ago:</span>
                        <div class="text-sm text-gray-900">{{ $contact->created_at->diffForHumans() }}</div>
                    </div>
                </div>
            </div>

            <!-- Status Management -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Status Management</h3>
                
                <form method="POST" action="{{ route('admin.contacts.update-status', $contact) }}">
                    @csrf
                    <div class="space-y-4">
                        <div>
                            <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                            <select id="status" name="status" 
                                    class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                <option value="pending" {{ $contact->status === 'pending' ? 'selected' : '' }}>Pending</option>
                                <option value="read" {{ $contact->status === 'read' ? 'selected' : '' }}>Read</option>
                                <option value="replied" {{ $contact->status === 'replied' ? 'selected' : '' }}>Replied</option>
                                <option value="resolved" {{ $contact->status === 'resolved' ? 'selected' : '' }}>Resolved</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="admin_notes" class="block text-sm font-medium text-gray-700 mb-1">Status Notes</label>
                            <textarea id="admin_notes" name="admin_notes" rows="3" 
                                      class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                      placeholder="Add notes about status change..."></textarea>
                        </div>
                        
                        <button type="submit" 
                                class="w-full inline-flex justify-center items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700">
                            <i class="fas fa-save mr-2"></i>
                            Update Status
                        </button>
                    </div>
                </form>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
                
                <div class="space-y-3">
                    <a href="mailto:{{ $contact->email }}?subject=Re: {{ $contact->subject }}&body=Hi {{ $contact->name }},%0D%0A%0D%0AThank you for contacting us.%0D%0A%0D%0ABest regards,%0D%0ADIGIZALL Team" 
                       class="w-full inline-flex justify-center items-center px-4 py-2 bg-green-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-green-700">
                        <i class="fas fa-envelope mr-2"></i>
                        Send Email Reply
                    </a>
                    
                    @if($contact->phone)
                        <a href="tel:{{ $contact->phone }}" 
                           class="w-full inline-flex justify-center items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700">
                            <i class="fas fa-phone mr-2"></i>
                            Call Customer
                        </a>
                    @endif
                    
                    <form method="POST" action="{{ route('admin.contacts.destroy', $contact) }}" 
                          class="w-full" 
                          onsubmit="return confirm('Are you sure you want to delete this contact message? This action cannot be undone.')">
                        @csrf
                        @method('DELETE')
                        <button type="submit" 
                                class="w-full inline-flex justify-center items-center px-4 py-2 bg-red-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-red-700">
                            <i class="fas fa-trash mr-2"></i>
                            Delete Message
                        </button>
                    </form>
                </div>
            </div>

            <!-- Related Information -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Related Information</h3>
                
                <div class="text-sm text-gray-600">
                    <p class="mb-2">
                        <strong>IP Address:</strong> 
                        <span class="font-mono">{{ request()->ip() }}</span>
                    </p>
                    <p class="mb-2">
                        <strong>User Agent:</strong> 
                        <span class="text-xs">{{ request()->userAgent() }}</span>
                    </p>
                    <p>
                        <strong>Referrer:</strong> 
                        <span>{{ request()->headers->get('referer', 'Direct') }}</span>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
