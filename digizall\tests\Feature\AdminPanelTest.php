<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use App\Models\User;
use App\Models\QuoteRequest as Quote;
use App\Models\ContactMessage as Contact;

class AdminPanelTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Create admin user
        $this->admin = User::factory()->create([
            'is_admin' => true,
            'email' => '<EMAIL>',
        ]);

        // Create regular user
        $this->user = User::factory()->create([
            'is_admin' => false,
        ]);
    }

    public function test_admin_dashboard_requires_authentication()
    {
        $response = $this->get('/admin');
        $response->assertRedirect('/login');
    }

    public function test_admin_dashboard_requires_admin_privileges()
    {
        $response = $this->actingAs($this->user)->get('/admin');
        $response->assertStatus(403);
    }

    public function test_admin_can_access_dashboard()
    {
        $response = $this->actingAs($this->admin)->get('/admin');
        $response->assertStatus(200);
        $response->assertSee('Admin Dashboard');
    }

    public function test_admin_dashboard_shows_statistics()
    {
        // Create test data
        Quote::factory()->count(5)->create();
        Contact::factory()->count(3)->create();

        $response = $this->actingAs($this->admin)->get('/admin');

        $response->assertStatus(200);
        $response->assertSee('Total Quotes');
        $response->assertSee('Total Contacts');
    }

    public function test_admin_can_view_quotes()
    {
        $quote = Quote::factory()->create([
            'name' => 'Test Client',
            'email' => '<EMAIL>',
        ]);

        $response = $this->actingAs($this->admin)->get('/admin/quotes');

        $response->assertStatus(200);
        $response->assertSee('Test Client');
        $response->assertSee('<EMAIL>');
    }

    public function test_admin_can_view_quote_details()
    {
        $quote = Quote::factory()->create([
            'name' => 'Test Client',
            'description' => 'Test project description',
        ]);

        $response = $this->actingAs($this->admin)->get("/admin/quotes/{$quote->id}");

        $response->assertStatus(200);
        $response->assertSee('Test Client');
        $response->assertSee('Test project description');
    }

    public function test_admin_can_update_quote_status()
    {
        $quote = Quote::factory()->create(['status' => 'pending']);

        $response = $this->actingAs($this->admin)
                         ->post("/admin/quotes/{$quote->id}/status", [
                             'status' => 'in_progress',
                             'notes' => 'Started working on the project',
                         ]);

        $response->assertRedirect();

        $quote->refresh();
        $this->assertEquals('in_progress', $quote->status);
        $this->assertEquals('Started working on the project', $quote->admin_notes);
    }

    public function test_admin_can_view_contacts()
    {
        $contact = Contact::factory()->create([
            'name' => 'Test Contact',
            'email' => '<EMAIL>',
        ]);

        $response = $this->actingAs($this->admin)->get('/admin/contacts');

        $response->assertStatus(200);
        $response->assertSee('Test Contact');
        $response->assertSee('<EMAIL>');
    }

    public function test_admin_can_export_quotes()
    {
        Quote::factory()->count(3)->create();

        $response = $this->actingAs($this->admin)->get('/admin/quotes/export');

        $response->assertStatus(200);
        $response->assertHeader('content-type', 'text/csv; charset=UTF-8');
    }

    public function test_admin_can_access_performance_dashboard()
    {
        $response = $this->actingAs($this->admin)->get('/admin/performance');

        $response->assertStatus(200);
        $response->assertSee('Performance Dashboard');
    }

    public function test_non_admin_cannot_access_admin_routes()
    {
        $routes = [
            '/admin',
            '/admin/quotes',
            '/admin/contacts',
            '/admin/performance',
        ];

        foreach ($routes as $route) {
            $response = $this->actingAs($this->user)->get($route);
            $this->assertTrue(
                $response->status() === 403 || $response->status() === 302,
                "Route {$route} should be protected from non-admin users"
            );
        }
    }

    public function test_admin_middleware_blocks_unauthenticated_users()
    {
        $routes = [
            '/admin',
            '/admin/quotes',
            '/admin/contacts',
            '/admin/performance',
        ];

        foreach ($routes as $route) {
            $response = $this->get($route);
            $this->assertTrue(
                $response->status() === 302,
                "Route {$route} should redirect unauthenticated users"
            );
        }
    }
}
