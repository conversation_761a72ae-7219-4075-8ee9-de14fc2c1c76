<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use App\Services\SecurityService;

class SecureQuoteRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Check rate limiting
        if (SecurityService::checkRateLimit($this, 'quote')) {
            SecurityService::logSecurityEvent('quote_rate_limit_exceeded', [
                'ip' => $this->ip(),
                'remaining_attempts' => SecurityService::getRemainingAttempts($this, 'quote'),
            ]);
            return false;
        }

        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => [
                'required',
                'string',
                'max:100',
                'regex:/^[a-zA-Z\s\-\.\']+$/', // Only letters, spaces, hyphens, dots, apostrophes
            ],
            'email' => [
                'required',
                'email:rfc,dns',
                'max:255',
                'not_regex:/[<>"\']/', // Prevent XSS characters
            ],
            'phone' => [
                'nullable',
                'string',
                'max:20',
                'regex:/^[\+]?[0-9\s\-\(\)]+$/', // Only numbers, spaces, hyphens, parentheses, plus
            ],
            'company' => [
                'nullable',
                'string',
                'max:100',
                'regex:/^[a-zA-Z0-9\s\-\.&,]+$/', // Company name characters
            ],
            'website' => [
                'nullable',
                'url',
                'max:255',
                'not_regex:/javascript:|data:|vbscript:/i', // Prevent XSS
            ],
            'service' => [
                'required',
                'string',
                'in:web-development,web-application-development,search-engine-optimization,social-media-marketing,content-writing,graphic-design',
            ],
            'project_type' => [
                'required',
                'string',
                'in:new-project,redesign,maintenance,consultation',
            ],
            'budget' => [
                'required',
                'string',
                'in:under-1000,1000-5000,5000-10000,10000-25000,over-25000',
            ],
            'timeline' => [
                'required',
                'string',
                'in:asap,1-month,2-3-months,3-6-months,flexible',
            ],
            'description' => [
                'required',
                'string',
                'min:10',
                'max:2000',
                'not_regex:/<script|javascript:|data:|vbscript:/i', // Prevent XSS
            ],
            'features' => [
                'nullable',
                'array',
                'max:10',
            ],
            'features.*' => [
                'string',
                'in:responsive-design,cms-integration,e-commerce,seo-optimization,social-media-integration,analytics-tracking,contact-forms,blog-functionality,payment-gateway,multi-language',
            ],
            'additional_info' => [
                'nullable',
                'string',
                'max:1000',
                'not_regex:/<script|javascript:|data:|vbscript:/i', // Prevent XSS
            ],
            'preferred_contact' => [
                'required',
                'string',
                'in:email,phone,whatsapp',
            ],
            'marketing_consent' => [
                'nullable',
                'boolean',
            ],
            'g-recaptcha-response' => [
                'nullable', // Make optional for now, but can be required in production
                'string',
            ],
        ];
    }

    /**
     * Get custom validation messages
     */
    public function messages(): array
    {
        return [
            'name.regex' => 'Name can only contain letters, spaces, hyphens, dots, and apostrophes.',
            'email.not_regex' => 'Email contains invalid characters.',
            'phone.regex' => 'Phone number format is invalid.',
            'company.regex' => 'Company name contains invalid characters.',
            'website.not_regex' => 'Website URL contains invalid characters.',
            'description.not_regex' => 'Description contains invalid characters.',
            'additional_info.not_regex' => 'Additional information contains invalid characters.',
        ];
    }

    /**
     * Prepare the data for validation
     */
    protected function prepareForValidation(): void
    {
        // Sanitize input data
        $sanitized = SecurityService::sanitizeInput($this->all());
        $this->replace($sanitized);

        // Hit rate limiter
        SecurityService::hitRateLimit($this, 'quote');
    }

    /**
     * Handle a failed authorization attempt
     */
    protected function failedAuthorization()
    {
        if (SecurityService::checkRateLimit($this, 'quote')) {
            $remaining = SecurityService::getRemainingAttempts($this, 'quote');
            abort(429, "Too many quote requests. Please try again later. Remaining attempts: {$remaining}");
        }

        parent::failedAuthorization();
    }
}
