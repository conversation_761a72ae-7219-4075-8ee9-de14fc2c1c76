<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MetaTag extends Model
{
    use HasFactory;

    protected $fillable = [
        'page_type',
        'page_identifier',
        'meta_title',
        'meta_description',
        'meta_keywords',
        'og_title',
        'og_description',
        'og_image',
        'og_type',
        'structured_data'
    ];

    protected $casts = [
        'structured_data' => 'array'
    ];

    /**
     * Get meta tags for a specific page.
     */
    public static function getForPage($pageType, $pageIdentifier = null)
    {
        return self::where('page_type', $pageType)
                  ->where('page_identifier', $pageIdentifier)
                  ->first();
    }

    /**
     * Set meta tags for a page.
     */
    public static function setForPage($pageType, $pageIdentifier, $data)
    {
        return self::updateOrCreate(
            [
                'page_type' => $pageType,
                'page_identifier' => $pageIdentifier
            ],
            $data
        );
    }

    /**
     * Get meta tags for home page.
     */
    public static function getForHome()
    {
        return self::getForPage('home');
    }

    /**
     * Get meta tags for service page.
     */
    public static function getForService($serviceSlug)
    {
        return self::getForPage('service', $serviceSlug);
    }

    /**
     * Get meta tags for blog post.
     */
    public static function getForBlogPost($postSlug)
    {
        return self::getForPage('blog', $postSlug);
    }

    /**
     * Get Open Graph image URL.
     */
    public function getOgImageUrlAttribute(): string
    {
        return $this->og_image
            ? asset('storage/' . $this->og_image)
            : asset('images/default-og-image.jpg');
    }

    /**
     * Generate structured data JSON-LD.
     */
    public function getStructuredDataJsonAttribute(): string
    {
        return $this->structured_data
            ? json_encode($this->structured_data, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE)
            : '';
    }

    /**
     * Get all meta tags as array for easy rendering.
     */
    public function toMetaArray(): array
    {
        return [
            'title' => $this->meta_title,
            'description' => $this->meta_description,
            'keywords' => $this->meta_keywords,
            'og:title' => $this->og_title ?: $this->meta_title,
            'og:description' => $this->og_description ?: $this->meta_description,
            'og:image' => $this->og_image_url,
            'og:type' => $this->og_type,
            'structured_data' => $this->structured_data_json
        ];
    }
}
