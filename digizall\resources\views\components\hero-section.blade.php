@props([
    'title' => '',
    'subtitle' => '',
    'description' => '',
    'backgroundImage' => '',
    'ctaText' => 'Get Started',
    'ctaLink' => '#',
    'secondaryCtaText' => '',
    'secondaryCtaLink' => '#',
    'showCta' => true,
    'height' => 'min-h-screen',
    'overlay' => true
])

<section class="relative {{ $height }} flex items-center justify-center overflow-hidden">
    <!-- Background Image -->
    @if($backgroundImage)
        <div class="absolute inset-0 z-0">
            <img src="{{ $backgroundImage }}" alt="Hero Background" class="w-full h-full object-cover">
        </div>
    @else
        <!-- Gradient Background -->
        <div class="absolute inset-0 z-0 bg-gradient-to-br from-primary-600 via-primary-700 to-secondary-800"></div>
    @endif

    <!-- Overlay -->
    @if($overlay)
        <div class="absolute inset-0 z-10 bg-black bg-opacity-50"></div>
    @endif

    <!-- Animated Background Elements -->
    <div class="absolute inset-0 z-5">
        <div class="absolute top-20 left-10 w-20 h-20 bg-primary-400 rounded-full opacity-20 animate-bounce-slow"></div>
        <div class="absolute top-40 right-20 w-16 h-16 bg-secondary-400 rounded-full opacity-20 animate-pulse"></div>
        <div class="absolute bottom-32 left-1/4 w-12 h-12 bg-primary-300 rounded-full opacity-20 animate-bounce-slow" style="animation-delay: 1s;"></div>
        <div class="absolute bottom-20 right-1/3 w-14 h-14 bg-secondary-300 rounded-full opacity-20 animate-pulse" style="animation-delay: 2s;"></div>
    </div>

    <!-- Content -->
    <div class="relative z-20 container-custom text-center text-white">
        <div class="max-w-4xl mx-auto">
            @if($subtitle)
                <div class="mb-4" data-aos="fade-up">
                    <span class="inline-block px-4 py-2 bg-primary-600 bg-opacity-80 rounded-full text-sm font-medium">
                        {{ $subtitle }}
                    </span>
                </div>
            @endif

            @if($title)
                <h1 class="heading-primary text-white mb-6" data-aos="fade-up" data-aos-delay="100">
                    {!! $title !!}
                </h1>
            @endif

            @if($description)
                <p class="text-xl text-gray-200 mb-8 leading-relaxed max-w-3xl mx-auto" data-aos="fade-up" data-aos-delay="200">
                    {{ $description }}
                </p>
            @endif

            @if($showCta)
                <div class="flex flex-col sm:flex-row gap-4 justify-center items-center" data-aos="fade-up" data-aos-delay="300">
                    <a href="{{ $ctaLink }}" class="btn-primary bg-white text-primary-600 hover:bg-gray-100 hover:text-primary-700 px-8 py-4 text-lg font-semibold">
                        {{ $ctaText }}
                        <i class="fas fa-arrow-right ml-2"></i>
                    </a>
                    
                    @if($secondaryCtaText)
                        <a href="{{ $secondaryCtaLink }}" class="btn-secondary border-2 border-white text-white hover:bg-white hover:text-primary-600 px-8 py-4 text-lg font-semibold">
                            {{ $secondaryCtaText }}
                        </a>
                    @endif
                </div>
            @endif

            <!-- Additional Content Slot -->
            {{ $slot }}
        </div>
    </div>

    <!-- Scroll Down Indicator -->
    <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20 animate-bounce">
        <div class="w-6 h-10 border-2 border-white rounded-full flex justify-center">
            <div class="w-1 h-3 bg-white rounded-full mt-2 animate-pulse"></div>
        </div>
    </div>
</section>
