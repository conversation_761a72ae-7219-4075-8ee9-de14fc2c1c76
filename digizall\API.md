# DIGIZALL API Documentation

This document provides comprehensive information about the DIGIZALL application's internal APIs and endpoints.

## 🔐 Authentication

### Admin Authentication
All admin endpoints require authentication and admin privileges.

```php
// Middleware: 'auth', 'admin'
// Headers: X-CSRF-TOKEN (for web requests)
```

### Employee Authentication
Attendance portal endpoints require employee authentication.

```php
// Middleware: 'auth'
// Headers: X-CSRF-TOKEN (for web requests)
```

## 📊 Admin API Endpoints

### Dashboard
```http
GET /admin
```
Returns admin dashboard with statistics and recent activities.

**Response:**
```json
{
    "quotes_count": 25,
    "contacts_count": 15,
    "pending_quotes": 8,
    "recent_quotes": [...],
    "recent_contacts": [...]
}
```

### Quote Management

#### Get All Quotes
```http
GET /admin/quotes
```

**Query Parameters:**
- `status` (optional): Filter by status (pending, in_progress, completed, cancelled)
- `search` (optional): Search in name, email, or company
- `page` (optional): Pagination page number

**Response:**
```json
{
    "data": [
        {
            "id": 1,
            "name": "<PERSON>e",
            "email": "<EMAIL>",
            "company": "Example Corp",
            "service": "web-development",
            "budget": "5000-10000",
            "status": "pending",
            "created_at": "2025-01-01T00:00:00Z"
        }
    ],
    "meta": {
        "current_page": 1,
        "total": 25,
        "per_page": 15
    }
}
```

#### Get Quote Details
```http
GET /admin/quotes/{id}
```

**Response:**
```json
{
    "id": 1,
    "name": "John Doe",
    "email": "<EMAIL>",
    "phone": "+1234567890",
    "company": "Example Corp",
    "website": "https://example.com",
    "service": "web-development",
    "project_type": "new-project",
    "budget": "5000-10000",
    "timeline": "2-3-months",
    "description": "Project description...",
    "features": ["responsive-design", "cms-integration"],
    "additional_info": "Additional information...",
    "preferred_contact": "email",
    "status": "pending",
    "admin_notes": null,
    "created_at": "2025-01-01T00:00:00Z",
    "updated_at": "2025-01-01T00:00:00Z"
}
```

#### Update Quote Status
```http
POST /admin/quotes/{id}/status
```

**Request Body:**
```json
{
    "status": "in_progress",
    "notes": "Started working on the project"
}
```

**Response:**
```json
{
    "success": true,
    "message": "Quote status updated successfully"
}
```

#### Export Quotes
```http
GET /admin/quotes/export
```

**Query Parameters:**
- `format` (optional): csv, xlsx (default: csv)
- `status` (optional): Filter by status
- `date_from` (optional): Start date (Y-m-d)
- `date_to` (optional): End date (Y-m-d)

**Response:** File download

### Contact Management

#### Get All Contacts
```http
GET /admin/contacts
```

**Query Parameters:**
- `status` (optional): Filter by status (pending, replied, resolved)
- `search` (optional): Search in name, email, or subject
- `page` (optional): Pagination page number

#### Get Contact Details
```http
GET /admin/contacts/{id}
```

#### Update Contact Status
```http
POST /admin/contacts/{id}/status
```

**Request Body:**
```json
{
    "status": "replied",
    "notes": "Replied via email"
}
```

#### Add Notes to Contact
```http
POST /admin/contacts/{id}/notes
```

**Request Body:**
```json
{
    "notes": "Additional notes about this contact"
}
```

### Performance Monitoring

#### Get Performance Metrics
```http
GET /admin/performance/metrics
```

**Response:**
```json
{
    "recent_requests": [
        {
            "url": "/",
            "duration_ms": 250,
            "memory_mb": 15.5,
            "query_count": 8,
            "timestamp": "2025-01-01T12:00:00Z"
        }
    ],
    "daily_summary": {
        "avg_duration_ms": 300,
        "avg_memory_mb": 18.2,
        "total_requests": 1250
    },
    "system_resources": {
        "memory_usage": {
            "current_mb": 512,
            "limit_mb": 2048
        },
        "disk_usage": {
            "used_percentage": 65
        }
    }
}
```

#### Run Performance Optimization
```http
POST /admin/performance/optimize
```

**Request Body:**
```json
{
    "type": "cache" // Options: cache, database, images, all
}
```

**Response:**
```json
{
    "success": true,
    "message": "Performance optimization completed successfully",
    "output": "Optimization details..."
}
```

#### Get Performance Trends
```http
GET /admin/performance/trends
```

**Response:**
```json
{
    "trends": [
        {
            "date": "2025-01-01",
            "avg_duration_ms": 280,
            "avg_memory_mb": 16.8,
            "request_count": 1200
        }
    ]
}
```

## 👥 Attendance API Endpoints

### Employee Dashboard
```http
GET /app
```
Returns employee dashboard with attendance summary.

### Check In/Out

#### Check In
```http
POST /app/check-in
```

**Request Body:**
```json
{
    "latitude": 40.7128,
    "longitude": -74.0060,
    "notes": "Starting work day"
}
```

**Response:**
```json
{
    "success": true,
    "message": "Checked in successfully",
    "attendance": {
        "id": 123,
        "check_in_time": "2025-01-01T09:00:00Z",
        "location": "Office"
    }
}
```

#### Check Out
```http
POST /app/check-out
```

**Request Body:**
```json
{
    "latitude": 40.7128,
    "longitude": -74.0060,
    "notes": "End of work day"
}
```

### Attendance History
```http
GET /app/attendance
```

**Query Parameters:**
- `month` (optional): Filter by month (Y-m)
- `page` (optional): Pagination page number

**Response:**
```json
{
    "data": [
        {
            "id": 123,
            "date": "2025-01-01",
            "check_in_time": "09:00:00",
            "check_out_time": "17:30:00",
            "total_hours": "8.5",
            "status": "present",
            "notes": "Regular work day"
        }
    ],
    "summary": {
        "total_days": 22,
        "present_days": 20,
        "absent_days": 2,
        "total_hours": "160.5"
    }
}
```

### Leave Requests

#### Submit Leave Request
```http
POST /app/leave-requests
```

**Request Body:**
```json
{
    "type": "vacation",
    "start_date": "2025-01-15",
    "end_date": "2025-01-17",
    "reason": "Family vacation",
    "half_day": false
}
```

#### Get Leave Requests
```http
GET /app/leave-requests
```

**Response:**
```json
{
    "data": [
        {
            "id": 1,
            "type": "vacation",
            "start_date": "2025-01-15",
            "end_date": "2025-01-17",
            "days": 3,
            "status": "pending",
            "reason": "Family vacation",
            "submitted_at": "2025-01-01T10:00:00Z"
        }
    ]
}
```

## 🌐 Public API Endpoints

### Contact Form Submission
```http
POST /contact
```

**Request Body:**
```json
{
    "name": "John Doe",
    "email": "<EMAIL>",
    "phone": "+1234567890",
    "subject": "Inquiry about services",
    "message": "I'm interested in your web development services...",
    "preferred_contact": "email",
    "g-recaptcha-response": "recaptcha_token"
}
```

**Response:**
```json
{
    "success": true,
    "message": "Thank you for your message. We'll get back to you soon!"
}
```

### Quote Request Submission
```http
POST /quote
```

**Request Body:**
```json
{
    "name": "John Doe",
    "email": "<EMAIL>",
    "phone": "+1234567890",
    "company": "Example Corp",
    "website": "https://example.com",
    "service": "web-development",
    "project_type": "new-project",
    "budget": "5000-10000",
    "timeline": "2-3-months",
    "description": "Project description...",
    "features": ["responsive-design", "cms-integration"],
    "additional_info": "Additional information...",
    "preferred_contact": "email",
    "marketing_consent": true,
    "g-recaptcha-response": "recaptcha_token"
}
```

### SEO Endpoints

#### Sitemap
```http
GET /sitemap.xml
```
Returns XML sitemap for search engines.

#### Robots.txt
```http
GET /robots.txt
```
Returns robots.txt file for search engine crawlers.

## 🔧 Utility Endpoints

### Health Check
```http
GET /health
```

**Response:**
```json
{
    "status": "ok",
    "timestamp": "2025-01-01T12:00:00Z",
    "version": "1.0.0",
    "database": "connected",
    "cache": "active"
}
```

### Asset Optimization Status
```http
GET /admin/assets/stats
```

**Response:**
```json
{
    "css_files": 5,
    "js_files": 8,
    "image_files": 25,
    "webp_files": 20,
    "total_size_mb": 15.8,
    "optimization_date": "2025-01-01T10:00:00Z"
}
```

## 📝 Request/Response Format

### Standard Response Format
```json
{
    "success": true,
    "message": "Operation completed successfully",
    "data": {...},
    "meta": {
        "timestamp": "2025-01-01T12:00:00Z",
        "version": "1.0.0"
    }
}
```

### Error Response Format
```json
{
    "success": false,
    "message": "Error description",
    "errors": {
        "field_name": ["Validation error message"]
    },
    "code": 422
}
```

### Pagination Format
```json
{
    "data": [...],
    "meta": {
        "current_page": 1,
        "from": 1,
        "last_page": 5,
        "per_page": 15,
        "to": 15,
        "total": 75
    },
    "links": {
        "first": "?page=1",
        "last": "?page=5",
        "prev": null,
        "next": "?page=2"
    }
}
```

## 🚨 Error Codes

- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `422` - Validation Error
- `429` - Too Many Requests
- `500` - Internal Server Error

## 🔒 Security Considerations

### Rate Limiting
- Contact forms: 3 requests per hour per IP
- Quote forms: 2 requests per hour per IP
- Login attempts: 5 attempts per 15 minutes per IP
- API endpoints: 60 requests per minute per IP

### CSRF Protection
All POST, PUT, DELETE requests require CSRF token in header:
```
X-CSRF-TOKEN: csrf_token_value
```

### Input Validation
All inputs are validated and sanitized:
- XSS prevention
- SQL injection protection
- File upload security
- Input length limits

---

For API support: <EMAIL>
