<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use App\Services\SecurityService;

class SecurityTest extends TestCase
{
    use RefreshDatabase;

    public function test_security_headers_are_present()
    {
        $response = $this->get('/');

        $response->assertHeader('X-Content-Type-Options', 'nosniff');
        $response->assertHeader('X-Frame-Options', 'SAMEORIGIN');
        $response->assertHeader('X-XSS-Protection', '1; mode=block');
        $response->assertHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
    }

    public function test_csrf_protection_is_enabled()
    {
        $response = $this->post('/quote', [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'service' => 'web-development',
            'budget' => '5000-10000',
            'timeline' => '2-3-months',
            'project_type' => 'new-project',
            'description' => 'Test description',
            'preferred_contact' => 'email',
        ]);

        $response->assertStatus(419); // CSRF token mismatch
    }

    public function test_sql_injection_detection()
    {
        $maliciousInput = [
            'name' => "'; DROP TABLE users; --",
            'email' => '<EMAIL>',
            'message' => 'SELECT * FROM users WHERE id = 1',
        ];

        $threats = SecurityService::detectSuspiciousInput($maliciousInput);

        $this->assertNotEmpty($threats);
        $this->assertEquals('sql_injection', $threats[0]['type']);
    }

    public function test_xss_detection()
    {
        $maliciousInput = [
            'name' => '<script>alert("XSS")</script>',
            'message' => 'javascript:alert("XSS")',
            'description' => '<iframe src="javascript:alert(1)"></iframe>',
        ];

        $threats = SecurityService::detectSuspiciousInput($maliciousInput);

        $this->assertNotEmpty($threats);
        $xssThreats = array_filter($threats, function($threat) {
            return $threat['type'] === 'xss';
        });
        $this->assertNotEmpty($xssThreats);
    }

    public function test_input_sanitization()
    {
        $dirtyInput = [
            'name' => '<script>alert("test")</script>John Doe',
            'email' => '<EMAIL><script>',
            'message' => 'Hello <b>world</b>!',
        ];

        $sanitized = SecurityService::sanitizeInput($dirtyInput);

        $this->assertStringNotContainsString('<script>', $sanitized['name']);
        $this->assertStringNotContainsString('<script>', $sanitized['email']);
        $this->assertStringNotContainsString('<b>', $sanitized['message']);
    }

    public function test_password_strength_validation()
    {
        // Weak password
        $weakResult = SecurityService::validatePasswordStrength('123456');
        $this->assertFalse($weakResult['valid']);
        $this->assertEquals('weak', $weakResult['strength']);

        // Strong password
        $strongResult = SecurityService::validatePasswordStrength('MyStr0ng!Password');
        $this->assertTrue($strongResult['valid']);
        $this->assertEquals('strong', $strongResult['strength']);
    }

    public function test_file_upload_validation()
    {
        // This would require creating mock uploaded files
        // For now, we'll test the validation logic directly
        $this->assertTrue(true); // Placeholder
    }

    public function test_rate_limiting_blocks_excessive_requests()
    {
        // Simulate multiple requests from same IP
        for ($i = 0; $i < 6; $i++) {
            $response = $this->withSession(['_token' => csrf_token()])
                             ->post('/quote', [
                                 'name' => 'Test User',
                                 'email' => '<EMAIL>',
                                 'service' => 'web-development',
                                 'budget' => '5000-10000',
                                 'timeline' => '2-3-months',
                                 'project_type' => 'new-project',
                                 'description' => 'Test description',
                                 'preferred_contact' => 'email',
                             ]);
        }

        // The last request should be rate limited
        $this->assertTrue($response->status() === 429 || $response->status() === 403);
    }

    public function test_suspicious_ip_detection()
    {
        $suspiciousIPs = [
            '***********',  // Private network
            '********',     // Private network
            '**********',   // Private network
        ];

        foreach ($suspiciousIPs as $ip) {
            $result = SecurityService::checkSuspiciousIP($ip);
            $this->assertTrue($result['suspicious']);
        }

        // Test legitimate IP
        $legitimateResult = SecurityService::checkSuspiciousIP('*******');
        $this->assertFalse($legitimateResult['suspicious']);
    }

    public function test_filename_sanitization()
    {
        $dangerousFilenames = [
            '../../../etc/passwd',
            'test<script>.txt',
            'file with spaces.exe',
            '...hidden.file',
        ];

        foreach ($dangerousFilenames as $filename) {
            $sanitized = SecurityService::sanitizeFilename($filename);

            $this->assertStringNotContainsString('..', $sanitized);
            $this->assertStringNotContainsString('<', $sanitized);
            $this->assertStringNotContainsString('>', $sanitized);
            $this->assertStringNotContainsString('/', $sanitized);
        }
    }

    public function test_secure_password_generation()
    {
        $password = SecurityService::generateSecurePassword(12);

        $this->assertEquals(12, strlen($password));

        $validation = SecurityService::validatePasswordStrength($password);
        $this->assertTrue($validation['valid']);
        $this->assertContains($validation['strength'], ['medium', 'strong']);
    }

    public function test_admin_routes_require_authentication()
    {
        $adminRoutes = [
            '/admin',
            '/admin/quotes',
            '/admin/contacts',
            '/admin/performance',
        ];

        foreach ($adminRoutes as $route) {
            $response = $this->get($route);
            $this->assertEquals(302, $response->status(), "Route {$route} should redirect unauthenticated users");
        }
    }

    public function test_sensitive_data_is_not_exposed_in_errors()
    {
        // Test that database credentials, API keys, etc. are not exposed
        $response = $this->get('/non-existent-route');

        $content = $response->getContent();
        $this->assertStringNotContainsString('password', strtolower($content));
        $this->assertStringNotContainsString('secret', strtolower($content));
        $this->assertStringNotContainsString('key', strtolower($content));
    }
}
