<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Performance Monitoring Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration settings for application performance monitoring and
    | optimization features.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Monitoring Settings
    |--------------------------------------------------------------------------
    |
    | Enable or disable various performance monitoring features.
    |
    */

    'monitoring' => [
        'enabled' => env('PERFORMANCE_MONITORING_ENABLED', true),
        'log_slow_queries' => env('PERFORMANCE_LOG_SLOW_QUERIES', true),
        'log_memory_usage' => env('PERFORMANCE_LOG_MEMORY_USAGE', true),
        'log_execution_time' => env('PERFORMANCE_LOG_EXECUTION_TIME', true),
        'track_user_interactions' => env('PERFORMANCE_TRACK_USER_INTERACTIONS', false),
    ],

    /*
    |--------------------------------------------------------------------------
    | Performance Thresholds
    |--------------------------------------------------------------------------
    |
    | Define thresholds for various performance metrics.
    |
    */

    'thresholds' => [
        'slow_query_time' => env('PERFORMANCE_SLOW_QUERY_TIME', 1000), // milliseconds
        'slow_request_time' => env('PERFORMANCE_SLOW_REQUEST_TIME', 2000), // milliseconds
        'high_memory_usage' => env('PERFORMANCE_HIGH_MEMORY_USAGE', 128), // MB
        'max_execution_time' => env('PERFORMANCE_MAX_EXECUTION_TIME', 30), // seconds
        'cpu_usage_threshold' => env('PERFORMANCE_CPU_THRESHOLD', 80), // percentage
    ],

    /*
    |--------------------------------------------------------------------------
    | Database Performance
    |--------------------------------------------------------------------------
    |
    | Settings for database performance monitoring and optimization.
    |
    */

    'database' => [
        'log_queries' => env('PERFORMANCE_DB_LOG_QUERIES', false),
        'log_slow_queries_only' => env('PERFORMANCE_DB_LOG_SLOW_ONLY', true),
        'query_cache_enabled' => env('PERFORMANCE_DB_QUERY_CACHE', true),
        'connection_pooling' => env('PERFORMANCE_DB_CONNECTION_POOLING', false),
        'optimize_queries' => env('PERFORMANCE_DB_OPTIMIZE_QUERIES', true),
        'index_recommendations' => env('PERFORMANCE_DB_INDEX_RECOMMENDATIONS', false),
    ],

    /*
    |--------------------------------------------------------------------------
    | Caching Configuration
    |--------------------------------------------------------------------------
    |
    | Performance-related caching settings.
    |
    */

    'caching' => [
        'page_cache_enabled' => env('PERFORMANCE_PAGE_CACHE_ENABLED', false),
        'page_cache_ttl' => env('PERFORMANCE_PAGE_CACHE_TTL', 3600), // seconds
        'query_cache_enabled' => env('PERFORMANCE_QUERY_CACHE_ENABLED', true),
        'query_cache_ttl' => env('PERFORMANCE_QUERY_CACHE_TTL', 1800), // seconds
        'view_cache_enabled' => env('PERFORMANCE_VIEW_CACHE_ENABLED', true),
        'route_cache_enabled' => env('PERFORMANCE_ROUTE_CACHE_ENABLED', true),
        'config_cache_enabled' => env('PERFORMANCE_CONFIG_CACHE_ENABLED', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Asset Optimization
    |--------------------------------------------------------------------------
    |
    | Settings for optimizing CSS, JavaScript, and image assets.
    |
    */

    'assets' => [
        'minify_css' => env('PERFORMANCE_MINIFY_CSS', true),
        'minify_js' => env('PERFORMANCE_MINIFY_JS', true),
        'combine_css' => env('PERFORMANCE_COMBINE_CSS', false),
        'combine_js' => env('PERFORMANCE_COMBINE_JS', false),
        'optimize_images' => env('PERFORMANCE_OPTIMIZE_IMAGES', true),
        'lazy_load_images' => env('PERFORMANCE_LAZY_LOAD_IMAGES', true),
        'webp_conversion' => env('PERFORMANCE_WEBP_CONVERSION', false),
        'cdn_enabled' => env('PERFORMANCE_CDN_ENABLED', false),
        'cdn_url' => env('PERFORMANCE_CDN_URL', ''),
    ],

    /*
    |--------------------------------------------------------------------------
    | Memory Management
    |--------------------------------------------------------------------------
    |
    | Settings for memory usage monitoring and optimization.
    |
    */

    'memory' => [
        'track_usage' => env('PERFORMANCE_TRACK_MEMORY_USAGE', true),
        'log_peak_usage' => env('PERFORMANCE_LOG_PEAK_MEMORY', true),
        'garbage_collection_enabled' => env('PERFORMANCE_GARBAGE_COLLECTION', true),
        'memory_limit_warning' => env('PERFORMANCE_MEMORY_LIMIT_WARNING', 80), // percentage
        'auto_cleanup_enabled' => env('PERFORMANCE_AUTO_CLEANUP', true),
        'cleanup_interval' => env('PERFORMANCE_CLEANUP_INTERVAL', 3600), // seconds
    ],

    /*
    |--------------------------------------------------------------------------
    | Response Time Optimization
    |--------------------------------------------------------------------------
    |
    | Settings for optimizing response times.
    |
    */

    'response_time' => [
        'gzip_compression' => env('PERFORMANCE_GZIP_COMPRESSION', true),
        'browser_caching' => env('PERFORMANCE_BROWSER_CACHING', true),
        'etag_enabled' => env('PERFORMANCE_ETAG_ENABLED', true),
        'keep_alive_enabled' => env('PERFORMANCE_KEEP_ALIVE', true),
        'preload_critical_resources' => env('PERFORMANCE_PRELOAD_CRITICAL', false),
        'defer_non_critical_css' => env('PERFORMANCE_DEFER_NON_CRITICAL_CSS', false),
    ],

    /*
    |--------------------------------------------------------------------------
    | Monitoring Tools Integration
    |--------------------------------------------------------------------------
    |
    | Integration with external monitoring tools.
    |
    */

    'tools' => [
        'new_relic' => [
            'enabled' => env('PERFORMANCE_NEW_RELIC_ENABLED', false),
            'app_name' => env('NEW_RELIC_APP_NAME', 'DIGIZALL'),
            'license_key' => env('NEW_RELIC_LICENSE_KEY', ''),
        ],
        'datadog' => [
            'enabled' => env('PERFORMANCE_DATADOG_ENABLED', false),
            'api_key' => env('DATADOG_API_KEY', ''),
            'app_key' => env('DATADOG_APP_KEY', ''),
        ],
        'sentry' => [
            'enabled' => env('PERFORMANCE_SENTRY_ENABLED', false),
            'dsn' => env('SENTRY_LARAVEL_DSN', ''),
            'traces_sample_rate' => env('SENTRY_TRACES_SAMPLE_RATE', 0.1),
        ],
        'custom_metrics' => [
            'enabled' => env('PERFORMANCE_CUSTOM_METRICS_ENABLED', false),
            'endpoint' => env('PERFORMANCE_METRICS_ENDPOINT', ''),
            'api_key' => env('PERFORMANCE_METRICS_API_KEY', ''),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Alerting Configuration
    |--------------------------------------------------------------------------
    |
    | Settings for performance alerts and notifications.
    |
    */

    'alerts' => [
        'enabled' => env('PERFORMANCE_ALERTS_ENABLED', true),
        'email_notifications' => env('PERFORMANCE_EMAIL_ALERTS', true),
        'slack_notifications' => env('PERFORMANCE_SLACK_ALERTS', false),
        'webhook_notifications' => env('PERFORMANCE_WEBHOOK_ALERTS', false),
        'alert_recipients' => explode(',', env('PERFORMANCE_ALERT_RECIPIENTS', '<EMAIL>')),
        'alert_thresholds' => [
            'response_time' => env('PERFORMANCE_ALERT_RESPONSE_TIME', 5000), // milliseconds
            'error_rate' => env('PERFORMANCE_ALERT_ERROR_RATE', 5), // percentage
            'memory_usage' => env('PERFORMANCE_ALERT_MEMORY_USAGE', 90), // percentage
            'cpu_usage' => env('PERFORMANCE_ALERT_CPU_USAGE', 85), // percentage
            'disk_usage' => env('PERFORMANCE_ALERT_DISK_USAGE', 85), // percentage
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Reporting Configuration
    |--------------------------------------------------------------------------
    |
    | Settings for performance reports and analytics.
    |
    */

    'reporting' => [
        'generate_daily_reports' => env('PERFORMANCE_DAILY_REPORTS', true),
        'generate_weekly_reports' => env('PERFORMANCE_WEEKLY_REPORTS', true),
        'generate_monthly_reports' => env('PERFORMANCE_MONTHLY_REPORTS', true),
        'email_reports' => env('PERFORMANCE_EMAIL_REPORTS', false),
        'report_recipients' => explode(',', env('PERFORMANCE_REPORT_RECIPIENTS', '<EMAIL>')),
        'include_charts' => env('PERFORMANCE_REPORTS_INCLUDE_CHARTS', true),
        'retention_days' => env('PERFORMANCE_REPORTS_RETENTION_DAYS', 90),
    ],

    /*
    |--------------------------------------------------------------------------
    | Optimization Recommendations
    |--------------------------------------------------------------------------
    |
    | Settings for automatic optimization recommendations.
    |
    */

    'recommendations' => [
        'enabled' => env('PERFORMANCE_RECOMMENDATIONS_ENABLED', true),
        'auto_apply_safe_optimizations' => env('PERFORMANCE_AUTO_APPLY_OPTIMIZATIONS', false),
        'recommendation_frequency' => env('PERFORMANCE_RECOMMENDATION_FREQUENCY', 'weekly'),
        'include_database_recommendations' => env('PERFORMANCE_DB_RECOMMENDATIONS', true),
        'include_code_recommendations' => env('PERFORMANCE_CODE_RECOMMENDATIONS', false),
        'include_infrastructure_recommendations' => env('PERFORMANCE_INFRA_RECOMMENDATIONS', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Load Testing Configuration
    |--------------------------------------------------------------------------
    |
    | Settings for load testing and stress testing.
    |
    */

    'load_testing' => [
        'enabled' => env('PERFORMANCE_LOAD_TESTING_ENABLED', false),
        'scheduled_tests' => env('PERFORMANCE_SCHEDULED_LOAD_TESTS', false),
        'test_frequency' => env('PERFORMANCE_LOAD_TEST_FREQUENCY', 'weekly'),
        'max_concurrent_users' => env('PERFORMANCE_MAX_CONCURRENT_USERS', 100),
        'test_duration' => env('PERFORMANCE_LOAD_TEST_DURATION', 300), // seconds
        'ramp_up_time' => env('PERFORMANCE_LOAD_TEST_RAMP_UP', 60), // seconds
    ],

    /*
    |--------------------------------------------------------------------------
    | Development Mode Settings
    |--------------------------------------------------------------------------
    |
    | Performance monitoring settings specific to development environment.
    |
    */

    'development' => [
        'debug_bar_enabled' => env('PERFORMANCE_DEBUG_BAR_ENABLED', false),
        'query_log_enabled' => env('PERFORMANCE_DEV_QUERY_LOG', true),
        'profiling_enabled' => env('PERFORMANCE_DEV_PROFILING', false),
        'memory_tracking_enabled' => env('PERFORMANCE_DEV_MEMORY_TRACKING', true),
        'route_timing_enabled' => env('PERFORMANCE_DEV_ROUTE_TIMING', true),
    ],

];
