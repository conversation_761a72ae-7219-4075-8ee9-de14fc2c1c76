<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use App\Models\Service;
use App\Models\BlogPost;
use App\Models\Testimonial;
use App\Models\Setting;

class CacheService
{
    const CACHE_TTL = 3600; // 1 hour
    const LONG_CACHE_TTL = 86400; // 24 hours

    /**
     * Get cached services with categories
     */
    public static function getServices()
    {
        return Cache::remember('services.all', self::CACHE_TTL, function () {
            return Service::with(['features', 'pricing'])
                          ->active()
                          ->orderBy('sort_order')
                          ->get();
        });
    }

    /**
     * Get cached featured services
     */
    public static function getFeaturedServices()
    {
        return Cache::remember('services.featured', self::CACHE_TTL, function () {
            return Service::with(['features', 'pricing'])
                          ->active()
                          ->featured()
                          ->orderBy('sort_order')
                          ->limit(6)
                          ->get();
        });
    }

    /**
     * Get cached services by category
     */
    public static function getServicesByCategory()
    {
        return Cache::remember('services.by_category', self::CACHE_TTL, function () {
            return Service::active()
                          ->orderBy('category')
                          ->orderBy('sort_order')
                          ->get()
                          ->groupBy('category');
        });
    }

    /**
     * Get cached blog posts
     */
    public static function getBlogPosts($limit = 10)
    {
        $cacheKey = "blog.posts.{$limit}";
        
        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($limit) {
            return BlogPost::with(['category', 'author', 'tags'])
                           ->published()
                           ->orderBy('published_at', 'desc')
                           ->limit($limit)
                           ->get();
        });
    }

    /**
     * Get cached featured blog posts
     */
    public static function getFeaturedBlogPosts($limit = 3)
    {
        $cacheKey = "blog.featured.{$limit}";
        
        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($limit) {
            return BlogPost::with(['category', 'author'])
                           ->published()
                           ->featured()
                           ->orderBy('published_at', 'desc')
                           ->limit($limit)
                           ->get();
        });
    }

    /**
     * Get cached testimonials
     */
    public static function getTestimonials()
    {
        return Cache::remember('testimonials.all', self::CACHE_TTL, function () {
            return Testimonial::active()
                              ->orderBy('sort_order')
                              ->get();
        });
    }

    /**
     * Get cached featured testimonials
     */
    public static function getFeaturedTestimonials()
    {
        return Cache::remember('testimonials.featured', self::CACHE_TTL, function () {
            return Testimonial::active()
                              ->featured()
                              ->orderBy('sort_order')
                              ->get();
        });
    }

    /**
     * Get cached settings
     */
    public static function getSettings()
    {
        return Cache::remember('settings.all', self::LONG_CACHE_TTL, function () {
            return Setting::all()->pluck('value', 'key');
        });
    }

    /**
     * Get cached setting by key
     */
    public static function getSetting($key, $default = null)
    {
        $settings = self::getSettings();
        return $settings->get($key, $default);
    }

    /**
     * Get cached statistics
     */
    public static function getStatistics()
    {
        return Cache::remember('statistics.dashboard', self::CACHE_TTL, function () {
            return [
                'total_services' => Service::active()->count(),
                'total_blog_posts' => BlogPost::published()->count(),
                'total_testimonials' => Testimonial::active()->count(),
                'projects_completed' => self::getSetting('projects_completed', 150),
                'happy_clients' => self::getSetting('happy_clients', 120),
                'years_experience' => self::getSetting('years_experience', 8),
                'team_members' => self::getSetting('team_members', 25),
            ];
        });
    }

    /**
     * Clear all caches
     */
    public static function clearAll()
    {
        $tags = [
            'services.all',
            'services.featured',
            'services.by_category',
            'blog.posts.*',
            'blog.featured.*',
            'testimonials.all',
            'testimonials.featured',
            'settings.all',
            'statistics.dashboard',
        ];

        foreach ($tags as $tag) {
            if (str_contains($tag, '*')) {
                // Clear pattern-based cache keys
                $pattern = str_replace('*', '', $tag);
                Cache::flush(); // For simplicity, flush all cache
            } else {
                Cache::forget($tag);
            }
        }
    }

    /**
     * Clear service-related caches
     */
    public static function clearServiceCaches()
    {
        Cache::forget('services.all');
        Cache::forget('services.featured');
        Cache::forget('services.by_category');
        Cache::forget('statistics.dashboard');
    }

    /**
     * Clear blog-related caches
     */
    public static function clearBlogCaches()
    {
        // Clear all blog cache keys
        for ($i = 1; $i <= 20; $i++) {
            Cache::forget("blog.posts.{$i}");
            Cache::forget("blog.featured.{$i}");
        }
        Cache::forget('statistics.dashboard');
    }

    /**
     * Clear testimonial-related caches
     */
    public static function clearTestimonialCaches()
    {
        Cache::forget('testimonials.all');
        Cache::forget('testimonials.featured');
        Cache::forget('statistics.dashboard');
    }

    /**
     * Clear settings cache
     */
    public static function clearSettingsCache()
    {
        Cache::forget('settings.all');
        Cache::forget('statistics.dashboard');
    }

    /**
     * Warm up caches
     */
    public static function warmUp()
    {
        // Warm up frequently accessed data
        self::getServices();
        self::getFeaturedServices();
        self::getServicesByCategory();
        self::getBlogPosts();
        self::getFeaturedBlogPosts();
        self::getTestimonials();
        self::getFeaturedTestimonials();
        self::getSettings();
        self::getStatistics();
    }

    /**
     * Get cache statistics
     */
    public static function getStats()
    {
        $keys = [
            'services.all',
            'services.featured',
            'services.by_category',
            'blog.posts.10',
            'blog.featured.3',
            'testimonials.all',
            'testimonials.featured',
            'settings.all',
            'statistics.dashboard',
        ];

        $stats = [];
        foreach ($keys as $key) {
            $stats[$key] = Cache::has($key);
        }

        return $stats;
    }
}
