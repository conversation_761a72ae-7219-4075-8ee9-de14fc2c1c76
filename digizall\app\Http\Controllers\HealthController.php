<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;
use App\Services\PerformanceMonitoringService;
use App\Services\DatabaseOptimizationService;
use App\Services\AssetOptimizationService;

class HealthController extends Controller
{
    /**
     * System health check endpoint
     */
    public function index()
    {
        $health = [
            'status' => 'ok',
            'timestamp' => now()->toISOString(),
            'version' => config('app.version', '1.0.0'),
            'environment' => config('app.env'),
            'checks' => [
                'database' => $this->checkDatabase(),
                'cache' => $this->checkCache(),
                'storage' => $this->checkStorage(),
                'queue' => $this->checkQueue(),
                'mail' => $this->checkMail(),
            ]
        ];

        // Determine overall status
        $allHealthy = collect($health['checks'])->every(function ($check) {
            return $check['status'] === 'ok';
        });

        $health['status'] = $allHealthy ? 'ok' : 'degraded';

        $statusCode = $allHealthy ? 200 : 503;

        return response()->json($health, $statusCode);
    }

    /**
     * Detailed system status
     */
    public function status()
    {
        $status = [
            'application' => [
                'name' => config('app.name'),
                'version' => config('app.version', '1.0.0'),
                'environment' => config('app.env'),
                'debug' => config('app.debug'),
                'timezone' => config('app.timezone'),
                'locale' => config('app.locale'),
                'url' => config('app.url'),
            ],
            'system' => [
                'php_version' => PHP_VERSION,
                'laravel_version' => app()->version(),
                'server_time' => now()->toISOString(),
                'uptime' => $this->getSystemUptime(),
                'memory_usage' => $this->getMemoryUsage(),
                'disk_usage' => $this->getDiskUsage(),
            ],
            'database' => $this->getDatabaseStatus(),
            'cache' => $this->getCacheStatus(),
            'performance' => $this->getPerformanceMetrics(),
        ];

        return response()->json($status);
    }

    /**
     * Check database connectivity
     */
    private function checkDatabase(): array
    {
        try {
            $start = microtime(true);
            DB::connection()->getPdo();
            $responseTime = round((microtime(true) - $start) * 1000, 2);

            return [
                'status' => 'ok',
                'message' => 'Database connection successful',
                'response_time_ms' => $responseTime,
                'connection' => config('database.default'),
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'message' => 'Database connection failed',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Check cache system
     */
    private function checkCache(): array
    {
        try {
            $key = 'health_check_' . time();
            $value = 'test_value';

            $start = microtime(true);
            Cache::put($key, $value, 60);
            $retrieved = Cache::get($key);
            Cache::forget($key);
            $responseTime = round((microtime(true) - $start) * 1000, 2);

            if ($retrieved === $value) {
                return [
                    'status' => 'ok',
                    'message' => 'Cache system working',
                    'response_time_ms' => $responseTime,
                    'driver' => config('cache.default'),
                ];
            } else {
                return [
                    'status' => 'error',
                    'message' => 'Cache read/write failed',
                ];
            }
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'message' => 'Cache system error',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Check storage system
     */
    private function checkStorage(): array
    {
        try {
            $testFile = 'health_check_' . time() . '.txt';
            $testContent = 'Health check test';

            Storage::put($testFile, $testContent);
            $retrieved = Storage::get($testFile);
            Storage::delete($testFile);

            if ($retrieved === $testContent) {
                return [
                    'status' => 'ok',
                    'message' => 'Storage system working',
                    'driver' => config('filesystems.default'),
                ];
            } else {
                return [
                    'status' => 'error',
                    'message' => 'Storage read/write failed',
                ];
            }
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'message' => 'Storage system error',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Check queue system
     */
    private function checkQueue(): array
    {
        try {
            $driver = config('queue.default');
            
            return [
                'status' => 'ok',
                'message' => 'Queue system configured',
                'driver' => $driver,
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'message' => 'Queue system error',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Check mail system
     */
    private function checkMail(): array
    {
        try {
            $mailer = config('mail.default');
            
            return [
                'status' => 'ok',
                'message' => 'Mail system configured',
                'mailer' => $mailer,
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'message' => 'Mail system error',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Get detailed database status
     */
    private function getDatabaseStatus(): array
    {
        try {
            $connection = DB::connection();
            $pdo = $connection->getPdo();
            
            return [
                'connection' => config('database.default'),
                'driver' => $pdo->getAttribute(\PDO::ATTR_DRIVER_NAME),
                'version' => $pdo->getAttribute(\PDO::ATTR_SERVER_VERSION),
                'health_score' => DatabaseOptimizationService::getHealthScore(),
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Get cache status details
     */
    private function getCacheStatus(): array
    {
        return [
            'driver' => config('cache.default'),
            'prefix' => config('cache.prefix'),
            'stats' => $this->getCacheStats(),
        ];
    }

    /**
     * Get performance metrics
     */
    private function getPerformanceMetrics(): array
    {
        return [
            'monitoring_enabled' => config('performance.monitoring.enabled', false),
            'recent_metrics' => PerformanceMonitoringService::getRecentMetrics(5),
            'daily_summary' => PerformanceMonitoringService::getDailySummary(),
        ];
    }

    /**
     * Get system uptime
     */
    private function getSystemUptime(): string
    {
        if (PHP_OS_FAMILY === 'Linux') {
            $uptime = shell_exec('uptime -p');
            return trim($uptime ?: 'Unknown');
        }
        
        return 'Unknown';
    }

    /**
     * Get memory usage
     */
    private function getMemoryUsage(): array
    {
        return [
            'current_mb' => round(memory_get_usage(true) / 1024 / 1024, 2),
            'peak_mb' => round(memory_get_peak_usage(true) / 1024 / 1024, 2),
            'limit' => ini_get('memory_limit'),
        ];
    }

    /**
     * Get disk usage
     */
    private function getDiskUsage(): array
    {
        $path = base_path();
        
        return [
            'total_gb' => round(disk_total_space($path) / 1024 / 1024 / 1024, 2),
            'free_gb' => round(disk_free_space($path) / 1024 / 1024 / 1024, 2),
            'used_percentage' => round((1 - disk_free_space($path) / disk_total_space($path)) * 100, 2),
        ];
    }

    /**
     * Get cache statistics
     */
    private function getCacheStats(): array
    {
        try {
            // This is a basic implementation - can be enhanced based on cache driver
            return [
                'hit_rate' => 'N/A',
                'miss_rate' => 'N/A',
                'keys_count' => 'N/A',
            ];
        } catch (\Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }
}
