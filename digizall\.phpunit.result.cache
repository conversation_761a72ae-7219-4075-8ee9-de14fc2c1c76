{"version": 1, "defects": {"Tests\\Feature\\HomePageTest::test_home_page_contains_required_sections": 7, "Tests\\Feature\\HomePageTest::test_home_page_displays_recent_blog_posts": 8, "Tests\\Feature\\HomePageTest::test_home_page_displays_testimonials": 8, "Tests\\Feature\\AttendanceTest::test_attendance_dashboard_requires_authentication": 8, "Tests\\Feature\\AttendanceTest::test_employee_can_access_attendance_dashboard": 8, "Tests\\Feature\\AttendanceTest::test_employee_can_check_in": 7, "Tests\\Feature\\AttendanceTest::test_employee_cannot_check_in_twice": 8, "Tests\\Feature\\AttendanceTest::test_employee_can_check_out": 8, "Tests\\Feature\\AttendanceTest::test_employee_cannot_check_out_without_check_in": 8, "Tests\\Feature\\AttendanceTest::test_employee_can_start_break": 8, "Tests\\Feature\\AttendanceTest::test_employee_can_end_break": 8, "Tests\\Feature\\AttendanceTest::test_attendance_calculates_total_hours": 8, "Tests\\Feature\\AttendanceTest::test_employee_status_api_returns_correct_data": 8, "Tests\\Feature\\AttendanceTest::test_non_employee_user_sees_admin_dashboard": 8, "Tests\\Feature\\AdminPanelTest::test_admin_dashboard_requires_authentication": 8, "Tests\\Feature\\AdminPanelTest::test_admin_dashboard_requires_admin_privileges": 8, "Tests\\Feature\\AdminPanelTest::test_admin_can_access_dashboard": 8, "Tests\\Feature\\AdminPanelTest::test_admin_dashboard_shows_statistics": 8, "Tests\\Feature\\AdminPanelTest::test_admin_can_view_quotes": 8, "Tests\\Feature\\AdminPanelTest::test_admin_can_view_quote_details": 8, "Tests\\Feature\\AdminPanelTest::test_admin_can_update_quote_status": 8, "Tests\\Feature\\AdminPanelTest::test_admin_can_view_contacts": 8, "Tests\\Feature\\AdminPanelTest::test_admin_can_export_quotes": 8, "Tests\\Feature\\AdminPanelTest::test_admin_can_access_performance_dashboard": 8, "Tests\\Feature\\AdminPanelTest::test_non_admin_cannot_access_admin_routes": 8, "Tests\\Feature\\AdminPanelTest::test_admin_middleware_blocks_unauthenticated_users": 8}, "times": {"Tests\\Feature\\HomePageTest::test_home_page_loads_successfully": 0.128, "Tests\\Feature\\HomePageTest::test_home_page_contains_required_sections": 0.061, "Tests\\Feature\\HomePageTest::test_home_page_displays_featured_services": 0.089, "Tests\\Feature\\HomePageTest::test_home_page_displays_recent_blog_posts": 0.019, "Tests\\Feature\\HomePageTest::test_home_page_displays_testimonials": 0.005, "Tests\\Feature\\HomePageTest::test_navigation_links_are_present": 0.06, "Tests\\Feature\\HomePageTest::test_cta_buttons_are_present": 0.087, "Tests\\Feature\\ExampleTest::test_the_application_returns_a_successful_response": 0.102, "Tests\\Feature\\AttendanceTest::test_attendance_dashboard_requires_authentication": 0.065, "Tests\\Feature\\AttendanceTest::test_employee_can_access_attendance_dashboard": 0.917, "Tests\\Feature\\AttendanceTest::test_employee_can_check_in": 0.033}}