<?php

namespace App\Http\Controllers\Attendance;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Employee;
use App\Models\Attendance;
use Carbon\Carbon;

class AttendanceController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Check in employee
     */
    public function checkIn(Request $request)
    {
        $user = auth()->user();
        $employee = Employee::where('user_id', $user->id)->first();

        if (!$employee) {
            return response()->json(['error' => 'Employee record not found'], 404);
        }

        $today = today();
        $attendance = Attendance::firstOrCreate(
            [
                'employee_id' => $employee->id,
                'date' => $today,
            ],
            [
                'status' => 'present',
            ]
        );

        if ($attendance->check_in) {
            return response()->json(['error' => 'Already checked in today'], 400);
        }

        $attendance->update([
            'check_in' => now(),
            'check_in_ip' => $request->ip(),
            'check_in_location' => $this->getLocationFromRequest($request),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Checked in successfully',
            'check_in_time' => $attendance->check_in->format('H:i'),
        ]);
    }

    /**
     * Check out employee
     */
    public function checkOut(Request $request)
    {
        $user = auth()->user();
        $employee = Employee::where('user_id', $user->id)->first();

        if (!$employee) {
            return response()->json(['error' => 'Employee record not found'], 404);
        }

        $attendance = $employee->todayAttendance;

        if (!$attendance || !$attendance->check_in) {
            return response()->json(['error' => 'No check-in record found for today'], 400);
        }

        if ($attendance->check_out) {
            return response()->json(['error' => 'Already checked out today'], 400);
        }

        $attendance->update([
            'check_out' => now(),
            'check_out_ip' => $request->ip(),
            'check_out_location' => $this->getLocationFromRequest($request),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Checked out successfully',
            'check_out_time' => $attendance->check_out->format('H:i'),
            'total_hours' => $attendance->formatted_total_hours,
        ]);
    }

    /**
     * Start break
     */
    public function startBreak(Request $request)
    {
        $user = auth()->user();
        $employee = Employee::where('user_id', $user->id)->first();

        if (!$employee) {
            return response()->json(['error' => 'Employee record not found'], 404);
        }

        $attendance = $employee->todayAttendance;

        if (!$attendance || !$attendance->check_in) {
            return response()->json(['error' => 'Please check in first'], 400);
        }

        if ($attendance->break_start) {
            return response()->json(['error' => 'Break already started'], 400);
        }

        $attendance->update([
            'break_start' => now(),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Break started',
            'break_start_time' => $attendance->break_start->format('H:i'),
        ]);
    }

    /**
     * End break
     */
    public function endBreak(Request $request)
    {
        $user = auth()->user();
        $employee = Employee::where('user_id', $user->id)->first();

        if (!$employee) {
            return response()->json(['error' => 'Employee record not found'], 404);
        }

        $attendance = $employee->todayAttendance;

        if (!$attendance || !$attendance->break_start) {
            return response()->json(['error' => 'No active break found'], 400);
        }

        if ($attendance->break_end) {
            return response()->json(['error' => 'Break already ended'], 400);
        }

        $attendance->update([
            'break_end' => now(),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Break ended',
            'break_end_time' => $attendance->break_end->format('H:i'),
            'break_duration' => $attendance->formatted_break_duration,
        ]);
    }

    /**
     * Get employee's attendance history
     */
    public function history(Request $request)
    {
        $user = auth()->user();
        $employee = Employee::where('user_id', $user->id)->first();

        if (!$employee) {
            return redirect()->route('attendance.dashboard')
                           ->with('error', 'Employee record not found');
        }

        $query = $employee->attendances()->orderBy('date', 'desc');

        // Filter by date range if provided
        if ($request->has('start_date') && $request->start_date) {
            $query->whereDate('date', '>=', $request->start_date);
        }

        if ($request->has('end_date') && $request->end_date) {
            $query->whereDate('date', '<=', $request->end_date);
        }

        // Filter by status if provided
        if ($request->has('status') && $request->status) {
            $query->where('status', $request->status);
        }

        $attendances = $query->paginate(15);

        return view('attendance.employee.history', compact('employee', 'attendances'));
    }

    /**
     * Get current status
     */
    public function status()
    {
        $user = auth()->user();
        $employee = Employee::where('user_id', $user->id)->first();

        if (!$employee) {
            return response()->json(['error' => 'Employee record not found'], 404);
        }

        $attendance = $employee->todayAttendance;

        return response()->json([
            'employee' => [
                'name' => $employee->full_name,
                'employee_id' => $employee->employee_id,
                'department' => $employee->department->name,
            ],
            'attendance' => $attendance ? [
                'date' => $attendance->date->format('Y-m-d'),
                'check_in' => $attendance->check_in?->format('H:i'),
                'check_out' => $attendance->check_out?->format('H:i'),
                'break_start' => $attendance->break_start?->format('H:i'),
                'break_end' => $attendance->break_end?->format('H:i'),
                'status' => $attendance->status,
                'is_checked_in' => $employee->is_checked_in,
                'is_on_break' => $attendance->is_on_break,
                'current_status' => $employee->current_status,
            ] : null,
        ]);
    }

    /**
     * Extract location from request (if available)
     */
    private function getLocationFromRequest(Request $request)
    {
        if ($request->has('latitude') && $request->has('longitude')) {
            return [
                'latitude' => $request->latitude,
                'longitude' => $request->longitude,
            ];
        }

        return null;
    }
}
