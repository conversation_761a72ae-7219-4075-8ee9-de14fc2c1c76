<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ContactMessage as Contact;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Response;

class ContactsController extends Controller
{
    public function __construct()
    {
        $this->middleware('admin');
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Contact::query();

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('subject', 'like', "%{$search}%")
                  ->orWhere('message', 'like', "%{$search}%");
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by preferred contact method
        if ($request->filled('preferred_contact')) {
            $query->where('preferred_contact', $request->preferred_contact);
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $contacts = $query->latest()->paginate(15);

        return view('admin.contacts.index', compact('contacts'));
    }

    /**
     * Display the specified resource.
     */
    public function show(Contact $contact)
    {
        // Mark as read when viewed
        if ($contact->status === 'pending') {
            $contact->update(['status' => 'read']);
        }

        return view('admin.contacts.show', compact('contact'));
    }

    /**
     * Update contact status
     */
    public function updateStatus(Request $request, Contact $contact)
    {
        $request->validate([
            'status' => 'required|in:pending,read,replied,resolved',
            'admin_notes' => 'nullable|string|max:1000',
        ]);

        $contact->update([
            'status' => $request->status,
            'admin_notes' => $request->admin_notes,
            'replied_at' => $request->status === 'replied' ? now() : $contact->replied_at,
        ]);

        return back()->with('success', 'Contact status updated successfully.');
    }

    /**
     * Add notes to contact
     */
    public function addNotes(Request $request, Contact $contact)
    {
        $request->validate([
            'notes' => 'required|string|max:1000',
        ]);

        $existingNotes = $contact->admin_notes ? $contact->admin_notes . "\n\n" : '';
        $newNotes = $existingNotes . '[' . now()->format('Y-m-d H:i') . '] ' . $request->notes;

        $contact->update(['admin_notes' => $newNotes]);

        return back()->with('success', 'Notes added successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Contact $contact)
    {
        $contact->delete();

        return redirect()->route('admin.contacts.index')
                        ->with('success', 'Contact deleted successfully.');
    }

    /**
     * Export contacts to CSV
     */
    public function export(Request $request)
    {
        $query = Contact::query();

        // Apply same filters as index
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $contacts = $query->latest()->get();

        $filename = 'contacts-export-' . now()->format('Y-m-d-H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($contacts) {
            $file = fopen('php://output', 'w');

            // CSV headers
            fputcsv($file, [
                'ID',
                'Name',
                'Email',
                'Phone',
                'Subject',
                'Message',
                'Preferred Contact',
                'Status',
                'Submitted At',
                'Admin Notes'
            ]);

            // CSV data
            foreach ($contacts as $contact) {
                fputcsv($file, [
                    $contact->id,
                    $contact->name,
                    $contact->email,
                    $contact->phone,
                    $contact->subject,
                    $contact->message,
                    $contact->preferred_contact,
                    $contact->status,
                    $contact->created_at->format('Y-m-d H:i:s'),
                    $contact->admin_notes
                ]);
            }

            fclose($file);
        };

        return Response::stream($callback, 200, $headers);
    }

    /**
     * Bulk actions
     */
    public function bulkAction(Request $request)
    {
        $request->validate([
            'action' => 'required|in:delete,mark_read,mark_replied,mark_resolved',
            'contacts' => 'required|array',
            'contacts.*' => 'exists:contacts,id',
        ]);

        $contacts = Contact::whereIn('id', $request->contacts);

        switch ($request->action) {
            case 'delete':
                $contacts->delete();
                $message = 'Selected contacts deleted successfully.';
                break;

            case 'mark_read':
                $contacts->update(['status' => 'read']);
                $message = 'Selected contacts marked as read.';
                break;

            case 'mark_replied':
                $contacts->update(['status' => 'replied', 'replied_at' => now()]);
                $message = 'Selected contacts marked as replied.';
                break;

            case 'mark_resolved':
                $contacts->update(['status' => 'resolved']);
                $message = 'Selected contacts marked as resolved.';
                break;
        }

        return back()->with('success', $message);
    }

    /**
     * Get contact statistics
     */
    public function getStats()
    {
        $stats = [
            'total' => Contact::count(),
            'pending' => Contact::where('status', 'pending')->count(),
            'read' => Contact::where('status', 'read')->count(),
            'replied' => Contact::where('status', 'replied')->count(),
            'resolved' => Contact::where('status', 'resolved')->count(),
            'today' => Contact::whereDate('created_at', today())->count(),
            'this_week' => Contact::whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()])->count(),
            'this_month' => Contact::whereMonth('created_at', now()->month)->count(),
        ];

        return response()->json($stats);
    }
}
