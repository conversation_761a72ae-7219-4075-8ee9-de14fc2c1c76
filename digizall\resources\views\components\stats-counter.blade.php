@props([
    'stats' => [],
    'background' => 'primary', // primary, secondary, white, transparent
    'layout' => 'horizontal' // horizontal, vertical
])

@php
    $bgClasses = match($background) {
        'primary' => 'bg-primary-600 text-white',
        'secondary' => 'bg-secondary-800 text-white',
        'white' => 'bg-white text-gray-900',
        'transparent' => 'bg-transparent text-white',
        default => 'bg-primary-600 text-white'
    };
@endphp

<section class="section-padding {{ $bgClasses }} relative overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-10">
        <div class="absolute top-10 left-10 w-20 h-20 border border-current rounded-full"></div>
        <div class="absolute top-32 right-20 w-16 h-16 border border-current rounded-full"></div>
        <div class="absolute bottom-20 left-1/4 w-12 h-12 border border-current rounded-full"></div>
        <div class="absolute bottom-32 right-1/3 w-14 h-14 border border-current rounded-full"></div>
    </div>

    <div class="container-custom relative z-10">
        <!-- Section Header -->
        <div class="text-center mb-16" data-aos="fade-up">
            <h2 class="heading-secondary mb-4">
                Our Achievements in Numbers
            </h2>
            <p class="text-lg opacity-90 max-w-2xl mx-auto">
                These numbers represent our commitment to delivering exceptional results for our clients worldwide.
            </p>
        </div>

        <!-- Stats Grid -->
        <div class="grid grid-cols-2 lg:grid-cols-4 gap-8">
            @foreach($stats as $index => $stat)
                <div class="text-center group" data-aos="fade-up" data-aos-delay="{{ $index * 100 }}">
                    <!-- Icon -->
                    @if(isset($stat['icon']))
                        <div class="w-16 h-16 mx-auto mb-4 bg-white bg-opacity-20 rounded-full flex items-center justify-center group-hover:bg-opacity-30 transition-all duration-300">
                            <i class="{{ $stat['icon'] }} text-2xl"></i>
                        </div>
                    @endif

                    <!-- Number -->
                    <div class="mb-2">
                        <span class="counter text-4xl lg:text-5xl font-bold block" 
                              data-target="{{ $stat['number'] }}">
                            0
                        </span>
                        @if(isset($stat['suffix']))
                            <span class="text-2xl font-bold">{{ $stat['suffix'] }}</span>
                        @endif
                    </div>

                    <!-- Label -->
                    <h3 class="text-lg font-semibold opacity-90">
                        {{ $stat['label'] }}
                    </h3>

                    <!-- Description -->
                    @if(isset($stat['description']))
                        <p class="text-sm opacity-75 mt-2">
                            {{ $stat['description'] }}
                        </p>
                    @endif
                </div>
            @endforeach
        </div>

        <!-- Additional Content -->
        @if(isset($slot) && !empty(trim($slot)))
            <div class="mt-16 text-center" data-aos="fade-up" data-aos-delay="400">
                {{ $slot }}
            </div>
        @endif
    </div>
</section>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Counter animation
    const counters = document.querySelectorAll('.counter');
    const observerOptions = {
        threshold: 0.5,
        rootMargin: '0px 0px -100px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const counter = entry.target;
                const target = parseInt(counter.getAttribute('data-target'));
                const duration = 2000; // 2 seconds
                const increment = target / (duration / 16); // 60fps
                let current = 0;

                const updateCounter = () => {
                    current += increment;
                    if (current < target) {
                        counter.textContent = Math.floor(current).toLocaleString();
                        requestAnimationFrame(updateCounter);
                    } else {
                        counter.textContent = target.toLocaleString();
                    }
                };

                updateCounter();
                observer.unobserve(counter);
            }
        });
    }, observerOptions);

    counters.forEach(counter => {
        observer.observe(counter);
    });
});
</script>
@endpush
