<?php

namespace App\Helpers;

use Illuminate\Support\Str;

class SEOHelper
{
    /**
     * Generate meta title with proper length and formatting
     */
    public static function generateMetaTitle($title, $siteName = 'DIGIZALL')
    {
        $maxLength = 60;
        $fullTitle = $title . ' - ' . $siteName;
        
        if (strlen($fullTitle) <= $maxLength) {
            return $fullTitle;
        }
        
        // Truncate title if too long
        $availableLength = $maxLength - strlen(' - ' . $siteName);
        $truncatedTitle = Str::limit($title, $availableLength, '');
        
        return $truncatedTitle . ' - ' . $siteName;
    }

    /**
     * Generate meta description with proper length
     */
    public static function generateMetaDescription($description)
    {
        $maxLength = 160;
        
        if (strlen($description) <= $maxLength) {
            return $description;
        }
        
        return Str::limit($description, $maxLength, '...');
    }

    /**
     * Generate canonical URL
     */
    public static function generateCanonicalUrl($path = null)
    {
        $baseUrl = config('app.url');
        
        if ($path) {
            return rtrim($baseUrl, '/') . '/' . ltrim($path, '/');
        }
        
        return $baseUrl . request()->getPathInfo();
    }

    /**
     * Generate Open Graph meta tags
     */
    public static function generateOpenGraphTags($data)
    {
        $defaults = [
            'title' => config('app.name'),
            'description' => 'Your Digital Success Partner',
            'image' => asset('images/og-image.jpg'),
            'url' => self::generateCanonicalUrl(),
            'type' => 'website',
            'site_name' => config('app.name'),
        ];

        $ogData = array_merge($defaults, $data);

        $tags = [];
        foreach ($ogData as $property => $content) {
            $tags[] = '<meta property="og:' . $property . '" content="' . htmlspecialchars($content) . '">';
        }

        return implode("\n    ", $tags);
    }

    /**
     * Generate Twitter Card meta tags
     */
    public static function generateTwitterCardTags($data)
    {
        $defaults = [
            'card' => 'summary_large_image',
            'title' => config('app.name'),
            'description' => 'Your Digital Success Partner',
            'image' => asset('images/og-image.jpg'),
        ];

        $twitterData = array_merge($defaults, $data);

        $tags = [];
        foreach ($twitterData as $name => $content) {
            $tags[] = '<meta name="twitter:' . $name . '" content="' . htmlspecialchars($content) . '">';
        }

        return implode("\n    ", $tags);
    }

    /**
     * Generate structured data (JSON-LD)
     */
    public static function generateStructuredData($type, $data)
    {
        $baseData = [
            '@context' => 'https://schema.org',
            '@type' => $type,
        ];

        $structuredData = array_merge($baseData, $data);

        return '<script type="application/ld+json">' . json_encode($structuredData, JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT) . '</script>';
    }

    /**
     * Generate organization structured data
     */
    public static function generateOrganizationStructuredData()
    {
        $data = [
            'name' => 'DIGIZALL',
            'url' => config('app.url'),
            'logo' => asset('images/logo.png'),
            'description' => 'Your Digital Success Partner - Comprehensive digital solutions including web development, digital marketing, SEO, and more.',
            'address' => [
                '@type' => 'PostalAddress',
                'addressCountry' => 'Multiple',
                'addressLocality' => 'UK, USA, Australia, Pakistan',
            ],
            'contactPoint' => [
                '@type' => 'ContactPoint',
                'telephone' => '+92-308-4281241',
                'contactType' => 'customer service',
                'email' => '<EMAIL>',
            ],
            'sameAs' => [
                'https://facebook.com/digizall',
                'https://twitter.com/digizall',
                'https://linkedin.com/company/digizall',
            ],
        ];

        return self::generateStructuredData('Organization', $data);
    }

    /**
     * Generate breadcrumb structured data
     */
    public static function generateBreadcrumbStructuredData($breadcrumbs)
    {
        $itemListElement = [];
        
        foreach ($breadcrumbs as $index => $breadcrumb) {
            $itemListElement[] = [
                '@type' => 'ListItem',
                'position' => $index + 1,
                'name' => $breadcrumb['name'],
                'item' => $breadcrumb['url'],
            ];
        }

        $data = [
            'itemListElement' => $itemListElement,
        ];

        return self::generateStructuredData('BreadcrumbList', $data);
    }

    /**
     * Generate service structured data
     */
    public static function generateServiceStructuredData($service)
    {
        $data = [
            'name' => $service->title,
            'description' => $service->short_description,
            'provider' => [
                '@type' => 'Organization',
                'name' => 'DIGIZALL',
                'url' => config('app.url'),
            ],
            'areaServed' => 'Worldwide',
            'serviceType' => $service->category,
        ];

        return self::generateStructuredData('Service', $data);
    }

    /**
     * Generate article structured data for blog posts
     */
    public static function generateArticleStructuredData($post)
    {
        $data = [
            'headline' => $post->title,
            'description' => $post->excerpt,
            'image' => $post->featured_image ? asset('storage/' . $post->featured_image) : asset('images/blog-default.jpg'),
            'author' => [
                '@type' => 'Person',
                'name' => $post->author->name,
            ],
            'publisher' => [
                '@type' => 'Organization',
                'name' => 'DIGIZALL',
                'logo' => [
                    '@type' => 'ImageObject',
                    'url' => asset('images/logo.png'),
                ],
            ],
            'datePublished' => $post->published_at->toISOString(),
            'dateModified' => $post->updated_at->toISOString(),
            'mainEntityOfPage' => [
                '@type' => 'WebPage',
                '@id' => route('blog.show', $post->slug),
            ],
        ];

        return self::generateStructuredData('Article', $data);
    }

    /**
     * Generate FAQ structured data
     */
    public static function generateFAQStructuredData($faqs)
    {
        $mainEntity = [];
        
        foreach ($faqs as $faq) {
            $mainEntity[] = [
                '@type' => 'Question',
                'name' => $faq['question'],
                'acceptedAnswer' => [
                    '@type' => 'Answer',
                    'text' => $faq['answer'],
                ],
            ];
        }

        $data = [
            'mainEntity' => $mainEntity,
        ];

        return self::generateStructuredData('FAQPage', $data);
    }

    /**
     * Generate robots meta tag
     */
    public static function generateRobotsTag($index = true, $follow = true, $additional = [])
    {
        $directives = [];
        
        $directives[] = $index ? 'index' : 'noindex';
        $directives[] = $follow ? 'follow' : 'nofollow';
        
        if (!empty($additional)) {
            $directives = array_merge($directives, $additional);
        }

        return '<meta name="robots" content="' . implode(', ', $directives) . '">';
    }

    /**
     * Generate hreflang tags for multilingual sites
     */
    public static function generateHreflangTags($alternates)
    {
        $tags = [];

        foreach ($alternates as $lang => $url) {
            $tags[] = '<link rel="alternate" hreflang="' . $lang . '" href="' . $url . '">';
        }

        return implode("\n    ", $tags);
    }

    /**
     * Generate local business structured data
     */
    public static function generateLocalBusinessStructuredData($business)
    {
        $data = [
            'name' => $business['name'] ?? 'DIGIZALL',
            'description' => $business['description'] ?? 'Your Digital Success Partner',
            'url' => $business['url'] ?? config('app.url'),
            'telephone' => $business['phone'] ?? '+92-308-4281241',
            'email' => $business['email'] ?? '<EMAIL>',
            'address' => [
                '@type' => 'PostalAddress',
                'streetAddress' => $business['street'] ?? '',
                'addressLocality' => $business['city'] ?? '',
                'addressRegion' => $business['state'] ?? '',
                'postalCode' => $business['zip'] ?? '',
                'addressCountry' => $business['country'] ?? 'Multiple',
            ],
            'geo' => [
                '@type' => 'GeoCoordinates',
                'latitude' => $business['latitude'] ?? '',
                'longitude' => $business['longitude'] ?? '',
            ],
            'openingHours' => $business['hours'] ?? [
                'Mo-Fr 09:00-18:00',
                'Sa 09:00-16:00',
            ],
            'priceRange' => $business['price_range'] ?? '$$',
            'image' => $business['image'] ?? asset('images/logo.png'),
        ];

        return self::generateStructuredData('LocalBusiness', $data);
    }

    /**
     * Generate website structured data
     */
    public static function generateWebsiteStructuredData()
    {
        $data = [
            'url' => config('app.url'),
            'name' => 'DIGIZALL',
            'description' => 'Your Digital Success Partner - Comprehensive digital solutions',
            'publisher' => [
                '@type' => 'Organization',
                'name' => 'DIGIZALL',
                'logo' => [
                    '@type' => 'ImageObject',
                    'url' => asset('images/logo.png'),
                ],
            ],
            'potentialAction' => [
                '@type' => 'SearchAction',
                'target' => config('app.url') . '/search?q={search_term_string}',
                'query-input' => 'required name=search_term_string',
            ],
        ];

        return self::generateStructuredData('WebSite', $data);
    }

    /**
     * Generate product/service structured data
     */
    public static function generateProductStructuredData($product)
    {
        $data = [
            'name' => $product['name'],
            'description' => $product['description'],
            'image' => $product['image'] ?? asset('images/service-default.jpg'),
            'brand' => [
                '@type' => 'Brand',
                'name' => 'DIGIZALL',
            ],
            'provider' => [
                '@type' => 'Organization',
                'name' => 'DIGIZALL',
                'url' => config('app.url'),
            ],
            'category' => $product['category'] ?? 'Digital Services',
        ];

        if (isset($product['price'])) {
            $data['offers'] = [
                '@type' => 'Offer',
                'price' => $product['price'],
                'priceCurrency' => $product['currency'] ?? 'USD',
                'availability' => 'https://schema.org/InStock',
                'seller' => [
                    '@type' => 'Organization',
                    'name' => 'DIGIZALL',
                ],
            ];
        }

        return self::generateStructuredData('Product', $data);
    }

    /**
     * Generate review structured data
     */
    public static function generateReviewStructuredData($review)
    {
        $data = [
            'author' => [
                '@type' => 'Person',
                'name' => $review['author_name'],
            ],
            'reviewRating' => [
                '@type' => 'Rating',
                'ratingValue' => $review['rating'],
                'bestRating' => '5',
                'worstRating' => '1',
            ],
            'reviewBody' => $review['content'],
            'datePublished' => $review['date'],
            'itemReviewed' => [
                '@type' => 'Organization',
                'name' => 'DIGIZALL',
                'url' => config('app.url'),
            ],
        ];

        return self::generateStructuredData('Review', $data);
    }

    /**
     * Generate aggregate rating structured data
     */
    public static function generateAggregateRatingStructuredData($ratings)
    {
        $data = [
            'itemReviewed' => [
                '@type' => 'Organization',
                'name' => 'DIGIZALL',
                'url' => config('app.url'),
            ],
            'ratingValue' => $ratings['average'],
            'bestRating' => '5',
            'worstRating' => '1',
            'ratingCount' => $ratings['count'],
        ];

        return self::generateStructuredData('AggregateRating', $data);
    }

    /**
     * Generate complete page SEO meta tags
     */
    public static function generatePageSEO($data)
    {
        $seo = [];

        // Basic meta tags
        $seo[] = '<title>' . self::generateMetaTitle($data['title']) . '</title>';
        $seo[] = '<meta name="description" content="' . self::generateMetaDescription($data['description']) . '">';
        $seo[] = '<link rel="canonical" href="' . self::generateCanonicalUrl($data['url'] ?? null) . '">';

        // Keywords (if provided)
        if (!empty($data['keywords'])) {
            $keywords = is_array($data['keywords']) ? implode(', ', $data['keywords']) : $data['keywords'];
            $seo[] = '<meta name="keywords" content="' . htmlspecialchars($keywords) . '">';
        }

        // Author (if provided)
        if (!empty($data['author'])) {
            $seo[] = '<meta name="author" content="' . htmlspecialchars($data['author']) . '">';
        }

        // Open Graph tags
        $ogData = [
            'title' => $data['title'],
            'description' => $data['description'],
            'image' => $data['image'] ?? asset('images/og-image.jpg'),
            'url' => $data['url'] ?? self::generateCanonicalUrl(),
        ];
        $seo[] = self::generateOpenGraphTags($ogData);

        // Twitter Card tags
        $twitterData = [
            'title' => $data['title'],
            'description' => $data['description'],
            'image' => $data['image'] ?? asset('images/og-image.jpg'),
        ];
        $seo[] = self::generateTwitterCardTags($twitterData);

        // Robots tag
        $index = $data['index'] ?? true;
        $follow = $data['follow'] ?? true;
        $additional = $data['robots_additional'] ?? [];
        $seo[] = self::generateRobotsTag($index, $follow, $additional);

        return implode("\n    ", $seo);
    }

    /**
     * Get page-specific SEO data
     */
    public static function getPageSEOData($page, $model = null)
    {
        switch ($page) {
            case 'home':
                return [
                    'title' => 'Digital Solutions & Web Development Services',
                    'description' => 'DIGIZALL offers comprehensive digital solutions including web development, SEO, digital marketing, and custom software development. Transform your business with our expert team.',
                    'keywords' => ['digital solutions', 'web development', 'SEO services', 'digital marketing', 'custom software'],
                    'image' => asset('images/hero-bg.jpg'),
                ];

            case 'services':
                return [
                    'title' => 'Our Digital Services - Web Development, SEO & Marketing',
                    'description' => 'Explore our comprehensive range of digital services including web development, mobile apps, SEO optimization, digital marketing, and custom software solutions.',
                    'keywords' => ['digital services', 'web development services', 'SEO services', 'digital marketing services'],
                ];

            case 'service':
                if ($model) {
                    return [
                        'title' => $model->title . ' - Professional Digital Services',
                        'description' => $model->short_description,
                        'keywords' => explode(',', $model->keywords ?? ''),
                        'image' => $model->featured_image ? asset('storage/' . $model->featured_image) : null,
                    ];
                }
                break;

            case 'blog':
                return [
                    'title' => 'Digital Marketing Blog - Tips, Insights & Industry News',
                    'description' => 'Stay updated with the latest digital marketing trends, web development tips, SEO insights, and industry news from DIGIZALL experts.',
                    'keywords' => ['digital marketing blog', 'web development tips', 'SEO insights', 'industry news'],
                ];

            case 'blog-post':
                if ($model) {
                    return [
                        'title' => $model->title,
                        'description' => $model->excerpt,
                        'keywords' => $model->tags ? $model->tags->pluck('name')->toArray() : [],
                        'image' => $model->featured_image ? asset('storage/' . $model->featured_image) : null,
                        'author' => $model->author->name ?? 'DIGIZALL Team',
                    ];
                }
                break;

            case 'about':
                return [
                    'title' => 'About DIGIZALL - Your Digital Success Partner',
                    'description' => 'Learn about DIGIZALL, a leading digital solutions company specializing in web development, digital marketing, and custom software development with years of experience.',
                    'keywords' => ['about digizall', 'digital solutions company', 'web development company'],
                ];

            case 'contact':
                return [
                    'title' => 'Contact DIGIZALL - Get Your Free Quote Today',
                    'description' => 'Contact DIGIZALL for professional digital solutions. Get a free quote for web development, SEO, digital marketing, or custom software development projects.',
                    'keywords' => ['contact digizall', 'free quote', 'digital solutions contact'],
                ];

            default:
                return [
                    'title' => 'DIGIZALL - Your Digital Success Partner',
                    'description' => 'Professional digital solutions including web development, SEO, digital marketing, and custom software development.',
                    'keywords' => ['digizall', 'digital solutions', 'web development'],
                ];
        }

        return [];
    }
}
