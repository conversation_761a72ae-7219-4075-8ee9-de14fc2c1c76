@extends('layouts.admin')

@section('title', 'Asset Management')
@section('page-title', 'Asset Management')

@section('content')
<div class="space-y-6">
    <!-- Asset Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- CSS Files -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                        <i class="fas fa-file-code text-white text-sm"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">CSS Files</p>
                    <p class="text-2xl font-semibold text-gray-900">{{ $stats['css_files'] ?? 0 }}</p>
                </div>
            </div>
        </div>

        <!-- JavaScript Files -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                        <i class="fab fa-js-square text-white text-sm"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">JS Files</p>
                    <p class="text-2xl font-semibold text-gray-900">{{ $stats['js_files'] ?? 0 }}</p>
                </div>
            </div>
        </div>

        <!-- Image Files -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                        <i class="fas fa-images text-white text-sm"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Images</p>
                    <p class="text-2xl font-semibold text-gray-900">{{ $stats['image_files'] ?? 0 }}</p>
                </div>
            </div>
        </div>

        <!-- Total Size -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                        <i class="fas fa-hdd text-white text-sm"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Total Size</p>
                    <p class="text-2xl font-semibold text-gray-900">{{ number_format($stats['total_size_mb'] ?? 0, 1) }} MB</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Build Information -->
    <div class="bg-white rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Build Information</h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h4 class="text-sm font-medium text-gray-900 mb-3">Build Status</h4>
                    <div class="space-y-2">
                        <div class="flex items-center">
                            <span class="w-3 h-3 rounded-full mr-2 {{ $buildInfo['manifest_exists'] ? 'bg-green-500' : 'bg-red-500' }}"></span>
                            <span class="text-sm text-gray-600">Manifest File: {{ $buildInfo['manifest_exists'] ? 'Exists' : 'Missing' }}</span>
                        </div>
                        <div class="flex items-center">
                            <span class="w-3 h-3 rounded-full mr-2 {{ $stats['build_exists'] ? 'bg-green-500' : 'bg-red-500' }}"></span>
                            <span class="text-sm text-gray-600">Build Directory: {{ $stats['build_exists'] ? 'Exists' : 'Missing' }}</span>
                        </div>
                        <div class="flex items-center">
                            <span class="w-3 h-3 rounded-full mr-2 {{ $stats['assets_compiled'] ? 'bg-green-500' : 'bg-red-500' }}"></span>
                            <span class="text-sm text-gray-600">Assets Compiled: {{ $stats['assets_compiled'] ? 'Yes' : 'No' }}</span>
                        </div>
                    </div>
                </div>
                <div>
                    <h4 class="text-sm font-medium text-gray-900 mb-3">Build Details</h4>
                    <div class="space-y-2">
                        @if($buildInfo['build_date'])
                            <p class="text-sm text-gray-600">Last Build: {{ $buildInfo['build_date'] }}</p>
                        @endif
                        @if(!empty($buildInfo['entry_points']))
                            <p class="text-sm text-gray-600">Entry Points: {{ count($buildInfo['entry_points']) }}</p>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Asset Management Actions -->
    <div class="bg-white rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Asset Management</h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <!-- Build Assets -->
                <button onclick="buildAssets()" class="btn-primary w-full">
                    <i class="fas fa-hammer mr-2"></i>
                    Build Assets
                </button>

                <!-- Optimize Assets -->
                <button onclick="optimizeAssets()" class="btn-secondary w-full">
                    <i class="fas fa-compress-alt mr-2"></i>
                    Optimize Assets
                </button>

                <!-- Clear Cache -->
                <button onclick="clearCache()" class="btn-outline w-full">
                    <i class="fas fa-trash mr-2"></i>
                    Clear Cache
                </button>

                <!-- Download Report -->
                <a href="{{ route('admin.assets.report') }}" class="btn-outline w-full text-center">
                    <i class="fas fa-download mr-2"></i>
                    Download Report
                </a>
            </div>
        </div>
    </div>

    <!-- Asset Files List -->
    <div class="bg-white rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Asset Files</h3>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">File Name</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Size</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Modified</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @forelse($assetFiles as $file)
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                {{ $file['name'] }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                    {{ $file['type'] === 'css' ? 'bg-blue-100 text-blue-800' : 
                                       ($file['type'] === 'js' ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800') }}">
                                    {{ strtoupper($file['type']) }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ $file['size_mb'] }} MB
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ date('Y-m-d H:i', $file['modified']) }}
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="4" class="px-6 py-4 text-center text-sm text-gray-500">
                                No asset files found. Run the build process to generate assets.
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Loading Modal -->
<div id="loadingModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-white rounded-lg p-6 max-w-sm w-full mx-4">
        <div class="flex items-center">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mr-3"></div>
            <span class="text-gray-900">Processing...</span>
        </div>
    </div>
</div>

@endsection

@push('scripts')
<script>
function showLoading() {
    document.getElementById('loadingModal').classList.remove('hidden');
    document.getElementById('loadingModal').classList.add('flex');
}

function hideLoading() {
    document.getElementById('loadingModal').classList.add('hidden');
    document.getElementById('loadingModal').classList.remove('flex');
}

function buildAssets() {
    if (!confirm('This will rebuild all assets. Continue?')) return;
    
    showLoading();
    
    fetch('{{ route("admin.assets.build") }}', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ environment: 'production' })
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        if (data.success) {
            alert('Assets built successfully!');
            location.reload();
        } else {
            alert('Build failed: ' + data.message);
        }
    })
    .catch(error => {
        hideLoading();
        alert('Build failed: ' + error.message);
    });
}

function optimizeAssets() {
    if (!confirm('This will optimize all assets. Continue?')) return;
    
    showLoading();
    
    fetch('{{ route("admin.assets.optimize") }}', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            minify_css: true,
            minify_js: true,
            optimize_images: true,
            generate_webp: true
        })
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        if (data.success) {
            alert('Assets optimized successfully!');
            location.reload();
        } else {
            alert('Optimization failed: ' + data.message);
        }
    })
    .catch(error => {
        hideLoading();
        alert('Optimization failed: ' + error.message);
    });
}

function clearCache() {
    if (!confirm('This will clear all asset caches. Continue?')) return;
    
    showLoading();
    
    fetch('{{ route("admin.assets.clear-cache") }}', {
        method: 'DELETE',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
        }
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        if (data.success) {
            alert('Cache cleared successfully!');
            location.reload();
        } else {
            alert('Cache clear failed: ' + data.message);
        }
    })
    .catch(error => {
        hideLoading();
        alert('Cache clear failed: ' + error.message);
    });
}
</script>
@endpush
