<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ServiceFeature extends Model
{
    use HasFactory;

    protected $fillable = [
        'service_id',
        'title',
        'description',
        'icon',
        'sort_order'
    ];

    /**
     * Get the service that owns the feature.
     */
    public function service()
    {
        return $this->belongsTo(Service::class);
    }

    /**
     * Get the feature's icon URL.
     */
    public function getIconUrlAttribute(): string
    {
        return $this->icon
            ? asset('storage/' . $this->icon)
            : asset('images/default-feature-icon.svg');
    }
}
