<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Model>
 */
class QuoteFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->name(),
            'email' => $this->faker->unique()->safeEmail(),
            'phone' => $this->faker->phoneNumber(),
            'company' => $this->faker->company(),
            'website' => $this->faker->optional()->url(),
            'service' => $this->faker->randomElement([
                'web-development',
                'web-application-development',
                'search-engine-optimization',
                'social-media-marketing',
                'content-writing',
                'graphic-design'
            ]),
            'project_type' => $this->faker->randomElement([
                'new-project',
                'redesign',
                'maintenance',
                'consultation'
            ]),
            'budget' => $this->faker->randomElement([
                'under-1000',
                '1000-5000',
                '5000-10000',
                '10000-25000',
                'over-25000'
            ]),
            'timeline' => $this->faker->randomElement([
                'asap',
                '1-month',
                '2-3-months',
                '3-6-months',
                'flexible'
            ]),
            'description' => $this->faker->paragraphs(3, true),
            'features' => json_encode($this->faker->randomElements([
                'responsive-design',
                'cms-integration',
                'e-commerce',
                'seo-optimization',
                'social-media-integration',
                'analytics-tracking',
                'contact-forms',
                'blog-functionality'
            ], $this->faker->numberBetween(1, 4))),
            'additional_info' => $this->faker->optional()->paragraph(),
            'preferred_contact' => $this->faker->randomElement(['email', 'phone', 'whatsapp']),
            'status' => $this->faker->randomElement(['pending', 'in_progress', 'quoted', 'accepted', 'rejected', 'completed']),
            'ip_address' => $this->faker->ipv4(),
            'user_agent' => $this->faker->userAgent(),
            'marketing_consent' => $this->faker->boolean(70), // 70% chance of consent
            'admin_notes' => $this->faker->optional()->paragraph(),
        ];
    }
}
