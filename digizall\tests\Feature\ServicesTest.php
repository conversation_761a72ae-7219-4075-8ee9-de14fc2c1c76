<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use App\Models\Service;
use App\Models\ServiceCategory;

class ServicesTest extends TestCase
{
    use RefreshDatabase;

    public function test_services_index_page_loads()
    {
        $response = $this->get('/services');
        $response->assertStatus(200);
    }

    public function test_services_index_displays_active_services()
    {
        $category = ServiceCategory::factory()->create();

        $activeService = Service::factory()->create([
            'title' => 'Active Service',
            'is_active' => true,
            'category_id' => $category->id
        ]);

        $inactiveService = Service::factory()->create([
            'title' => 'Inactive Service',
            'is_active' => false,
            'category_id' => $category->id
        ]);

        $response = $this->get('/services');

        $response->assertSee('Active Service');
        $response->assertDontSee('Inactive Service');
    }

    public function test_service_detail_page_loads()
    {
        $category = ServiceCategory::factory()->create();
        $service = Service::factory()->create([
            'slug' => 'test-service',
            'is_active' => true,
            'category_id' => $category->id
        ]);

        $response = $this->get('/services/test-service');
        $response->assertStatus(200);
        $response->assertSee($service->title);
    }

    public function test_inactive_service_returns_404()
    {
        $category = ServiceCategory::factory()->create();
        $service = Service::factory()->create([
            'slug' => 'inactive-service',
            'is_active' => false,
            'category_id' => $category->id
        ]);

        $response = $this->get('/services/inactive-service');
        $response->assertStatus(404);
    }

    public function test_nonexistent_service_returns_404()
    {
        $response = $this->get('/services/nonexistent-service');
        $response->assertStatus(404);
    }

    public function test_service_detail_contains_required_elements()
    {
        $category = ServiceCategory::factory()->create();
        $service = Service::factory()->create([
            'slug' => 'test-service',
            'title' => 'Test Service',
            'description' => 'Test description',
            'is_active' => true,
            'category_id' => $category->id
        ]);

        $response = $this->get('/services/test-service');

        $response->assertSee('Test Service');
        $response->assertSee('Test description');
        $response->assertSee('Get Quote');
    }

    public function test_services_can_be_filtered_by_category()
    {
        $category1 = ServiceCategory::factory()->create(['name' => 'Web Development']);
        $category2 = ServiceCategory::factory()->create(['name' => 'Digital Marketing']);

        $service1 = Service::factory()->create([
            'title' => 'Web Service',
            'category_id' => $category1->id,
            'is_active' => true
        ]);

        $service2 = Service::factory()->create([
            'title' => 'Marketing Service',
            'category_id' => $category2->id,
            'is_active' => true
        ]);

        $response = $this->get('/services?category=' . $category1->id);

        $response->assertSee('Web Service');
        $response->assertDontSee('Marketing Service');
    }
}
