@props([
    'steps' => [],
    'title' => 'Our Process',
    'subtitle' => 'How we work to deliver exceptional results',
    'layout' => 'horizontal', // horizontal, vertical, timeline
    'background' => 'white' // white, gray, primary
])

@php
    $bgClasses = match($background) {
        'gray' => 'bg-gray-50',
        'primary' => 'bg-gradient-to-r from-primary-600 to-secondary-600 text-white',
        default => 'bg-white'
    };
    
    $textColor = $background === 'primary' ? 'text-white' : 'text-gray-900';
    $subtitleColor = $background === 'primary' ? 'text-gray-200' : 'text-gray-600';
@endphp

<section class="section-padding {{ $bgClasses }}">
    <div class="container-custom">
        <!-- Section Header -->
        <div class="text-center mb-16" data-aos="fade-up">
            <h2 class="heading-secondary {{ $textColor }} mb-4">{{ $title }}</h2>
            <p class="text-lg {{ $subtitleColor }} max-w-2xl mx-auto">
                {{ $subtitle }}
            </p>
        </div>

        @if($layout === 'timeline')
            <!-- Timeline Layout -->
            <div class="max-w-4xl mx-auto">
                @foreach($steps as $index => $step)
                    <div class="flex items-start mb-12 {{ $loop->even ? 'flex-row-reverse' : '' }}" data-aos="fade-up" data-aos-delay="{{ $index * 100 }}">
                        <!-- Content -->
                        <div class="flex-1 {{ $loop->even ? 'text-right pr-8' : 'pl-8' }}">
                            <div class="bg-white rounded-xl shadow-lg p-6 {{ $background === 'primary' ? 'text-gray-900' : '' }}">
                                <h3 class="text-xl font-bold mb-3">{{ $step['title'] ?? 'Step ' . ($index + 1) }}</h3>
                                <p class="text-gray-600">{{ $step['description'] ?? '' }}</p>
                            </div>
                        </div>
                        
                        <!-- Step Number -->
                        <div class="flex-shrink-0 w-16 h-16 bg-primary-600 text-white rounded-full flex items-center justify-center font-bold text-xl relative z-10">
                            {{ $index + 1 }}
                        </div>
                        
                        <!-- Spacer -->
                        <div class="flex-1"></div>
                    </div>
                @endforeach
            </div>
        @elseif($layout === 'vertical')
            <!-- Vertical Layout -->
            <div class="max-w-2xl mx-auto">
                @foreach($steps as $index => $step)
                    <div class="flex items-start mb-8 {{ $loop->last ? '' : 'pb-8 border-b border-gray-200' }}" data-aos="fade-up" data-aos-delay="{{ $index * 100 }}">
                        <!-- Step Number -->
                        <div class="flex-shrink-0 w-12 h-12 bg-primary-600 text-white rounded-full flex items-center justify-center font-bold mr-6">
                            {{ $index + 1 }}
                        </div>
                        
                        <!-- Content -->
                        <div class="flex-1">
                            <h3 class="text-xl font-bold {{ $textColor }} mb-3">{{ $step['title'] ?? 'Step ' . ($index + 1) }}</h3>
                            <p class="{{ $subtitleColor }}">{{ $step['description'] ?? '' }}</p>
                        </div>
                    </div>
                @endforeach
            </div>
        @else
            <!-- Horizontal Layout (Default) -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-{{ min(count($steps), 4) }} gap-8">
                @foreach($steps as $index => $step)
                    <div class="text-center" data-aos="fade-up" data-aos-delay="{{ $index * 100 }}">
                        <!-- Step Number -->
                        <div class="w-16 h-16 bg-primary-600 text-white rounded-full flex items-center justify-center mx-auto mb-6 font-bold text-xl">
                            {{ $index + 1 }}
                        </div>
                        
                        <!-- Step Icon (if provided) -->
                        @if(isset($step['icon']))
                            <div class="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-6">
                                <i class="{{ $step['icon'] }} text-2xl text-primary-600"></i>
                            </div>
                        @endif
                        
                        <!-- Content -->
                        <h3 class="text-xl font-bold {{ $textColor }} mb-4">{{ $step['title'] ?? 'Step ' . ($index + 1) }}</h3>
                        <p class="{{ $subtitleColor }}">{{ $step['description'] ?? '' }}</p>
                        
                        <!-- Additional Details -->
                        @if(isset($step['details']))
                            <ul class="mt-4 text-sm {{ $subtitleColor }} space-y-1">
                                @foreach($step['details'] as $detail)
                                    <li>• {{ $detail }}</li>
                                @endforeach
                            </ul>
                        @endif
                    </div>
                @endforeach
            </div>
        @endif

        <!-- Process Benefits -->
        @if(isset($benefits) && count($benefits) > 0)
            <div class="mt-16 text-center" data-aos="fade-up">
                <h3 class="text-2xl font-bold {{ $textColor }} mb-8">Why Our Process Works</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
                    @foreach($benefits as $benefit)
                        <div class="flex items-center justify-center space-x-3">
                            @if(isset($benefit['icon']))
                                <i class="{{ $benefit['icon'] }} text-primary-600"></i>
                            @endif
                            <span class="{{ $subtitleColor }}">{{ $benefit['text'] ?? $benefit }}</span>
                        </div>
                    @endforeach
                </div>
            </div>
        @endif
    </div>
</section>
