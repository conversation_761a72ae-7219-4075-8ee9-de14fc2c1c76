@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles for DIGIZALL */
@layer base {
  html {
    scroll-behavior: smooth;
  }

  body {
    font-family: 'Inter', sans-serif;
  }
}

@layer components {
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200 inline-flex items-center justify-center;
  }

  .btn-secondary {
    @apply bg-secondary-100 hover:bg-secondary-200 text-secondary-800 font-semibold py-3 px-6 rounded-lg transition-colors duration-200 inline-flex items-center justify-center;
  }

  .btn-outline {
    @apply border-2 border-primary-600 text-primary-600 hover:bg-primary-600 hover:text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 inline-flex items-center justify-center;
  }

  .section-padding {
    @apply py-16 lg:py-24;
  }

  .container-custom {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  .heading-primary {
    @apply text-4xl lg:text-5xl xl:text-6xl font-bold text-gray-900 leading-tight;
  }

  .heading-secondary {
    @apply text-3xl lg:text-4xl font-bold text-gray-900 leading-tight;
  }

  .heading-tertiary {
    @apply text-2xl lg:text-3xl font-bold text-gray-900 leading-tight;
  }

  .text-body {
    @apply text-lg text-gray-600 leading-relaxed;
  }

  .card {
    @apply bg-white rounded-lg shadow-lg p-6 transition-all duration-300 hover:shadow-xl;
  }

  .service-card {
    @apply bg-white rounded-lg shadow-lg p-8 transition-all duration-300 hover:shadow-xl hover:-translate-y-2 group;
  }

  .testimonial-card {
    @apply bg-white rounded-lg shadow-lg p-6 transition-all duration-300 hover:shadow-xl;
  }

  .blog-card {
    @apply bg-white rounded-lg shadow-lg overflow-hidden transition-all duration-300 hover:shadow-xl hover:-translate-y-1;
  }

  .form-input {
    @apply w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors;
  }

  .form-textarea {
    @apply w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors resize-vertical;
  }

  .form-select {
    @apply w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors;
  }

  .nav-link {
    @apply text-gray-700 hover:text-primary-600 font-medium transition-colors duration-200;
  }

  .nav-link.active {
    @apply text-primary-600;
  }

  .hero-gradient {
    @apply bg-gradient-to-br from-primary-600 via-primary-700 to-secondary-800;
  }

  .text-gradient {
    @apply bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent;
  }

  /* Animation utilities */
  .animate-fade-in {
    animation: fadeIn 0.6s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.6s ease-out;
  }

  .animate-bounce-slow {
    animation: bounce 2s infinite;
  }

  /* Responsive utilities */
  .responsive-padding {
    @apply px-4 sm:px-6 lg:px-8;
  }

  .responsive-text {
    @apply text-base sm:text-lg lg:text-xl;
  }

  /* Custom scrollbar */
  .custom-scrollbar::-webkit-scrollbar {
    width: 8px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    @apply bg-gray-100 rounded-lg;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    @apply bg-primary-400 rounded-lg;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    @apply bg-primary-600;
  }
}

@layer utilities {
  /* Custom utilities */
  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .text-shadow-lg {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }

  .backdrop-blur-xs {
    backdrop-filter: blur(2px);
  }

  .backdrop-blur-sm {
    backdrop-filter: blur(4px);
  }

  .backdrop-blur-md {
    backdrop-filter: blur(8px);
  }

  /* Gradient backgrounds */
  .bg-gradient-primary {
    @apply bg-gradient-to-r from-primary-500 to-primary-700;
  }

  .bg-gradient-secondary {
    @apply bg-gradient-to-r from-secondary-500 to-secondary-700;
  }

  .bg-gradient-hero {
    @apply bg-gradient-to-br from-primary-600 via-primary-700 to-secondary-800;
  }

  /* Box shadows */
  .shadow-primary {
    box-shadow: 0 10px 25px rgba(59, 130, 246, 0.15);
  }

  .shadow-secondary {
    box-shadow: 0 10px 25px rgba(100, 116, 139, 0.15);
  }

  /* Hover effects */
  .hover-lift {
    @apply transition-transform duration-300 hover:-translate-y-2;
  }

  .hover-scale {
    @apply transition-transform duration-300 hover:scale-105;
  }

  /* Focus states */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }

  /* Mobile-first responsive fixes */
  .mobile-menu-open {
    @apply overflow-hidden;
  }

  /* Ensure proper spacing on mobile */
  .mobile-padding {
    @apply px-4 sm:px-6 lg:px-8;
  }

  /* Fix for small screens */
  @media (max-width: 640px) {
    .heading-primary {
      font-size: 2.5rem;
      line-height: 1.2;
    }

    .heading-secondary {
      font-size: 2rem;
      line-height: 1.3;
    }

    .section-padding {
      padding-top: 3rem;
      padding-bottom: 3rem;
    }
  }

  /* Fix for medium screens */
  @media (min-width: 641px) and (max-width: 1024px) {
    .heading-primary {
      font-size: 3rem;
      line-height: 1.2;
    }

    .heading-secondary {
      font-size: 2.5rem;
      line-height: 1.3;
    }
  }

  /* Ensure buttons are properly sized */
  .btn-primary,
  .btn-secondary,
  .btn-outline {
    min-height: 48px;
    min-width: 120px;
  }

  /* Fix for form elements */
  .form-input,
  .form-textarea,
  .form-select {
    min-height: 48px;
  }

  /* Ensure proper touch targets */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  /* Print styles */
  @media print {
    .no-print {
      display: none !important;
    }

    .print-only {
      display: block !important;
    }

    body {
      background: white !important;
      color: black !important;
    }

    .bg-primary-600,
    .bg-secondary-600 {
      background: #374151 !important;
      color: white !important;
    }
  }

  /* Dark mode support (if needed in future) */
  @media (prefers-color-scheme: dark) {
    .dark-mode-auto {
      background-color: #1f2937;
      color: #f9fafb;
    }
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .btn-primary {
      border: 2px solid currentColor;
    }

    .btn-secondary {
      border: 2px solid currentColor;
    }

    .card,
    .service-card,
    .testimonial-card,
    .blog-card {
      border: 1px solid #374151;
    }
  }
}