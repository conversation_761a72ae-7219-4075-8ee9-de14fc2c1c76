# DIGIZALL Deployment Guide

This guide provides detailed instructions for deploying DIGIZALL to various hosting environments.

## 📋 Pre-Deployment Checklist

### 1. Environment Preparation
- [ ] PHP 8.1+ installed
- [ ] Composer installed
- [ ] Node.js 16+ and npm installed
- [ ] MySQL/MariaDB database created
- [ ] SSL certificate ready (for production)
- [ ] Domain/subdomain configured

### 2. Code Preparation
```bash
# Build production assets
npm run build

# Clear development caches
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear

# Optimize for production
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Run performance optimization
php artisan performance:optimize --all
php artisan assets:optimize
```

### 3. Environment Configuration
```env
APP_NAME="DIGIZALL"
APP_ENV=production
APP_KEY=base64:your-generated-key
APP_DEBUG=false
APP_URL=https://yourdomain.com

DB_CONNECTION=mysql
DB_HOST=your-db-host
DB_PORT=3306
DB_DATABASE=your-database-name
DB_USERNAME=your-db-username
DB_PASSWORD=your-secure-password

MAIL_MAILER=smtp
MAIL_HOST=your-smtp-host
MAIL_PORT=587
MAIL_USERNAME=your-email
MAIL_PASSWORD=your-email-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="DIGIZALL"

CACHE_DRIVER=file
SESSION_DRIVER=file
QUEUE_CONNECTION=database
```

## 🌐 Shared Hosting Deployment

### Step 1: Prepare Files
1. **Build Assets**
   ```bash
   npm run build
   ```

2. **Optimize Laravel**
   ```bash
   php artisan config:cache
   php artisan route:cache
   php artisan view:cache
   ```

3. **Create Deployment Package**
   - Exclude `node_modules`, `.git`, `tests` folders
   - Include `.env` file with production settings
   - Ensure `storage` and `bootstrap/cache` are writable

### Step 2: Upload Files
1. **Upload via FTP/SFTP**
   - Upload all files to your hosting account
   - Recommended: Upload to a folder above public_html (e.g., `/laravel`)

2. **Move Public Files**
   - Copy contents of `public` folder to `public_html` or domain folder
   - Update `index.php` to point to correct Laravel installation path

3. **Update index.php**
   ```php
   require __DIR__.'/../laravel/vendor/autoload.php';
   $app = require_once __DIR__.'/../laravel/bootstrap/app.php';
   ```

### Step 3: Set Permissions
```bash
chmod -R 755 storage
chmod -R 755 bootstrap/cache
```

### Step 4: Database Setup
1. **Import Database**
   - Create database in hosting control panel
   - Import database dump or run migrations
   ```bash
   php artisan migrate --force
   ```

2. **Create Admin User**
   ```bash
   php artisan tinker
   >>> $user = new App\Models\User();
   >>> $user->name = 'Admin User';
   >>> $user->email = '<EMAIL>';
   >>> $user->password = bcrypt('secure-password');
   >>> $user->is_admin = true;
   >>> $user->save();
   ```

### Step 5: Configure Subdomains
For attendance portal (app.digizall.com):
1. Create subdomain in hosting control panel
2. Point subdomain to same Laravel installation
3. Configure routes to handle subdomain requests

## 🖥️ VPS/Dedicated Server Deployment

### Step 1: Server Setup
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install required packages
sudo apt install nginx mysql-server php8.1-fpm php8.1-mysql php8.1-xml php8.1-curl php8.1-zip php8.1-gd php8.1-mbstring php8.1-intl redis-server -y

# Install Composer
curl -sS https://getcomposer.org/installer | php
sudo mv composer.phar /usr/local/bin/composer

# Install Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs
```

### Step 2: MySQL Configuration
```bash
# Secure MySQL installation
sudo mysql_secure_installation

# Create database and user
sudo mysql -u root -p
```

```sql
CREATE DATABASE digizall;
CREATE USER 'digizall_user'@'localhost' IDENTIFIED BY 'secure_password';
GRANT ALL PRIVILEGES ON digizall.* TO 'digizall_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

### Step 3: Deploy Application
```bash
# Clone repository
cd /var/www
sudo git clone https://github.com/your-repo/digizall.git
sudo chown -R www-data:www-data digizall
cd digizall

# Install dependencies
composer install --optimize-autoloader --no-dev
npm install && npm run build

# Set permissions
sudo chmod -R 775 storage bootstrap/cache
sudo chown -R www-data:www-data storage bootstrap/cache

# Configure environment
cp .env.example .env
php artisan key:generate

# Run migrations
php artisan migrate --force

# Optimize for production
php artisan config:cache
php artisan route:cache
php artisan view:cache
```

### Step 4: Nginx Configuration
```nginx
# /etc/nginx/sites-available/digizall
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;
    root /var/www/digizall/public;
    index index.php index.html;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }

    # Cache static assets
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Deny access to sensitive files
    location ~ /\. {
        deny all;
    }

    location ~ /(vendor|storage|bootstrap|database|tests) {
        deny all;
    }
}

# Subdomain configuration for app.yourdomain.com
server {
    listen 80;
    server_name app.yourdomain.com;
    root /var/www/digizall/public;
    index index.php index.html;

    # Same configuration as main domain
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }
}
```

### Step 5: Enable Site and SSL
```bash
# Enable site
sudo ln -s /etc/nginx/sites-available/digizall /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx

# Install SSL certificate
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com -d app.yourdomain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

### Step 6: Process Management (Optional)
```bash
# Install Supervisor for queue workers
sudo apt install supervisor

# Create supervisor config
sudo nano /etc/supervisor/conf.d/digizall-worker.conf
```

```ini
[program:digizall-worker]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/digizall/artisan queue:work --sleep=3 --tries=3 --max-time=3600
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=www-data
numprocs=2
redirect_stderr=true
stdout_logfile=/var/www/digizall/storage/logs/worker.log
stopwaitsecs=3600
```

```bash
# Start supervisor
sudo supervisorctl reread
sudo supervisorctl update
sudo supervisorctl start digizall-worker:*
```

## 🔧 Post-Deployment Tasks

### 1. Verify Installation
- [ ] Website loads correctly
- [ ] Admin panel accessible
- [ ] Attendance portal working
- [ ] Forms submitting properly
- [ ] Email notifications working
- [ ] SSL certificate active

### 2. Performance Optimization
```bash
# Run optimization commands
php artisan performance:optimize --all
php artisan assets:optimize

# Set up cron jobs
crontab -e
# Add: * * * * * cd /var/www/digizall && php artisan schedule:run >> /dev/null 2>&1
```

### 3. Monitoring Setup
- Set up log monitoring
- Configure backup schedules
- Monitor performance metrics
- Set up uptime monitoring

### 4. Security Hardening
- [ ] Change default passwords
- [ ] Configure firewall
- [ ] Set up fail2ban
- [ ] Regular security updates
- [ ] Monitor access logs

## 🔄 Maintenance

### Regular Tasks
```bash
# Weekly maintenance
php artisan cache:clear
php artisan performance:optimize --all
php artisan assets:optimize

# Monthly tasks
composer update --no-dev
npm update && npm run build
php artisan migrate --force

# Backup database
mysqldump -u username -p digizall > backup_$(date +%Y%m%d).sql
```

### Monitoring Commands
```bash
# Check application status
php artisan about

# Monitor performance
php artisan performance:optimize --report

# Check logs
tail -f storage/logs/laravel.log
```

## 🆘 Troubleshooting

### Common Issues

#### 500 Internal Server Error
- Check file permissions: `chmod -R 775 storage bootstrap/cache`
- Verify `.env` configuration
- Check error logs: `tail storage/logs/laravel.log`

#### Database Connection Issues
- Verify database credentials in `.env`
- Test connection: `php artisan tinker` then `DB::connection()->getPdo()`
- Check MySQL service: `sudo systemctl status mysql`

#### Asset Loading Issues
- Rebuild assets: `npm run build`
- Clear caches: `php artisan cache:clear`
- Check file permissions

#### Email Not Working
- Verify SMTP settings in `.env`
- Test email: `php artisan tinker` then `Mail::raw('Test', function($m) { $m->to('<EMAIL>')->subject('Test'); });`

### Performance Issues
- Enable caching: `php artisan config:cache`
- Optimize database: `php artisan performance:optimize --database`
- Monitor slow queries in admin panel

---

For additional support, contact: <EMAIL>
