@props([
    'testimonial',
    'showImage' => true,
    'showRating' => true,
    'layout' => 'default' // default, compact, featured
])

<div class="bg-white rounded-xl shadow-lg p-6 {{ $layout === 'featured' ? 'lg:p-8' : '' }} relative overflow-hidden group hover:shadow-xl transition-all duration-300" data-aos="fade-up">
    <!-- Quote Icon -->
    <div class="absolute top-4 right-4 text-primary-200 opacity-50">
        <i class="fas fa-quote-right text-3xl"></i>
    </div>

    <!-- Rating -->
    @if($showRating)
        <div class="flex items-center mb-4">
            @for($i = 1; $i <= 5; $i++)
                @if($i <= $testimonial->rating)
                    <i class="fas fa-star text-yellow-400"></i>
                @else
                    <i class="far fa-star text-gray-300"></i>
                @endif
            @endfor
            <span class="ml-2 text-sm text-gray-500">({{ $testimonial->rating }}/5)</span>
        </div>
    @endif

    <!-- Testimonial Text -->
    <blockquote class="text-gray-700 leading-relaxed mb-6 {{ $layout === 'featured' ? 'text-lg' : '' }}">
        "{{ $testimonial->testimonial }}"
    </blockquote>

    <!-- Client Info -->
    <div class="flex items-center">
        @if($showImage)
            <div class="flex-shrink-0 mr-4">
                <img src="{{ $testimonial->client_image_url }}" 
                     alt="{{ $testimonial->client_name }}" 
                     class="w-12 h-12 rounded-full object-cover border-2 border-gray-200">
            </div>
        @endif

        <div class="flex-1">
            <h4 class="font-semibold text-gray-900">{{ $testimonial->client_name }}</h4>
            @if($testimonial->client_position || $testimonial->client_company)
                <p class="text-sm text-gray-600">
                    @if($testimonial->client_position)
                        {{ $testimonial->client_position }}
                    @endif
                    @if($testimonial->client_position && $testimonial->client_company)
                        at
                    @endif
                    @if($testimonial->client_company)
                        {{ $testimonial->client_company }}
                    @endif
                </p>
            @endif
        </div>
    </div>

    <!-- Hover Effect -->
    <div class="absolute inset-0 bg-gradient-to-r from-primary-500 to-secondary-500 opacity-0 group-hover:opacity-5 transition-opacity duration-300 rounded-xl"></div>
</div>
