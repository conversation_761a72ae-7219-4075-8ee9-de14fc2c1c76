<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use App\Models\User;
use App\Models\Role;
use App\Models\BlogPost;
use App\Models\Service;
use App\Models\ContactMessage as Contact;
use App\Models\QuoteRequest;

class AdminControllerTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $adminUser;

    protected function setUp(): void
    {
        parent::setUp();

        // Create admin role
        $adminRole = Role::create([
            'name' => 'admin',
            'description' => 'Administrator',
            'permissions' => json_encode(['manage_all'])
        ]);

        // Create admin user
        $this->adminUser = User::factory()->create([
            'email' => '<EMAIL>',
            'is_admin' => true,
        ]);

        $this->adminUser->roles()->attach($adminRole);
    }

    /** @test */
    public function admin_can_access_dashboard()
    {
        $response = $this->actingAs($this->adminUser)
                         ->get('/admin/dashboard');

        $response->assertStatus(200);
        $response->assertViewIs('admin.dashboard');
    }

    /** @test */
    public function non_admin_cannot_access_admin_dashboard()
    {
        $user = User::factory()->create(['is_admin' => false]);

        $response = $this->actingAs($user)
                         ->get('/admin/dashboard');

        $response->assertStatus(403);
    }

    /** @test */
    public function guest_cannot_access_admin_dashboard()
    {
        $response = $this->get('/admin/dashboard');

        $response->assertRedirect('/login');
    }

    /** @test */
    public function admin_can_view_blog_posts()
    {
        BlogPost::factory()->count(5)->create();

        $response = $this->actingAs($this->adminUser)
                         ->get('/admin/blog');

        $response->assertStatus(200);
        $response->assertViewIs('admin.blog.index');
        $response->assertViewHas('posts');
    }

    /** @test */
    public function admin_can_create_blog_post()
    {
        $blogData = [
            'title' => 'Test Blog Post',
            'slug' => 'test-blog-post',
            'excerpt' => 'This is a test excerpt',
            'content' => 'This is test content for the blog post.',
            'category_id' => 1,
            'status' => 'published',
            'is_featured' => false,
        ];

        $response = $this->actingAs($this->adminUser)
                         ->post('/admin/blog', $blogData);

        $response->assertRedirect('/admin/blog');
        $this->assertDatabaseHas('blog_posts', [
            'title' => 'Test Blog Post',
            'slug' => 'test-blog-post',
        ]);
    }

    /** @test */
    public function admin_can_view_services()
    {
        Service::factory()->count(3)->create();

        $response = $this->actingAs($this->adminUser)
                         ->get('/admin/services');

        $response->assertStatus(200);
        $response->assertViewIs('admin.services.index');
        $response->assertViewHas('services');
    }

    /** @test */
    public function admin_can_view_contacts()
    {
        Contact::factory()->count(5)->create();

        $response = $this->actingAs($this->adminUser)
                         ->get('/admin/contacts');

        $response->assertStatus(200);
        $response->assertViewIs('admin.contacts.index');
        $response->assertViewHas('contacts');
    }

    /** @test */
    public function admin_can_update_contact_status()
    {
        $contact = Contact::factory()->create(['status' => 'pending']);

        $response = $this->actingAs($this->adminUser)
                         ->put("/admin/contacts/{$contact->id}/status", [
                             'status' => 'replied',
                             'admin_notes' => 'Responded to customer inquiry'
                         ]);

        $response->assertRedirect();
        $this->assertDatabaseHas('contact_messages', [
            'id' => $contact->id,
            'status' => 'replied',
            'admin_notes' => 'Responded to customer inquiry'
        ]);
    }

    /** @test */
    public function admin_can_delete_blog_post()
    {
        $blogPost = BlogPost::factory()->create();

        $response = $this->actingAs($this->adminUser)
                         ->delete("/admin/blog/{$blogPost->id}");

        $response->assertRedirect('/admin/blog');
        $this->assertDatabaseMissing('blog_posts', [
            'id' => $blogPost->id
        ]);
    }

    /** @test */
    public function admin_can_toggle_blog_post_status()
    {
        $blogPost = BlogPost::factory()->create(['status' => 'draft']);

        $response = $this->actingAs($this->adminUser)
                         ->post("/admin/blog/{$blogPost->id}/toggle-status");

        $response->assertRedirect();
        $this->assertDatabaseHas('blog_posts', [
            'id' => $blogPost->id,
            'status' => 'published'
        ]);
    }

    /** @test */
    public function admin_can_perform_bulk_actions_on_blog_posts()
    {
        $blogPosts = BlogPost::factory()->count(3)->create(['status' => 'draft']);
        $postIds = $blogPosts->pluck('id')->toArray();

        $response = $this->actingAs($this->adminUser)
                         ->post('/admin/blog/bulk', [
                             'action' => 'publish',
                             'posts' => $postIds
                         ]);

        $response->assertRedirect();

        foreach ($postIds as $postId) {
            $this->assertDatabaseHas('blog_posts', [
                'id' => $postId,
                'status' => 'published'
            ]);
        }
    }
}
