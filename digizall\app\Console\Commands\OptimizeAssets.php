<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\AssetOptimizationService;

class OptimizeAssets extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'assets:optimize
                            {--css : Only optimize CSS files}
                            {--js : Only optimize JavaScript files}
                            {--images : Only optimize images}
                            {--webp : Only generate WebP images}
                            {--cleanup : Only cleanup old assets}
                            {--stats : Show optimization statistics}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Optimize application assets (CSS, JS, images)';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🚀 Starting Asset Optimization...');
        $startTime = microtime(true);

        $options = $this->options();
        $runAll = !$options['css'] && !$options['js'] && !$options['images'] &&
                  !$options['webp'] && !$options['cleanup'] && !$options['stats'];

        if ($options['stats']) {
            $this->showStats();
            return 0;
        }

        if ($options['css'] || $runAll) {
            $this->optimizeCSS();
        }

        if ($options['js'] || $runAll) {
            $this->optimizeJS();
        }

        if ($options['images'] || $runAll) {
            $this->optimizeImages();
        }

        if ($options['webp'] || $runAll) {
            $this->generateWebP();
        }

        if ($options['cleanup'] || $runAll) {
            $this->cleanupAssets();
        }

        if ($runAll) {
            $this->generateManifest();
        }

        $duration = round((microtime(true) - $startTime) * 1000, 2);
        $this->info("✅ Asset optimization completed in {$duration}ms");

        return 0;
    }

    /**
     * Optimize CSS files
     */
    private function optimizeCSS()
    {
        $this->info('🎨 Optimizing CSS files...');

        $results = AssetOptimizationService::optimizeCSS();

        if (isset($results['error'])) {
            $this->warn("  ⚠️  {$results['error']}");
            return;
        }

        $totalSavings = 0;
        foreach ($results as $result) {
            $this->line("  • {$result['file']}: {$result['savings_percent']}% smaller");
            $totalSavings += $result['savings'];
        }

        $this->info("  ✅ Optimized " . count($results) . " CSS files, saved " . round($totalSavings / 1024, 2) . " KB");
    }

    /**
     * Optimize JavaScript files
     */
    private function optimizeJS()
    {
        $this->info('⚡ Optimizing JavaScript files...');

        $results = AssetOptimizationService::optimizeJS();

        if (isset($results['error'])) {
            $this->warn("  ⚠️  {$results['error']}");
            return;
        }

        $totalSavings = 0;
        foreach ($results as $result) {
            $this->line("  • {$result['file']}: {$result['savings_percent']}% smaller");
            $totalSavings += $result['savings'];
        }

        $this->info("  ✅ Optimized " . count($results) . " JS files, saved " . round($totalSavings / 1024, 2) . " KB");
    }

    /**
     * Optimize images
     */
    private function optimizeImages()
    {
        $this->info('🖼️  Optimizing images...');

        $results = AssetOptimizationService::optimizePublicImages();

        if (isset($results['error'])) {
            $this->warn("  ⚠️  {$results['error']}");
            return;
        }

        $totalSavings = 0;
        $optimizedCount = 0;

        foreach ($results as $result) {
            if (isset($result['error'])) {
                $this->warn("  ⚠️  {$result['file']}: {$result['error']}");
            } else {
                $this->line("  • {$result['file']}: {$result['savings_percent']}% smaller");
                $totalSavings += $result['savings'];
                $optimizedCount++;
            }
        }

        $this->info("  ✅ Optimized {$optimizedCount} images, saved " . round($totalSavings / 1024, 2) . " KB");
    }

    /**
     * Generate WebP images
     */
    private function generateWebP()
    {
        $this->info('🔄 Generating WebP images...');

        $results = AssetOptimizationService::generateWebPImages();

        if (isset($results['error'])) {
            $this->warn("  ⚠️  {$results['error']}");
            return;
        }

        $totalSavings = 0;
        $generatedCount = 0;

        foreach ($results as $result) {
            if (isset($result['error'])) {
                $this->warn("  ⚠️  {$result['file']}: {$result['error']}");
            } else {
                $savings = $result['savings'];
                $savingsPercent = round(($savings / $result['original_size']) * 100, 2);
                $this->line("  • {$result['webp']}: {$savingsPercent}% smaller than original");
                $totalSavings += $savings;
                $generatedCount++;
            }
        }

        $this->info("  ✅ Generated {$generatedCount} WebP images, saved " . round($totalSavings / 1024, 2) . " KB");
    }

    /**
     * Cleanup old assets
     */
    private function cleanupAssets()
    {
        $this->info('🧹 Cleaning up old assets...');

        $results = AssetOptimizationService::cleanupOldAssets();

        if (isset($results['error'])) {
            $this->warn("  ⚠️  {$results['error']}");
            return;
        }

        $totalSize = 0;
        foreach ($results as $result) {
            $this->line("  • Deleted {$result['file']} ({$result['age_days']} days old)");
            $totalSize += $result['size'];
        }

        $this->info("  ✅ Cleaned up " . count($results) . " old files, freed " . round($totalSize / 1024, 2) . " KB");
    }

    /**
     * Generate asset manifest
     */
    private function generateManifest()
    {
        $this->info('📋 Generating asset manifest...');

        $manifest = AssetOptimizationService::generateAssetManifest();

        $this->info("  ✅ Generated manifest with " . count($manifest) . " assets");
    }

    /**
     * Show optimization statistics
     */
    private function showStats()
    {
        $this->info('📊 Asset Optimization Statistics');

        $stats = AssetOptimizationService::getOptimizationStats();

        $this->table(['Metric', 'Value'], [
            ['CSS Files', $stats['css_files']],
            ['JavaScript Files', $stats['js_files']],
            ['Image Files', $stats['image_files']],
            ['WebP Files', $stats['webp_files']],
            ['Total Size', $stats['total_size_mb'] . ' MB'],
            ['Build Directory', $stats['build_exists'] ? '✅ Exists' : '❌ Missing'],
            ['Images Directory', $stats['images_exists'] ? '✅ Exists' : '❌ Missing'],
        ]);
    }
}
