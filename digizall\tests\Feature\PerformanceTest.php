<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use App\Services\CacheService;
use App\Services\DatabaseOptimizationService;
use App\Services\PerformanceMonitoringService;

class PerformanceTest extends TestCase
{
    use RefreshDatabase;

    public function test_page_load_times_are_acceptable()
    {
        $routes = [
            '/',
            '/services',
            '/blog',
            '/about',
            '/contact',
        ];

        foreach ($routes as $route) {
            $startTime = microtime(true);

            $response = $this->get($route);

            $endTime = microtime(true);
            $loadTime = ($endTime - $startTime) * 1000; // Convert to milliseconds

            $response->assertStatus(200);
            $this->assertLessThan(2000, $loadTime, "Route {$route} took {$loadTime}ms to load (should be < 2000ms)");
        }
    }

    public function test_database_queries_are_optimized()
    {
        // Enable query logging
        \DB::enableQueryLog();

        $response = $this->get('/');

        $queries = \DB::getQueryLog();
        $queryCount = count($queries);

        // Home page should not have excessive queries
        $this->assertLessThan(20, $queryCount, "Home page executed {$queryCount} queries (should be < 20)");

        // Check for N+1 query problems
        $duplicateQueries = [];
        foreach ($queries as $query) {
            $sql = $query['query'];
            if (isset($duplicateQueries[$sql])) {
                $duplicateQueries[$sql]++;
            } else {
                $duplicateQueries[$sql] = 1;
            }
        }

        $nPlusOneQueries = array_filter($duplicateQueries, function($count) {
            return $count > 3; // More than 3 identical queries might indicate N+1 problem
        });

        $this->assertEmpty($nPlusOneQueries, 'Potential N+1 query problem detected');
    }

    public function test_cache_service_functionality()
    {
        // Test cache warming
        CacheService::warmUp();

        // Test that cached data is retrieved
        $services1 = CacheService::getServices();
        $services2 = CacheService::getServices();

        $this->assertEquals($services1, $services2);

        // Test cache clearing
        CacheService::clearAll();

        // This should work without errors
        $this->assertTrue(true);
    }

    public function test_memory_usage_is_reasonable()
    {
        $initialMemory = memory_get_usage(true);

        $response = $this->get('/');

        $finalMemory = memory_get_usage(true);
        $memoryUsed = $finalMemory - $initialMemory;

        // Memory usage should be less than 50MB for a single request
        $this->assertLessThan(50 * 1024 * 1024, $memoryUsed,
            "Memory usage was " . round($memoryUsed / 1024 / 1024, 2) . "MB (should be < 50MB)");
    }

    public function test_response_headers_include_performance_optimizations()
    {
        $response = $this->get('/');

        // Check for caching headers
        $this->assertTrue(
            $response->headers->has('Cache-Control') ||
            $response->headers->has('ETag') ||
            $response->headers->has('Last-Modified'),
            'Response should include caching headers'
        );
    }

    public function test_database_health_check()
    {
        $health = DatabaseOptimizationService::getHealthScore();

        $this->assertIsArray($health);
        $this->assertArrayHasKey('score', $health);
        $this->assertArrayHasKey('grade', $health);

        // Database health should be at least grade C (score >= 70)
        $this->assertGreaterThanOrEqual(70, $health['score'],
            "Database health score is {$health['score']} (grade {$health['grade']}), should be >= 70");
    }

    public function test_asset_optimization_commands_work()
    {
        // Test that optimization commands can be called without errors
        $exitCode = \Artisan::call('performance:optimize', ['--cache' => true]);
        $this->assertEquals(0, $exitCode);

        $exitCode = \Artisan::call('assets:optimize', ['--stats' => true]);
        $this->assertEquals(0, $exitCode);
    }

    public function test_large_dataset_performance()
    {
        // Create a larger dataset to test performance
        \App\Models\Service::factory()->count(50)->create();
        \App\Models\BlogPost::factory()->count(100)->create();

        $startTime = microtime(true);

        $response = $this->get('/services');

        $endTime = microtime(true);
        $loadTime = ($endTime - $startTime) * 1000;

        $response->assertStatus(200);
        $this->assertLessThan(3000, $loadTime,
            "Services page with large dataset took {$loadTime}ms (should be < 3000ms)");
    }

    public function test_concurrent_request_handling()
    {
        // Simulate multiple concurrent requests
        $responses = [];
        $startTime = microtime(true);

        for ($i = 0; $i < 5; $i++) {
            $responses[] = $this->get('/');
        }

        $endTime = microtime(true);
        $totalTime = ($endTime - $startTime) * 1000;

        // All responses should be successful
        foreach ($responses as $response) {
            $response->assertStatus(200);
        }

        // Total time for 5 requests should be reasonable
        $this->assertLessThan(10000, $totalTime,
            "5 concurrent requests took {$totalTime}ms (should be < 10000ms)");
    }

    public function test_image_optimization_reduces_file_size()
    {
        // This would require actual image files to test
        // For now, we'll test that the optimization service exists and can be called
        $stats = \App\Services\AssetOptimizationService::getOptimizationStats();

        $this->assertIsArray($stats);
        $this->assertArrayHasKey('total_size_mb', $stats);
    }

    public function test_sitemap_generation_performance()
    {
        // Create test data
        \App\Models\Service::factory()->count(20)->create();
        \App\Models\BlogPost::factory()->count(50)->create();

        $startTime = microtime(true);

        $response = $this->get('/sitemap.xml');

        $endTime = microtime(true);
        $loadTime = ($endTime - $startTime) * 1000;

        $response->assertStatus(200);
        $response->assertHeader('Content-Type', 'application/xml; charset=UTF-8');

        $this->assertLessThan(2000, $loadTime,
            "Sitemap generation took {$loadTime}ms (should be < 2000ms)");
    }

    public function test_search_functionality_performance()
    {
        // Create searchable content
        \App\Models\Service::factory()->count(30)->create();
        \App\Models\BlogPost::factory()->count(50)->create();

        $startTime = microtime(true);

        $response = $this->get('/services?search=web');

        $endTime = microtime(true);
        $loadTime = ($endTime - $startTime) * 1000;

        $response->assertStatus(200);

        $this->assertLessThan(1500, $loadTime,
            "Search functionality took {$loadTime}ms (should be < 1500ms)");
    }
}
