@extends('layouts.app')

@section('title', $category->name . ' - DIGIZALL Blog')
@section('meta_description', 'Explore our ' . strtolower($category->name) . ' articles and stay updated with the latest insights and trends.')

@section('content')

<!-- <PERSON> Header -->
<section class="bg-gradient-to-r from-primary-600 to-secondary-600 text-white py-20">
    <div class="container-custom">
        <div class="text-center" data-aos="fade-up">
            <!-- Breadcrumb -->
            <nav class="mb-6">
                <ol class="flex items-center justify-center space-x-2 text-sm">
                    <li><a href="{{ route('home') }}" class="text-gray-200 hover:text-white">Home</a></li>
                    <li><i class="fas fa-chevron-right text-gray-400 mx-2"></i></li>
                    <li><a href="{{ route('blog.index') }}" class="text-gray-200 hover:text-white">Blog</a></li>
                    <li><i class="fas fa-chevron-right text-gray-400 mx-2"></i></li>
                    <li class="text-white">{{ $category->name }}</li>
                </ol>
            </nav>

            <!-- Category Badge -->
            <div class="mb-4">
                <span class="inline-block px-4 py-2 rounded-full text-sm font-semibold text-white" 
                      style="background-color: {{ $category->color }}">
                    {{ $category->name }}
                </span>
            </div>

            <h1 class="heading-primary text-white mb-4">
                {{ $category->name }} Articles
            </h1>
            
            @if($category->description)
                <p class="text-xl text-gray-200 max-w-2xl mx-auto">
                    {{ $category->description }}
                </p>
            @else
                <p class="text-xl text-gray-200 max-w-2xl mx-auto">
                    Explore our {{ strtolower($category->name) }} articles and stay updated with the latest insights.
                </p>
            @endif

            <!-- Post Count -->
            <div class="mt-6">
                <span class="text-gray-200">
                    {{ $posts->total() }} {{ Str::plural('article', $posts->total()) }} found
                </span>
            </div>
        </div>
    </div>
</section>

<!-- Blog Content -->
<section class="section-padding">
    <div class="container-custom">
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-12">
            <!-- Main Content -->
            <div class="lg:col-span-3">
                @if($posts->count() > 0)
                    <!-- Blog Posts Grid -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
                        @foreach($posts as $post)
                            <div data-aos="fade-up" data-aos-delay="{{ $loop->index * 100 }}">
                                <x-blog-card :post="$post" :featured="$post->is_featured" />
                            </div>
                        @endforeach
                    </div>

                    <!-- Pagination -->
                    <div class="flex justify-center" data-aos="fade-up">
                        {{ $posts->links() }}
                    </div>
                @else
                    <!-- No Posts Found -->
                    <div class="text-center py-16">
                        <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                            <i class="fas fa-folder-open text-3xl text-gray-400"></i>
                        </div>
                        <h2 class="text-2xl font-bold text-gray-900 mb-4">No Articles Found</h2>
                        <p class="text-gray-600 mb-8">
                            No articles are available in the {{ $category->name }} category at the moment.
                        </p>
                        <a href="{{ route('blog.index') }}" class="btn-primary">
                            View All Articles
                        </a>
                    </div>
                @endif
            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-1">
                <!-- Category Info -->
                <div class="bg-white rounded-xl shadow-lg p-6 mb-8" data-aos="fade-up">
                    <h3 class="text-xl font-bold text-gray-900 mb-4">About {{ $category->name }}</h3>
                    @if($category->description)
                        <p class="text-gray-600 mb-4">{{ $category->description }}</p>
                    @endif
                    <div class="flex items-center justify-between text-sm">
                        <span class="text-gray-500">Total Articles:</span>
                        <span class="font-semibold text-gray-900">{{ $posts->total() }}</span>
                    </div>
                </div>

                <!-- Other Categories -->
                @if($categories->count() > 0)
                    <div class="bg-white rounded-xl shadow-lg p-6 mb-8" data-aos="fade-up" data-aos-delay="100">
                        <h3 class="text-xl font-bold text-gray-900 mb-6">Other Categories</h3>
                        <div class="space-y-3">
                            @foreach($categories as $otherCategory)
                                <a href="{{ route('blog.category', $otherCategory->slug) }}" 
                                   class="flex items-center justify-between text-gray-600 hover:text-primary-600 transition-colors">
                                    <span>{{ $otherCategory->name }}</span>
                                    <span class="bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-xs">
                                        {{ $otherCategory->published_posts_count }}
                                    </span>
                                </a>
                            @endforeach
                        </div>
                        <div class="mt-4 pt-4 border-t border-gray-200">
                            <a href="{{ route('blog.index') }}" class="text-primary-600 hover:text-primary-700 font-medium text-sm">
                                View All Categories →
                            </a>
                        </div>
                    </div>
                @endif

                <!-- Quick Actions -->
                <div class="bg-gray-50 rounded-xl p-6" data-aos="fade-up" data-aos-delay="200">
                    <h3 class="text-lg font-bold text-gray-900 mb-4">Quick Actions</h3>
                    <div class="space-y-3">
                        <a href="{{ route('blog.index') }}" class="block text-gray-600 hover:text-primary-600 transition-colors">
                            <i class="fas fa-arrow-left mr-2"></i>
                            Back to All Posts
                        </a>
                        <a href="{{ route('contact') }}" class="block text-gray-600 hover:text-primary-600 transition-colors">
                            <i class="fas fa-envelope mr-2"></i>
                            Contact Us
                        </a>
                        <a href="{{ route('quote') }}" class="block text-gray-600 hover:text-primary-600 transition-colors">
                            <i class="fas fa-quote-left mr-2"></i>
                            Get Free Quote
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="section-padding bg-gray-50">
    <div class="container-custom">
        <div class="bg-gradient-to-r from-primary-600 to-secondary-600 rounded-2xl p-12 text-center text-white" data-aos="fade-up">
            <h2 class="heading-secondary text-white mb-6">
                Stay Updated with {{ $category->name }} Insights
            </h2>
            <p class="text-xl text-gray-200 mb-8 max-w-2xl mx-auto">
                Subscribe to our newsletter to get the latest {{ strtolower($category->name) }} tips and insights delivered to your inbox.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center max-w-md mx-auto">
                <input type="email" placeholder="Enter your email" 
                       class="flex-1 px-4 py-3 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-white">
                <button class="btn-primary bg-white text-primary-600 hover:bg-gray-100 px-6 py-3 font-semibold">
                    Subscribe
                </button>
            </div>
        </div>
    </div>
</section>

@endsection
