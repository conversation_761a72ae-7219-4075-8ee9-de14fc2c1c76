<?php

namespace App\Services;

use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Request;

class SecurityService
{
    /**
     * Rate limiting configurations
     */
    const RATE_LIMITS = [
        'login' => [
            'max_attempts' => 5,
            'decay_minutes' => 15,
        ],
        'contact' => [
            'max_attempts' => 3,
            'decay_minutes' => 60,
        ],
        'quote' => [
            'max_attempts' => 2,
            'decay_minutes' => 60,
        ],
        'api' => [
            'max_attempts' => 60,
            'decay_minutes' => 1,
        ],
    ];

    /**
     * Check rate limit for a given action
     */
    public static function checkRateLimit(Request $request, string $action): bool
    {
        $key = self::getRateLimitKey($request, $action);
        $config = self::RATE_LIMITS[$action] ?? self::RATE_LIMITS['api'];

        return RateLimiter::tooManyAttempts($key, $config['max_attempts']);
    }

    /**
     * Hit rate limiter for a given action
     */
    public static function hitRateLimit(Request $request, string $action): void
    {
        $key = self::getRateLimitKey($request, $action);
        $config = self::RATE_LIMITS[$action] ?? self::RATE_LIMITS['api'];

        RateLimiter::hit($key, $config['decay_minutes'] * 60);
    }

    /**
     * Get remaining attempts for rate limit
     */
    public static function getRemainingAttempts(Request $request, string $action): int
    {
        $key = self::getRateLimitKey($request, $action);
        $config = self::RATE_LIMITS[$action] ?? self::RATE_LIMITS['api'];

        return RateLimiter::remaining($key, $config['max_attempts']);
    }

    /**
     * Get rate limit key
     */
    private static function getRateLimitKey(Request $request, string $action): string
    {
        return $action . '|' . $request->ip();
    }

    /**
     * Validate and sanitize input data
     */
    public static function sanitizeInput(array $data): array
    {
        $sanitized = [];

        foreach ($data as $key => $value) {
            if (is_string($value)) {
                // Remove potentially dangerous characters
                $value = strip_tags($value);
                $value = htmlspecialchars($value, ENT_QUOTES, 'UTF-8');
                $value = trim($value);
            } elseif (is_array($value)) {
                $value = self::sanitizeInput($value);
            }

            $sanitized[$key] = $value;
        }

        return $sanitized;
    }

    /**
     * Check for suspicious patterns in input
     */
    public static function detectSuspiciousInput(array $data): array
    {
        $suspiciousPatterns = [
            'sql_injection' => [
                '/(\bunion\b.*\bselect\b)|(\bselect\b.*\bunion\b)/i',
                '/\b(select|insert|update|delete|drop|create|alter)\b.*\b(from|into|table|database)\b/i',
                '/(\bor\b|\band\b).*[\'"].*[\'"].*(\bor\b|\band\b)/i',
            ],
            'xss' => [
                '/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/i',
                '/javascript:/i',
                '/on\w+\s*=/i',
                '/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/i',
            ],
            'path_traversal' => [
                '/\.\.[\/\\\\]/i',
                '/\.(exe|bat|cmd|com|pif|scr|vbs|js)$/i',
            ],
            'command_injection' => [
                '/[;&|`$(){}]/i',
                '/\b(cat|ls|dir|type|copy|move|del|rm|mkdir|rmdir)\b/i',
            ],
        ];

        $threats = [];

        foreach ($data as $key => $value) {
            if (!is_string($value)) {
                continue;
            }

            foreach ($suspiciousPatterns as $threatType => $patterns) {
                foreach ($patterns as $pattern) {
                    if (preg_match($pattern, $value)) {
                        $threats[] = [
                            'type' => $threatType,
                            'field' => $key,
                            'pattern' => $pattern,
                            'value' => substr($value, 0, 100), // Limit logged value
                        ];
                    }
                }
            }
        }

        return $threats;
    }

    /**
     * Log security event
     */
    public static function logSecurityEvent(string $event, array $data = [], string $level = 'warning'): void
    {
        $logData = [
            'event' => $event,
            'ip' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'url' => request()->fullUrl(),
            'user_id' => auth()->id(),
            'timestamp' => now()->toISOString(),
            'data' => $data,
        ];

        Log::log($level, "Security Event: {$event}", $logData);
    }

    /**
     * Generate secure password
     */
    public static function generateSecurePassword(int $length = 12): string
    {
        $characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
        $password = '';

        for ($i = 0; $i < $length; $i++) {
            $password .= $characters[random_int(0, strlen($characters) - 1)];
        }

        return $password;
    }

    /**
     * Validate password strength
     */
    public static function validatePasswordStrength(string $password): array
    {
        $errors = [];
        $score = 0;

        // Length check
        if (strlen($password) < 8) {
            $errors[] = 'Password must be at least 8 characters long';
        } else {
            $score += 1;
        }

        // Uppercase check
        if (!preg_match('/[A-Z]/', $password)) {
            $errors[] = 'Password must contain at least one uppercase letter';
        } else {
            $score += 1;
        }

        // Lowercase check
        if (!preg_match('/[a-z]/', $password)) {
            $errors[] = 'Password must contain at least one lowercase letter';
        } else {
            $score += 1;
        }

        // Number check
        if (!preg_match('/[0-9]/', $password)) {
            $errors[] = 'Password must contain at least one number';
        } else {
            $score += 1;
        }

        // Special character check
        if (!preg_match('/[!@#$%^&*(),.?":{}|<>]/', $password)) {
            $errors[] = 'Password must contain at least one special character';
        } else {
            $score += 1;
        }

        // Common password check
        $commonPasswords = [
            'password', '123456', '123456789', 'qwerty', 'abc123',
            'password123', 'admin', 'letmein', 'welcome', 'monkey'
        ];

        if (in_array(strtolower($password), $commonPasswords)) {
            $errors[] = 'Password is too common';
            $score -= 2;
        }

        $strength = 'weak';
        if ($score >= 4) {
            $strength = 'strong';
        } elseif ($score >= 2) {
            $strength = 'medium';
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors,
            'score' => max(0, $score),
            'strength' => $strength,
        ];
    }

    /**
     * Check if IP is from a suspicious location or known threat
     */
    public static function checkSuspiciousIP(string $ip): array
    {
        // This is a basic implementation
        // In production, you might want to integrate with threat intelligence APIs
        
        $suspiciousRanges = [
            // Add known malicious IP ranges here
            '10.0.0.0/8',     // Private networks (shouldn't be external)
            '**********/12',  // Private networks
            '***********/16', // Private networks
        ];

        $isSuspicious = false;
        $reason = '';

        foreach ($suspiciousRanges as $range) {
            if (self::ipInRange($ip, $range)) {
                $isSuspicious = true;
                $reason = "IP in suspicious range: {$range}";
                break;
            }
        }

        return [
            'suspicious' => $isSuspicious,
            'reason' => $reason,
            'ip' => $ip,
        ];
    }

    /**
     * Check if IP is in range
     */
    private static function ipInRange(string $ip, string $range): bool
    {
        if (strpos($range, '/') === false) {
            return $ip === $range;
        }

        list($subnet, $bits) = explode('/', $range);
        $ip = ip2long($ip);
        $subnet = ip2long($subnet);
        $mask = -1 << (32 - $bits);
        $subnet &= $mask;

        return ($ip & $mask) === $subnet;
    }

    /**
     * Generate CSRF token for forms
     */
    public static function generateCSRFToken(): string
    {
        return csrf_token();
    }

    /**
     * Validate file upload security
     */
    public static function validateFileUpload($file): array
    {
        $errors = [];

        // Check file size (max 10MB)
        if ($file->getSize() > 10 * 1024 * 1024) {
            $errors[] = 'File size exceeds 10MB limit';
        }

        // Check file extension
        $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx', 'txt'];
        $extension = strtolower($file->getClientOriginalExtension());

        if (!in_array($extension, $allowedExtensions)) {
            $errors[] = 'File type not allowed';
        }

        // Check MIME type
        $allowedMimeTypes = [
            'image/jpeg', 'image/png', 'image/gif',
            'application/pdf', 'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'text/plain'
        ];

        if (!in_array($file->getMimeType(), $allowedMimeTypes)) {
            $errors[] = 'Invalid file type';
        }

        // Check for executable files
        $dangerousExtensions = ['exe', 'bat', 'cmd', 'com', 'pif', 'scr', 'vbs', 'js', 'jar', 'php', 'asp'];
        if (in_array($extension, $dangerousExtensions)) {
            $errors[] = 'Executable files are not allowed';
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors,
        ];
    }

    /**
     * Clean filename for safe storage
     */
    public static function sanitizeFilename(string $filename): string
    {
        // Remove path traversal attempts
        $filename = basename($filename);
        
        // Remove special characters
        $filename = preg_replace('/[^a-zA-Z0-9._-]/', '_', $filename);
        
        // Remove multiple dots
        $filename = preg_replace('/\.+/', '.', $filename);
        
        // Ensure it doesn't start with a dot
        $filename = ltrim($filename, '.');
        
        // Limit length
        if (strlen($filename) > 255) {
            $extension = pathinfo($filename, PATHINFO_EXTENSION);
            $name = pathinfo($filename, PATHINFO_FILENAME);
            $filename = substr($name, 0, 255 - strlen($extension) - 1) . '.' . $extension;
        }

        return $filename;
    }
}
