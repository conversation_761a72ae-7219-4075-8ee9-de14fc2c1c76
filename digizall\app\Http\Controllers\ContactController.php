<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Requests\ContactRequest;
use App\Models\ContactMessage as Contact;
use App\Models\MetaTag;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;

class ContactController extends Controller
{
    /**
     * Handle contact form submission.
     */
    public function store(ContactRequest $request)
    {
        try {
            // Create contact record
            $contact = Contact::create([
                'name' => $request->name,
                'email' => $request->email,
                'phone' => $request->phone,
                'company' => $request->company,
                'subject' => $request->subject,
                'message' => $request->message,
                'preferred_contact' => $request->preferred_contact,
                'marketing_consent' => $request->boolean('marketing_consent'),
                'status' => 'pending',
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ]);

            // Send notification emails
            $this->sendContactNotifications($contact);

            return redirect()->route('contact.success')
                ->with('success', 'Your message has been sent successfully! We\'ll get back to you soon.');

        } catch (\Exception $e) {
            Log::error('Contact submission failed: ' . $e->getMessage());

            return back()
                ->withInput()
                ->with('error', 'There was an error sending your message. Please try again or contact us directly.');
        }
    }

    /**
     * Show contact success page.
     */
    public function success()
    {
        return view('contact.success');
    }

    /**
     * Send contact notification emails.
     */
    private function sendContactNotifications(Contact $contact)
    {
        try {
            // Send confirmation email to client
            Mail::send('emails.contact.client-confirmation', compact('contact'), function ($message) use ($contact) {
                $message->to($contact->email, $contact->name)
                    ->subject('Message Received - DIGIZALL')
                    ->from(config('mail.from.address'), config('mail.from.name'));
            });

            // Send notification email to admin
            Mail::send('emails.contact.admin-notification', compact('contact'), function ($message) use ($contact) {
                $message->to(config('mail.admin_email', '<EMAIL>'))
                    ->subject('New Contact Message - ' . $contact->subject)
                    ->from(config('mail.from.address'), config('mail.from.name'));
            });

        } catch (\Exception $e) {
            Log::error('Failed to send contact emails: ' . $e->getMessage());
        }
    }
}
