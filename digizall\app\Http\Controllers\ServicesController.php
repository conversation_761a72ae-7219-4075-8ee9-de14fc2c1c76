<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Service;
use App\Models\ServiceFeature;
use App\Models\ServicePricing;
use App\Models\MetaTag;

class ServicesController extends Controller
{
    /**
     * Display a listing of services.
     */
    public function index()
    {
        $services = Service::active()
            ->with(['features', 'pricing'])
            ->orderBy('sort_order')
            ->orderBy('title')
            ->get();

        // Group services by category
        $servicesByCategory = $services->groupBy('category');

        // Get meta tags for services page
        $metaTags = MetaTag::getForPage('services');

        return view('services.index', compact('services', 'servicesByCategory', 'metaTags'));
    }

    /**
     * Display the specified service.
     */
    public function show($slug)
    {
        $service = Service::active()
            ->with(['features' => function($query) {
                $query->orderBy('sort_order');
            }, 'pricing' => function($query) {
                $query->active()->orderBy('sort_order');
            }])
            ->where('slug', $slug)
            ->firstOrFail();

        // Get related services (same category, excluding current)
        $relatedServices = Service::active()
            ->where('category', $service->category)
            ->where('id', '!=', $service->id)
            ->limit(3)
            ->get();

        // Get meta tags for this service
        $metaTags = MetaTag::getForService($slug);

        return view('services.show', compact('service', 'relatedServices', 'metaTags'));
    }

    /**
     * Display services by category.
     */
    public function category($category)
    {
        $services = Service::active()
            ->where('category', $category)
            ->with(['features', 'pricing'])
            ->orderBy('sort_order')
            ->orderBy('title')
            ->get();

        $categoryName = ucwords(str_replace('-', ' ', $category));

        return view('services.category', compact('services', 'category', 'categoryName'));
    }
}
