<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class Employee extends Model
{
    use HasFactory;

    protected $fillable = [
        'employee_id',
        'user_id',
        'department_id',
        'position',
        'employment_type',
        'hire_date',
        'termination_date',
        'salary',
        'phone',
        'address',
        'date_of_birth',
        'emergency_contact_name',
        'emergency_contact_phone',
        'work_schedule',
        'is_active',
    ];

    protected $casts = [
        'hire_date' => 'date',
        'termination_date' => 'date',
        'date_of_birth' => 'date',
        'work_schedule' => 'array',
        'is_active' => 'boolean',
        'salary' => 'decimal:2',
    ];

    /**
     * Get the user associated with the employee.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the department of the employee.
     */
    public function department()
    {
        return $this->belongsTo(Department::class);
    }

    /**
     * Get all attendance records for this employee.
     */
    public function attendances()
    {
        return $this->hasMany(Attendance::class);
    }

    /**
     * Get today's attendance record.
     */
    public function todayAttendance()
    {
        return $this->hasOne(Attendance::class)->whereDate('date', today());
    }

    /**
     * Get attendance records for current month.
     */
    public function currentMonthAttendances()
    {
        return $this->hasMany(Attendance::class)
                    ->whereYear('date', now()->year)
                    ->whereMonth('date', now()->month);
    }

    /**
     * Scope to get only active employees.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Get the employee's full name.
     */
    public function getFullNameAttribute()
    {
        return $this->user->name;
    }

    /**
     * Get the employee's email.
     */
    public function getEmailAttribute()
    {
        return $this->user->email;
    }

    /**
     * Check if employee is currently checked in.
     */
    public function getIsCheckedInAttribute()
    {
        $todayAttendance = $this->todayAttendance;
        return $todayAttendance && $todayAttendance->check_in && !$todayAttendance->check_out;
    }

    /**
     * Get current work status.
     */
    public function getCurrentStatusAttribute()
    {
        $todayAttendance = $this->todayAttendance;

        if (!$todayAttendance) {
            return 'Not checked in';
        }

        if ($todayAttendance->check_in && !$todayAttendance->check_out) {
            if ($todayAttendance->break_start && !$todayAttendance->break_end) {
                return 'On break';
            }
            return 'Working';
        }

        if ($todayAttendance->check_out) {
            return 'Checked out';
        }

        return 'Unknown';
    }

    /**
     * Get total working hours for current month.
     */
    public function getCurrentMonthHoursAttribute()
    {
        return $this->currentMonthAttendances()
                    ->whereNotNull('total_hours')
                    ->sum('total_hours');
    }

    /**
     * Generate unique employee ID.
     */
    public static function generateEmployeeId()
    {
        $prefix = 'EMP';
        $year = date('Y');
        $lastEmployee = self::where('employee_id', 'like', $prefix . $year . '%')
                           ->orderBy('employee_id', 'desc')
                           ->first();

        if ($lastEmployee) {
            $lastNumber = (int) substr($lastEmployee->employee_id, -4);
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return $prefix . $year . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
    }
}
