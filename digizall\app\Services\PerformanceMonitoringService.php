<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class PerformanceMonitoringService
{
    const CACHE_KEY_PREFIX = 'performance_monitor';
    const SLOW_QUERY_THRESHOLD = 1000; // milliseconds

    /**
     * Start monitoring a request
     */
    public static function startRequest($requestId = null)
    {
        $requestId = $requestId ?: uniqid('req_');
        
        Cache::put(self::CACHE_KEY_PREFIX . ".request.{$requestId}", [
            'start_time' => microtime(true),
            'start_memory' => memory_get_usage(true),
            'queries' => [],
            'cache_hits' => 0,
            'cache_misses' => 0,
        ], 300); // 5 minutes
        
        return $requestId;
    }

    /**
     * End monitoring a request
     */
    public static function endRequest($requestId)
    {
        $data = Cache::get(self::CACHE_KEY_PREFIX . ".request.{$requestId}");
        
        if (!$data) {
            return null;
        }

        $endTime = microtime(true);
        $endMemory = memory_get_usage(true);
        
        $metrics = [
            'request_id' => $requestId,
            'duration_ms' => round(($endTime - $data['start_time']) * 1000, 2),
            'memory_usage_mb' => round(($endMemory - $data['start_memory']) / 1024 / 1024, 2),
            'peak_memory_mb' => round(memory_get_peak_usage(true) / 1024 / 1024, 2),
            'query_count' => count($data['queries']),
            'slow_queries' => array_filter($data['queries'], function($query) {
                return $query['duration_ms'] > self::SLOW_QUERY_THRESHOLD;
            }),
            'cache_hits' => $data['cache_hits'],
            'cache_misses' => $data['cache_misses'],
            'timestamp' => now(),
        ];

        // Store metrics for analysis
        self::storeMetrics($metrics);
        
        // Clean up temporary data
        Cache::forget(self::CACHE_KEY_PREFIX . ".request.{$requestId}");
        
        return $metrics;
    }

    /**
     * Log a database query
     */
    public static function logQuery($requestId, $sql, $bindings, $duration)
    {
        $data = Cache::get(self::CACHE_KEY_PREFIX . ".request.{$requestId}");
        
        if ($data) {
            $data['queries'][] = [
                'sql' => $sql,
                'bindings' => $bindings,
                'duration_ms' => $duration,
                'is_slow' => $duration > self::SLOW_QUERY_THRESHOLD,
            ];
            
            Cache::put(self::CACHE_KEY_PREFIX . ".request.{$requestId}", $data, 300);
        }
    }

    /**
     * Log cache hit/miss
     */
    public static function logCacheEvent($requestId, $type)
    {
        $data = Cache::get(self::CACHE_KEY_PREFIX . ".request.{$requestId}");
        
        if ($data) {
            if ($type === 'hit') {
                $data['cache_hits']++;
            } else {
                $data['cache_misses']++;
            }
            
            Cache::put(self::CACHE_KEY_PREFIX . ".request.{$requestId}", $data, 300);
        }
    }

    /**
     * Store performance metrics
     */
    private static function storeMetrics($metrics)
    {
        // Store in cache for recent metrics
        $recentKey = self::CACHE_KEY_PREFIX . '.recent';
        $recent = Cache::get($recentKey, []);
        
        array_unshift($recent, $metrics);
        $recent = array_slice($recent, 0, 100); // Keep last 100 requests
        
        Cache::put($recentKey, $recent, 3600); // 1 hour
        
        // Log slow requests
        if ($metrics['duration_ms'] > 2000) { // > 2 seconds
            Log::warning('Slow request detected', $metrics);
        }
        
        // Store daily aggregates
        self::updateDailyAggregates($metrics);
    }

    /**
     * Update daily performance aggregates
     */
    private static function updateDailyAggregates($metrics)
    {
        $date = now()->format('Y-m-d');
        $key = self::CACHE_KEY_PREFIX . ".daily.{$date}";
        
        $daily = Cache::get($key, [
            'date' => $date,
            'request_count' => 0,
            'total_duration_ms' => 0,
            'total_memory_mb' => 0,
            'total_queries' => 0,
            'slow_requests' => 0,
            'cache_hits' => 0,
            'cache_misses' => 0,
        ]);
        
        $daily['request_count']++;
        $daily['total_duration_ms'] += $metrics['duration_ms'];
        $daily['total_memory_mb'] += $metrics['memory_usage_mb'];
        $daily['total_queries'] += $metrics['query_count'];
        $daily['cache_hits'] += $metrics['cache_hits'];
        $daily['cache_misses'] += $metrics['cache_misses'];
        
        if ($metrics['duration_ms'] > 2000) {
            $daily['slow_requests']++;
        }
        
        Cache::put($key, $daily, 86400 * 7); // Keep for 7 days
    }

    /**
     * Get recent performance metrics
     */
    public static function getRecentMetrics($limit = 50)
    {
        $recent = Cache::get(self::CACHE_KEY_PREFIX . '.recent', []);
        return array_slice($recent, 0, $limit);
    }

    /**
     * Get daily performance summary
     */
    public static function getDailySummary($date = null)
    {
        $date = $date ?: now()->format('Y-m-d');
        $key = self::CACHE_KEY_PREFIX . ".daily.{$date}";
        
        $daily = Cache::get($key);
        
        if (!$daily) {
            return null;
        }
        
        // Calculate averages
        $daily['avg_duration_ms'] = $daily['request_count'] > 0 
            ? round($daily['total_duration_ms'] / $daily['request_count'], 2) 
            : 0;
            
        $daily['avg_memory_mb'] = $daily['request_count'] > 0 
            ? round($daily['total_memory_mb'] / $daily['request_count'], 2) 
            : 0;
            
        $daily['avg_queries'] = $daily['request_count'] > 0 
            ? round($daily['total_queries'] / $daily['request_count'], 2) 
            : 0;
            
        $daily['cache_hit_rate'] = ($daily['cache_hits'] + $daily['cache_misses']) > 0 
            ? round($daily['cache_hits'] / ($daily['cache_hits'] + $daily['cache_misses']) * 100, 2) 
            : 0;
        
        return $daily;
    }

    /**
     * Get performance trends (last 7 days)
     */
    public static function getPerformanceTrends()
    {
        $trends = [];
        
        for ($i = 6; $i >= 0; $i--) {
            $date = now()->subDays($i)->format('Y-m-d');
            $summary = self::getDailySummary($date);
            
            if ($summary) {
                $trends[] = $summary;
            }
        }
        
        return $trends;
    }

    /**
     * Get slow queries summary
     */
    public static function getSlowQueriesSummary()
    {
        $recent = self::getRecentMetrics(100);
        $slowQueries = [];
        
        foreach ($recent as $metric) {
            if (!empty($metric['slow_queries'])) {
                foreach ($metric['slow_queries'] as $query) {
                    $slowQueries[] = [
                        'sql' => $query['sql'],
                        'duration_ms' => $query['duration_ms'],
                        'timestamp' => $metric['timestamp'],
                    ];
                }
            }
        }
        
        // Sort by duration
        usort($slowQueries, function($a, $b) {
            return $b['duration_ms'] <=> $a['duration_ms'];
        });
        
        return array_slice($slowQueries, 0, 20); // Top 20 slow queries
    }

    /**
     * Get system resource usage
     */
    public static function getSystemResources()
    {
        return [
            'memory_usage' => [
                'current_mb' => round(memory_get_usage(true) / 1024 / 1024, 2),
                'peak_mb' => round(memory_get_peak_usage(true) / 1024 / 1024, 2),
                'limit_mb' => self::getMemoryLimit(),
            ],
            'disk_usage' => self::getDiskUsage(),
            'cache_stats' => self::getCacheStats(),
            'database_connections' => self::getDatabaseConnections(),
        ];
    }

    /**
     * Get memory limit in MB
     */
    private static function getMemoryLimit()
    {
        $limit = ini_get('memory_limit');
        
        if ($limit == -1) {
            return 'Unlimited';
        }
        
        $value = (int) $limit;
        $unit = strtoupper(substr($limit, -1));
        
        switch ($unit) {
            case 'G':
                return $value * 1024;
            case 'M':
                return $value;
            case 'K':
                return $value / 1024;
            default:
                return round($value / 1024 / 1024, 2);
        }
    }

    /**
     * Get disk usage information
     */
    private static function getDiskUsage()
    {
        $path = storage_path();
        
        return [
            'free_gb' => round(disk_free_space($path) / 1024 / 1024 / 1024, 2),
            'total_gb' => round(disk_total_space($path) / 1024 / 1024 / 1024, 2),
            'used_percentage' => round((1 - disk_free_space($path) / disk_total_space($path)) * 100, 2),
        ];
    }

    /**
     * Get cache statistics
     */
    private static function getCacheStats()
    {
        // This would depend on your cache driver
        // For Redis, you could get more detailed stats
        return [
            'driver' => config('cache.default'),
            'status' => 'active', // You could ping the cache to verify
        ];
    }

    /**
     * Get database connection count
     */
    private static function getDatabaseConnections()
    {
        try {
            $result = DB::selectOne("SHOW STATUS LIKE 'Threads_connected'");
            return (int) $result->Value;
        } catch (\Exception $e) {
            return 'Unknown';
        }
    }

    /**
     * Generate performance report
     */
    public static function generateReport()
    {
        return [
            'summary' => self::getDailySummary(),
            'trends' => self::getPerformanceTrends(),
            'slow_queries' => self::getSlowQueriesSummary(),
            'system_resources' => self::getSystemResources(),
            'recent_metrics' => self::getRecentMetrics(10),
            'generated_at' => now(),
        ];
    }

    /**
     * Clear old performance data
     */
    public static function cleanup()
    {
        // Clear data older than 7 days
        for ($i = 7; $i < 30; $i++) {
            $date = now()->subDays($i)->format('Y-m-d');
            $key = self::CACHE_KEY_PREFIX . ".daily.{$date}";
            Cache::forget($key);
        }
    }
}
