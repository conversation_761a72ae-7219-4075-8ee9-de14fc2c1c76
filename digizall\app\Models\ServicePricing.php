<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ServicePricing extends Model
{
    use HasFactory;

    protected $table = 'service_pricing';

    protected $fillable = [
        'service_id',
        'plan_name',
        'price',
        'currency',
        'billing_period',
        'features',
        'is_popular',
        'is_active',
        'sort_order'
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'features' => 'array',
        'is_popular' => 'boolean',
        'is_active' => 'boolean'
    ];

    /**
     * Get the service that owns the pricing.
     */
    public function service()
    {
        return $this->belongsTo(Service::class);
    }

    /**
     * Scope for active pricing plans.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for popular pricing plans.
     */
    public function scopePopular($query)
    {
        return $query->where('is_popular', true);
    }

    /**
     * Get formatted price.
     */
    public function getFormattedPriceAttribute(): string
    {
        $symbol = $this->currency === 'USD' ? '$' : $this->currency;
        return $symbol . number_format($this->price, 2);
    }

    /**
     * Get billing period text.
     */
    public function getBillingPeriodTextAttribute(): string
    {
        return match($this->billing_period) {
            'monthly' => 'per month',
            'yearly' => 'per year',
            'one-time' => 'one-time',
            default => $this->billing_period
        };
    }
}
