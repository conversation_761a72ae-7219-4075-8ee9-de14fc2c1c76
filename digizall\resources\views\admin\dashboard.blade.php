@extends('layouts.admin')

@section('title', 'Dashboard')
@section('page-title', 'Dashboard')

@section('content')
<div class="space-y-6">
    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6">
        <!-- Total Users -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                        <i class="fas fa-users text-white text-sm"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Users</p>
                    <p class="text-2xl font-semibold text-gray-900">{{ number_format($stats['total_users']) }}</p>
                </div>
            </div>
        </div>

        <!-- Total Services -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                        <i class="fas fa-cogs text-white text-sm"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Services</p>
                    <p class="text-2xl font-semibold text-gray-900">{{ number_format($stats['total_services']) }}</p>
                </div>
            </div>
        </div>

        <!-- Total Blog Posts -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                        <i class="fas fa-blog text-white text-sm"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Blog Posts</p>
                    <p class="text-2xl font-semibold text-gray-900">{{ number_format($stats['total_blog_posts']) }}</p>
                </div>
            </div>
        </div>

        <!-- Total Quotes -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                        <i class="fas fa-quote-left text-white text-sm"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Quotes</p>
                    <p class="text-2xl font-semibold text-gray-900">{{ number_format($stats['total_quotes']) }}</p>
                </div>
            </div>
        </div>

        <!-- Total Contacts -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-red-500 rounded-md flex items-center justify-center">
                        <i class="fas fa-envelope text-white text-sm"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Contacts</p>
                    <p class="text-2xl font-semibold text-gray-900">{{ number_format($stats['total_contacts']) }}</p>
                </div>
            </div>
        </div>

        <!-- Total Testimonials -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-indigo-500 rounded-md flex items-center justify-center">
                        <i class="fas fa-star text-white text-sm"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Testimonials</p>
                    <p class="text-2xl font-semibold text-gray-900">{{ number_format($stats['total_testimonials']) }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Pending Items Alert -->
    @if($pendingQuotes > 0 || $pendingContacts > 0)
        <div class="bg-red-50 border border-red-200 rounded-lg p-4">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-exclamation-triangle text-red-400"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-red-800">Attention Required</h3>
                    <div class="mt-2 text-sm text-red-700">
                        <ul class="list-disc list-inside space-y-1">
                            @if($pendingQuotes > 0)
                                <li>
                                    <a href="{{ route('admin.quotes.index', ['status' => 'pending']) }}" class="underline hover:text-red-900">
                                        {{ $pendingQuotes }} pending quote{{ $pendingQuotes > 1 ? 's' : '' }} need{{ $pendingQuotes === 1 ? 's' : '' }} review
                                    </a>
                                </li>
                            @endif
                            @if($pendingContacts > 0)
                                <li>
                                    <a href="{{ route('admin.contacts.index', ['status' => 'pending']) }}" class="underline hover:text-red-900">
                                        {{ $pendingContacts }} pending contact{{ $pendingContacts > 1 ? 's' : '' }} need{{ $pendingContacts === 1 ? 's' : '' }} response
                                    </a>
                                </li>
                            @endif
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    @endif

    <!-- Charts Row -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Monthly Quotes Chart -->
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Monthly Quotes</h3>
            <div class="h-64">
                <canvas id="quotesChart"></canvas>
            </div>
        </div>

        <!-- Monthly Contacts Chart -->
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Monthly Contacts</h3>
            <div class="h-64">
                <canvas id="contactsChart"></canvas>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Recent Quotes -->
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-medium text-gray-900">Recent Quotes</h3>
                    <a href="{{ route('admin.quotes.index') }}" class="text-sm text-blue-600 hover:text-blue-500">View all</a>
                </div>
            </div>
            <div class="p-6">
                @if($recentQuotes->count() > 0)
                    <div class="space-y-4">
                        @foreach($recentQuotes as $quote)
                            <div class="flex items-center justify-between">
                                <div class="flex-1 min-w-0">
                                    <p class="text-sm font-medium text-gray-900 truncate">{{ $quote->name }}</p>
                                    <p class="text-sm text-gray-500">{{ ucwords(str_replace('-', ' ', $quote->service)) }}</p>
                                    <p class="text-xs text-gray-400">{{ $quote->created_at->diffForHumans() }}</p>
                                </div>
                                <div class="flex-shrink-0">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        {{ $quote->status === 'pending' ? 'bg-yellow-100 text-yellow-800' : 
                                           ($quote->status === 'quoted' ? 'bg-blue-100 text-blue-800' : 
                                           ($quote->status === 'accepted' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800')) }}">
                                        {{ ucfirst($quote->status) }}
                                    </span>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <p class="text-gray-500 text-sm">No quotes yet.</p>
                @endif
            </div>
        </div>

        <!-- Recent Contacts -->
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-medium text-gray-900">Recent Contacts</h3>
                    <a href="{{ route('admin.contacts.index') }}" class="text-sm text-blue-600 hover:text-blue-500">View all</a>
                </div>
            </div>
            <div class="p-6">
                @if($recentContacts->count() > 0)
                    <div class="space-y-4">
                        @foreach($recentContacts as $contact)
                            <div class="flex items-center justify-between">
                                <div class="flex-1 min-w-0">
                                    <p class="text-sm font-medium text-gray-900 truncate">{{ $contact->name }}</p>
                                    <p class="text-sm text-gray-500">{{ ucwords(str_replace('-', ' ', $contact->subject)) }}</p>
                                    <p class="text-xs text-gray-400">{{ $contact->created_at->diffForHumans() }}</p>
                                </div>
                                <div class="flex-shrink-0">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        {{ $contact->status === 'pending' ? 'bg-yellow-100 text-yellow-800' : 
                                           ($contact->status === 'responded' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800') }}">
                                        {{ ucfirst($contact->status) }}
                                    </span>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <p class="text-gray-500 text-sm">No contacts yet.</p>
                @endif
            </div>
        </div>

        <!-- Recent Blog Posts -->
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-medium text-gray-900">Recent Blog Posts</h3>
                    <a href="{{ route('admin.blog.index') }}" class="text-sm text-blue-600 hover:text-blue-500">View all</a>
                </div>
            </div>
            <div class="p-6">
                @if($recentBlogPosts->count() > 0)
                    <div class="space-y-4">
                        @foreach($recentBlogPosts as $post)
                            <div class="flex items-center justify-between">
                                <div class="flex-1 min-w-0">
                                    <p class="text-sm font-medium text-gray-900 truncate">{{ $post->title }}</p>
                                    <p class="text-sm text-gray-500">{{ $post->category->name ?? 'Uncategorized' }}</p>
                                    <p class="text-xs text-gray-400">{{ $post->created_at->diffForHumans() }}</p>
                                </div>
                                <div class="flex-shrink-0">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        {{ $post->status === 'published' ? 'bg-green-100 text-green-800' : 
                                           ($post->status === 'draft' ? 'bg-gray-100 text-gray-800' : 'bg-yellow-100 text-yellow-800') }}">
                                        {{ ucfirst($post->status) }}
                                    </span>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <p class="text-gray-500 text-sm">No blog posts yet.</p>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Monthly Quotes Chart
    const quotesCtx = document.getElementById('quotesChart').getContext('2d');
    new Chart(quotesCtx, {
        type: 'line',
        data: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
            datasets: [{
                label: 'Quotes',
                data: @json(array_values($monthlyQuotes)),
                borderColor: 'rgb(59, 130, 246)',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Monthly Contacts Chart
    const contactsCtx = document.getElementById('contactsChart').getContext('2d');
    new Chart(contactsCtx, {
        type: 'line',
        data: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
            datasets: [{
                label: 'Contacts',
                data: @json(array_values($monthlyContacts)),
                borderColor: 'rgb(16, 185, 129)',
                backgroundColor: 'rgba(16, 185, 129, 0.1)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
});
</script>
@endpush
