@extends('layouts.app')

@section('title', $metaTags->meta_title ?? 'Blog - DIGIZALL')
@section('meta_description', $metaTags->meta_description ?? 'Stay updated with the latest trends, tips, and insights in digital marketing, web development, and technology.')
@section('meta_keywords', $metaTags->meta_keywords ?? 'blog, digital marketing, web development, SEO, technology, DIGIZALL')

@if($metaTags)
    @section('og_title', $metaTags->og_title)
    @section('og_description', $metaTags->og_description)
    @section('og_image', $metaTags->og_image_url)
@endif

@section('content')

<!-- Page Header -->
<section class="bg-gradient-to-r from-primary-600 to-secondary-600 text-white py-20">
    <div class="container-custom">
        <div class="text-center" data-aos="fade-up">
            <h1 class="heading-primary text-white mb-4">
                DIGIZALL Blog
            </h1>
            <p class="text-xl text-gray-200 max-w-2xl mx-auto">
                Stay updated with the latest trends, tips, and insights in digital marketing and web development.
            </p>
        </div>
    </div>
</section>

<!-- Search and Filter Section -->
<section class="py-8 bg-gray-50">
    <div class="container-custom">
        <div class="flex flex-col lg:flex-row gap-6 items-center justify-between">
            <!-- Search Form -->
            <div class="flex-1 max-w-md">
                <form method="GET" action="{{ route('blog.index') }}" class="relative">
                    <input type="text" 
                           name="search" 
                           value="{{ request('search') }}"
                           placeholder="Search articles..." 
                           class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                    <button type="submit" class="absolute inset-y-0 right-0 pr-3 flex items-center">
                        <span class="bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700 transition-colors">
                            Search
                        </span>
                    </button>
                </form>
            </div>

            <!-- Category Filter -->
            <div class="flex flex-wrap gap-2">
                <a href="{{ route('blog.index') }}" 
                   class="px-4 py-2 rounded-full text-sm font-medium transition-colors {{ !request('category') ? 'bg-primary-600 text-white' : 'bg-white text-gray-600 hover:bg-gray-100' }}">
                    All Posts
                </a>
                @foreach($categories as $category)
                    <a href="{{ route('blog.index', ['category' => $category->slug]) }}" 
                       class="px-4 py-2 rounded-full text-sm font-medium transition-colors {{ request('category') === $category->slug ? 'bg-primary-600 text-white' : 'bg-white text-gray-600 hover:bg-gray-100' }}">
                        {{ $category->name }} ({{ $category->published_posts_count }})
                    </a>
                @endforeach
            </div>
        </div>
    </div>
</section>

<!-- Blog Content -->
<section class="section-padding">
    <div class="container-custom">
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-12">
            <!-- Main Content -->
            <div class="lg:col-span-3">
                @if($posts->count() > 0)
                    <!-- Blog Posts Grid -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
                        @foreach($posts as $post)
                            <div data-aos="fade-up" data-aos-delay="{{ $loop->index * 100 }}">
                                <x-blog-card :post="$post" :featured="$post->is_featured" />
                            </div>
                        @endforeach
                    </div>

                    <!-- Pagination -->
                    <div class="flex justify-center" data-aos="fade-up">
                        {{ $posts->appends(request()->query())->links() }}
                    </div>
                @else
                    <!-- No Posts Found -->
                    <div class="text-center py-16">
                        <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                            <i class="fas fa-search text-3xl text-gray-400"></i>
                        </div>
                        <h2 class="text-2xl font-bold text-gray-900 mb-4">No Posts Found</h2>
                        <p class="text-gray-600 mb-8">
                            @if(request('search'))
                                No posts found for "{{ request('search') }}". Try a different search term.
                            @else
                                No blog posts are available at the moment.
                            @endif
                        </p>
                        @if(request('search') || request('category'))
                            <a href="{{ route('blog.index') }}" class="btn-primary">
                                View All Posts
                            </a>
                        @endif
                    </div>
                @endif
            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-1">
                <!-- Featured Posts -->
                @if($featuredPosts->count() > 0)
                    <div class="bg-white rounded-xl shadow-lg p-6 mb-8" data-aos="fade-up">
                        <h3 class="text-xl font-bold text-gray-900 mb-6">Featured Posts</h3>
                        <div class="space-y-6">
                            @foreach($featuredPosts as $featuredPost)
                                <article class="flex space-x-4">
                                    <div class="flex-shrink-0">
                                        <img src="{{ $featuredPost->featured_image_url }}" 
                                             alt="{{ $featuredPost->title }}" 
                                             class="w-16 h-16 object-cover rounded-lg">
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <h4 class="text-sm font-semibold text-gray-900 line-clamp-2 mb-1">
                                            <a href="{{ $featuredPost->url }}" class="hover:text-primary-600">
                                                {{ $featuredPost->title }}
                                            </a>
                                        </h4>
                                        <p class="text-xs text-gray-500">{{ $featuredPost->formatted_published_date }}</p>
                                    </div>
                                </article>
                            @endforeach
                        </div>
                    </div>
                @endif

                <!-- Categories -->
                @if($categories->count() > 0)
                    <div class="bg-white rounded-xl shadow-lg p-6 mb-8" data-aos="fade-up" data-aos-delay="100">
                        <h3 class="text-xl font-bold text-gray-900 mb-6">Categories</h3>
                        <div class="space-y-3">
                            @foreach($categories as $category)
                                <a href="{{ route('blog.category', $category->slug) }}" 
                                   class="flex items-center justify-between text-gray-600 hover:text-primary-600 transition-colors">
                                    <span>{{ $category->name }}</span>
                                    <span class="bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-xs">
                                        {{ $category->published_posts_count }}
                                    </span>
                                </a>
                            @endforeach
                        </div>
                    </div>
                @endif

                <!-- Popular Tags -->
                @if($popularTags->count() > 0)
                    <div class="bg-white rounded-xl shadow-lg p-6" data-aos="fade-up" data-aos-delay="200">
                        <h3 class="text-xl font-bold text-gray-900 mb-6">Popular Tags</h3>
                        <div class="flex flex-wrap gap-2">
                            @foreach($popularTags as $tag)
                                <a href="{{ route('blog.tag', $tag->slug) }}" 
                                   class="inline-block px-3 py-1 bg-gray-100 text-gray-600 rounded-full text-sm hover:bg-primary-100 hover:text-primary-600 transition-colors">
                                    #{{ $tag->name }}
                                </a>
                            @endforeach
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
</section>

@endsection
