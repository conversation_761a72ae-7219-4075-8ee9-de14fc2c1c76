<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Services\AssetOptimizationService;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\File;

class AssetController extends Controller
{
    public function __construct()
    {
        $this->middleware('admin');
    }

    /**
     * Get asset optimization statistics
     */
    public function stats()
    {
        $stats = AssetOptimizationService::getOptimizationStats();
        
        // Add additional information
        $stats['optimization_date'] = $this->getLastOptimizationDate();
        $stats['total_size_mb'] = round($stats['total_size'] / 1024 / 1024, 2);
        $stats['vite_manifest_exists'] = File::exists(public_path('build/manifest.json'));
        $stats['assets_compiled'] = $this->checkAssetsCompiled();

        return response()->json($stats);
    }

    /**
     * Display asset management dashboard
     */
    public function index()
    {
        $stats = AssetOptimizationService::getOptimizationStats();
        $buildInfo = $this->getBuildInformation();
        $assetFiles = $this->getAssetFilesList();

        return view('admin.assets.index', compact('stats', 'buildInfo', 'assetFiles'));
    }

    /**
     * Optimize assets
     */
    public function optimize(Request $request)
    {
        try {
            $options = $request->only(['minify_css', 'minify_js', 'optimize_images', 'generate_webp']);
            
            // Run asset optimization
            $result = AssetOptimizationService::optimizeAssets($options);
            
            return response()->json([
                'success' => true,
                'message' => 'Assets optimized successfully',
                'result' => $result
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Asset optimization failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Build assets using Vite
     */
    public function build(Request $request)
    {
        try {
            $environment = $request->get('environment', 'production');
            
            if ($environment === 'production') {
                Artisan::call('assets:build');
            } else {
                Artisan::call('assets:dev');
            }
            
            $output = Artisan::output();
            
            return response()->json([
                'success' => true,
                'message' => 'Assets built successfully',
                'output' => $output
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Asset build failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Clear asset cache
     */
    public function clearCache()
    {
        try {
            // Clear view cache
            Artisan::call('view:clear');
            
            // Clear route cache
            Artisan::call('route:clear');
            
            // Clear config cache
            Artisan::call('config:clear');
            
            return response()->json([
                'success' => true,
                'message' => 'Asset cache cleared successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Cache clear failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get asset file information
     */
    public function fileInfo($type)
    {
        $buildPath = public_path('build');
        $files = [];

        switch ($type) {
            case 'css':
                $files = File::glob($buildPath . '/assets/*.css');
                break;
            case 'js':
                $files = File::glob($buildPath . '/assets/*.js');
                break;
            case 'images':
                $imagePath = public_path('images');
                $extensions = ['jpg', 'jpeg', 'png', 'gif', 'svg', 'webp'];
                foreach ($extensions as $ext) {
                    $files = array_merge($files, File::glob($imagePath . '/*.' . $ext));
                }
                break;
        }

        $fileInfo = [];
        foreach ($files as $file) {
            $fileInfo[] = [
                'name' => basename($file),
                'size' => File::size($file),
                'size_mb' => round(File::size($file) / 1024 / 1024, 3),
                'modified' => File::lastModified($file),
                'path' => str_replace(public_path(), '', $file),
            ];
        }

        return response()->json($fileInfo);
    }

    /**
     * Download asset optimization report
     */
    public function downloadReport()
    {
        $stats = AssetOptimizationService::getOptimizationStats();
        $buildInfo = $this->getBuildInformation();
        
        $report = [
            'generated_at' => now()->toISOString(),
            'statistics' => $stats,
            'build_information' => $buildInfo,
            'recommendations' => $this->getOptimizationRecommendations($stats),
        ];

        $filename = 'asset_optimization_report_' . now()->format('Y-m-d_H-i-s') . '.json';
        
        return response()->json($report)
                         ->header('Content-Disposition', 'attachment; filename="' . $filename . '"');
    }

    /**
     * Get last optimization date
     */
    private function getLastOptimizationDate()
    {
        $manifestPath = public_path('build/manifest.json');
        
        if (File::exists($manifestPath)) {
            return date('Y-m-d H:i:s', File::lastModified($manifestPath));
        }
        
        return null;
    }

    /**
     * Check if assets are compiled
     */
    private function checkAssetsCompiled(): bool
    {
        $manifestPath = public_path('build/manifest.json');
        $buildPath = public_path('build/assets');
        
        return File::exists($manifestPath) && File::exists($buildPath) && count(File::files($buildPath)) > 0;
    }

    /**
     * Get build information
     */
    private function getBuildInformation(): array
    {
        $manifestPath = public_path('build/manifest.json');
        $buildInfo = [
            'manifest_exists' => File::exists($manifestPath),
            'build_date' => null,
            'vite_version' => null,
            'entry_points' => [],
        ];

        if ($buildInfo['manifest_exists']) {
            $buildInfo['build_date'] = date('Y-m-d H:i:s', File::lastModified($manifestPath));
            
            try {
                $manifest = json_decode(File::get($manifestPath), true);
                $buildInfo['entry_points'] = array_keys($manifest);
            } catch (\Exception $e) {
                $buildInfo['error'] = 'Failed to parse manifest file';
            }
        }

        return $buildInfo;
    }

    /**
     * Get list of asset files
     */
    private function getAssetFilesList(): array
    {
        $buildPath = public_path('build/assets');
        $files = [];

        if (File::exists($buildPath)) {
            $allFiles = File::allFiles($buildPath);
            
            foreach ($allFiles as $file) {
                $files[] = [
                    'name' => $file->getFilename(),
                    'size' => $file->getSize(),
                    'size_mb' => round($file->getSize() / 1024 / 1024, 3),
                    'type' => $file->getExtension(),
                    'modified' => $file->getMTime(),
                ];
            }
        }

        return $files;
    }

    /**
     * Get optimization recommendations
     */
    private function getOptimizationRecommendations(array $stats): array
    {
        $recommendations = [];

        if ($stats['total_size'] > 50 * 1024 * 1024) { // > 50MB
            $recommendations[] = 'Consider reducing asset bundle size - current size is large';
        }

        if ($stats['css_files'] > 10) {
            $recommendations[] = 'Consider combining CSS files to reduce HTTP requests';
        }

        if ($stats['js_files'] > 15) {
            $recommendations[] = 'Consider code splitting for JavaScript files';
        }

        if (!$stats['build_exists']) {
            $recommendations[] = 'Run asset build process to generate optimized files';
        }

        if (empty($recommendations)) {
            $recommendations[] = 'Asset optimization looks good!';
        }

        return $recommendations;
    }
}
