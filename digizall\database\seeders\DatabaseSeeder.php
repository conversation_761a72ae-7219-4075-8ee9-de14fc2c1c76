<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->command->info('🌱 Starting database seeding...');

        // Run individual seeders
        $this->call([
            AdminUserSeeder::class,
            ServiceCategorySeeder::class,
            BlogCategorySeeder::class,
            BlogTagSeeder::class,
            SettingsSeeder::class,
        ]);

        // Create services and related data
        $this->createServices();

        // Create blog data
        $this->createBlogData();

        // Create testimonials
        $this->createTestimonials();

        $this->command->info('✅ Database seeding completed successfully!');
    }

    private function createRoles()
    {
        $roles = [
            [
                'name' => 'admin',
                'display_name' => 'Administrator',
                'description' => 'Full system access',
                'permissions' => [
                    'manage_users', 'manage_roles', 'manage_services', 'manage_blog',
                    'manage_testimonials', 'manage_quotes', 'manage_contacts',
                    'manage_attendance', 'manage_settings', 'view_analytics'
                ],
                'is_active' => true
            ],
            [
                'name' => 'hr',
                'display_name' => 'Human Resources',
                'description' => 'HR management access',
                'permissions' => [
                    'manage_users', 'view_attendance', 'manage_leave_requests',
                    'view_reports'
                ],
                'is_active' => true
            ],
            [
                'name' => 'employee',
                'display_name' => 'Employee',
                'description' => 'Basic employee access',
                'permissions' => [
                    'view_attendance', 'submit_leave_requests', 'update_profile'
                ],
                'is_active' => true
            ]
        ];

        foreach ($roles as $roleData) {
            \App\Models\Role::create($roleData);
        }
    }

    private function createUsers()
    {
        // Create admin user
        $admin = \App\Models\User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'employee_id' => 'EMP001',
            'department' => 'Management',
            'position' => 'System Administrator',
            'hire_date' => now()->subYears(2),
            'is_active' => true
        ]);
        $admin->assignRole('admin');

        // Create HR user
        $hr = \App\Models\User::create([
            'name' => 'HR Manager',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'employee_id' => 'EMP002',
            'department' => 'Human Resources',
            'position' => 'HR Manager',
            'hire_date' => now()->subYear(),
            'is_active' => true
        ]);
        $hr->assignRole('hr');

        // Create sample employees
        for ($i = 3; $i <= 10; $i++) {
            $employee = \App\Models\User::create([
                'name' => "Employee $i",
                'email' => "employee$<EMAIL>",
                'password' => bcrypt('password'),
                'employee_id' => sprintf('EMP%03d', $i),
                'department' => fake()->randomElement(['Development', 'Marketing', 'Design', 'Sales']),
                'position' => fake()->jobTitle(),
                'hire_date' => fake()->dateTimeBetween('-2 years', '-1 month'),
                'hourly_rate' => fake()->randomFloat(2, 15, 50),
                'is_active' => true
            ]);
            $employee->assignRole('employee');
        }
    }

    private function createServices()
    {
        $services = [
            [
                'title' => 'Website Design & Development',
                'slug' => 'website-design-development',
                'short_description' => 'Custom website design and development services',
                'description' => 'We create stunning, responsive websites that drive results.',
                'category' => 'Web Development',
                'is_featured' => true,
                'is_active' => true,
                'sort_order' => 1
            ],
            [
                'title' => 'Web Application Development',
                'slug' => 'web-application-development',
                'short_description' => 'Custom web applications using modern technologies',
                'description' => 'Build powerful web applications with React, Laravel, and more.',
                'category' => 'Web Development',
                'is_featured' => true,
                'is_active' => true,
                'sort_order' => 2
            ],
            [
                'title' => 'Search Engine Optimization',
                'slug' => 'search-engine-optimization',
                'short_description' => 'Improve your website\'s search engine rankings',
                'description' => 'Comprehensive SEO services to boost your online visibility.',
                'category' => 'Digital Marketing',
                'is_featured' => true,
                'is_active' => true,
                'sort_order' => 3
            ],
            [
                'title' => 'Social Media Marketing',
                'slug' => 'social-media-marketing',
                'short_description' => 'Grow your brand on social media platforms',
                'description' => 'Strategic social media marketing to engage your audience.',
                'category' => 'Digital Marketing',
                'is_featured' => false,
                'is_active' => true,
                'sort_order' => 4
            ],
            [
                'title' => 'Content Writing',
                'slug' => 'content-writing',
                'short_description' => 'Professional content writing services',
                'description' => 'High-quality content that engages and converts.',
                'category' => 'Content Writing',
                'is_featured' => false,
                'is_active' => true,
                'sort_order' => 5
            ],
            [
                'title' => 'Graphic Design',
                'slug' => 'graphic-design',
                'short_description' => 'Creative graphic design solutions',
                'description' => 'Professional graphic design for all your branding needs.',
                'category' => 'Graphic Design',
                'is_featured' => false,
                'is_active' => true,
                'sort_order' => 6
            ]
        ];

        foreach ($services as $serviceData) {
            $service = \App\Models\Service::create($serviceData);

            // Create pricing for each service
            $this->createServicePricing($service);
        }
    }

    private function createServicePricing($service)
    {
        $pricingPlans = [
            [
                'plan_name' => 'Basic',
                'price' => fake()->randomFloat(2, 299, 599),
                'features' => ['Feature 1', 'Feature 2', 'Feature 3'],
                'is_popular' => false,
                'sort_order' => 1
            ],
            [
                'plan_name' => 'Standard',
                'price' => fake()->randomFloat(2, 599, 999),
                'features' => ['All Basic features', 'Feature 4', 'Feature 5', 'Feature 6'],
                'is_popular' => true,
                'sort_order' => 2
            ],
            [
                'plan_name' => 'Premium',
                'price' => fake()->randomFloat(2, 999, 1999),
                'features' => ['All Standard features', 'Feature 7', 'Feature 8', 'Priority Support'],
                'is_popular' => false,
                'sort_order' => 3
            ]
        ];

        foreach ($pricingPlans as $planData) {
            $service->pricing()->create($planData);
        }
    }

    private function createBlogData()
    {
        // Create blog categories
        $categories = [
            ['name' => 'Web Development', 'slug' => 'web-development', 'color' => '#3b82f6'],
            ['name' => 'Digital Marketing', 'slug' => 'digital-marketing', 'color' => '#10b981'],
            ['name' => 'SEO Tips', 'slug' => 'seo-tips', 'color' => '#f59e0b'],
            ['name' => 'Design', 'slug' => 'design', 'color' => '#8b5cf6']
        ];

        foreach ($categories as $categoryData) {
            \App\Models\BlogCategory::create($categoryData);
        }

        // Create blog tags
        $tags = ['Laravel', 'React', 'SEO', 'Marketing', 'Design', 'Tips', 'Tutorial'];
        foreach ($tags as $tag) {
            \App\Models\BlogTag::create([
                'name' => $tag,
                'slug' => \Illuminate\Support\Str::slug($tag)
            ]);
        }

        // Create blog posts
        $author = \App\Models\User::where('email', '<EMAIL>')->first();
        $categories = \App\Models\BlogCategory::all();
        $tags = \App\Models\BlogTag::all();

        for ($i = 1; $i <= 10; $i++) {
            $post = \App\Models\BlogPost::create([
                'title' => "Sample Blog Post $i",
                'slug' => "sample-blog-post-$i",
                'excerpt' => fake()->sentence(),
                'content' => fake()->paragraphs(5, true),
                'category_id' => $categories->random()->id,
                'author_id' => $author->id,
                'status' => 'published',
                'published_at' => fake()->dateTimeBetween('-6 months', 'now'),
                'is_featured' => fake()->boolean(20)
            ]);

            // Attach random tags
            $post->tags()->attach($tags->random(rand(1, 3))->pluck('id'));
        }
    }

    private function createTestimonials()
    {
        $testimonials = [
            [
                'client_name' => 'John Smith',
                'client_position' => 'CEO',
                'client_company' => 'Tech Solutions Inc',
                'testimonial' => 'DIGIZALL delivered an exceptional website that exceeded our expectations. Their team is professional and highly skilled.',
                'rating' => 5,
                'is_featured' => true,
                'is_active' => true,
                'sort_order' => 1
            ],
            [
                'client_name' => 'Sarah Johnson',
                'client_position' => 'Marketing Director',
                'client_company' => 'Growth Marketing Co',
                'testimonial' => 'The SEO services provided by DIGIZALL significantly improved our search rankings. Highly recommended!',
                'rating' => 5,
                'is_featured' => true,
                'is_active' => true,
                'sort_order' => 2
            ],
            [
                'client_name' => 'Mike Wilson',
                'client_position' => 'Founder',
                'client_company' => 'StartupXYZ',
                'testimonial' => 'Professional service and great results. DIGIZALL helped us establish a strong online presence.',
                'rating' => 4,
                'is_featured' => false,
                'is_active' => true,
                'sort_order' => 3
            ]
        ];

        foreach ($testimonials as $testimonialData) {
            \App\Models\Testimonial::create($testimonialData);
        }
    }

    private function createSettings()
    {
        $settings = [
            ['key' => 'site_name', 'value' => 'DIGIZALL', 'type' => 'text', 'group' => 'general'],
            ['key' => 'site_tagline', 'value' => 'Your Digital Success Partner', 'type' => 'text', 'group' => 'general'],
            ['key' => 'contact_email', 'value' => '<EMAIL>', 'type' => 'text', 'group' => 'contact'],
            ['key' => 'contact_phone', 'value' => '******-567-8900', 'type' => 'text', 'group' => 'contact'],
            ['key' => 'contact_address', 'value' => '123 Business St, City, State 12345', 'type' => 'text', 'group' => 'contact'],
            ['key' => 'facebook_url', 'value' => 'https://facebook.com/digizall', 'type' => 'text', 'group' => 'social'],
            ['key' => 'twitter_url', 'value' => 'https://twitter.com/digizall', 'type' => 'text', 'group' => 'social'],
            ['key' => 'linkedin_url', 'value' => 'https://linkedin.com/company/digizall', 'type' => 'text', 'group' => 'social'],
            ['key' => 'projects_completed', 'value' => '150', 'type' => 'number', 'group' => 'stats'],
            ['key' => 'happy_clients', 'value' => '120', 'type' => 'number', 'group' => 'stats'],
            ['key' => 'years_experience', 'value' => '8', 'type' => 'number', 'group' => 'stats'],
            ['key' => 'team_members', 'value' => '25', 'type' => 'number', 'group' => 'stats']
        ];

        foreach ($settings as $settingData) {
            \App\Models\Setting::create($settingData);
        }
    }
}
