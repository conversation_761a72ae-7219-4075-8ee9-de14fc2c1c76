<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ServiceRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check() && auth()->user()->isAdmin();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $serviceId = $this->route('service') ? $this->route('service')->id : null;

        return [
            'title' => 'required|string|max:255',
            'slug' => [
                'nullable',
                'string',
                'max:255',
                'regex:/^[a-z0-9]+(?:-[a-z0-9]+)*$/',
                Rule::unique('services', 'slug')->ignore($serviceId)
            ],
            'short_description' => 'nullable|string|max:500',
            'description' => 'required|string',
            'category_id' => 'required|exists:service_categories,id',
            'icon' => 'nullable|string|max:255',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:2048',
            'process_steps' => 'nullable|array',
            'process_steps.*.title' => 'required_with:process_steps|string|max:255',
            'process_steps.*.description' => 'required_with:process_steps|string|max:1000',
            'process_steps.*.icon' => 'nullable|string|max:255',
            'faq' => 'nullable|array',
            'faq.*.question' => 'required_with:faq|string|max:500',
            'faq.*.answer' => 'required_with:faq|string|max:2000',
            'is_featured' => 'boolean',
            'is_active' => 'boolean',
            'sort_order' => 'nullable|integer|min:0|max:999',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'meta_keywords' => 'nullable|string|max:255',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'title' => 'service title',
            'short_description' => 'short description',
            'description' => 'service description',
            'category_id' => 'service category',
            'process_steps.*.title' => 'process step title',
            'process_steps.*.description' => 'process step description',
            'faq.*.question' => 'FAQ question',
            'faq.*.answer' => 'FAQ answer',
            'is_featured' => 'featured status',
            'is_active' => 'active status',
            'sort_order' => 'sort order',
            'meta_title' => 'meta title',
            'meta_description' => 'meta description',
            'meta_keywords' => 'meta keywords',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'title.required' => 'The service title is required.',
            'title.max' => 'The service title may not be greater than 255 characters.',
            'slug.regex' => 'The slug format is invalid. Use lowercase letters, numbers, and hyphens only.',
            'slug.unique' => 'This slug is already taken. Please choose a different one.',
            'description.required' => 'The service description is required.',
            'category_id.required' => 'Please select a service category.',
            'category_id.exists' => 'The selected service category is invalid.',
            'image.image' => 'The file must be an image.',
            'image.mimes' => 'The image must be a file of type: jpeg, png, jpg, gif, webp.',
            'image.max' => 'The image may not be greater than 2MB.',
            'process_steps.*.title.required_with' => 'Process step title is required when adding process steps.',
            'process_steps.*.description.required_with' => 'Process step description is required when adding process steps.',
            'faq.*.question.required_with' => 'FAQ question is required when adding FAQs.',
            'faq.*.answer.required_with' => 'FAQ answer is required when adding FAQs.',
            'sort_order.integer' => 'Sort order must be a number.',
            'sort_order.min' => 'Sort order must be at least 0.',
            'sort_order.max' => 'Sort order may not be greater than 999.',
            'meta_title.max' => 'Meta title may not be greater than 255 characters.',
            'meta_description.max' => 'Meta description may not be greater than 500 characters.',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Convert checkboxes to boolean
        $this->merge([
            'is_featured' => $this->boolean('is_featured'),
            'is_active' => $this->boolean('is_active', true), // Default to true
        ]);

        // Generate slug if not provided
        if (empty($this->slug) && !empty($this->title)) {
            $this->merge([
                'slug' => \Illuminate\Support\Str::slug($this->title)
            ]);
        }

        // Set default sort order
        if (is_null($this->sort_order)) {
            $this->merge([
                'sort_order' => 0
            ]);
        }
    }
}
