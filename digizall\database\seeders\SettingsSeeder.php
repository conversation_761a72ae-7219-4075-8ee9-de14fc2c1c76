<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Setting;

class SettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $settings = [
            // General Settings
            [
                'key' => 'site_name',
                'value' => 'DIGIZALL',
                'type' => 'text',
                'group' => 'general',
                'description' => 'Website name',
                'is_public' => true,
            ],
            [
                'key' => 'site_tagline',
                'value' => 'Digital Solutions for Modern Business',
                'type' => 'text',
                'group' => 'general',
                'description' => 'Website tagline',
                'is_public' => true,
            ],
            [
                'key' => 'site_description',
                'value' => 'DIGIZALL provides comprehensive digital solutions including web development, digital marketing, and technology consulting services.',
                'type' => 'textarea',
                'group' => 'general',
                'description' => 'Website description',
                'is_public' => true,
            ],
            [
                'key' => 'admin_email',
                'value' => '<EMAIL>',
                'type' => 'email',
                'group' => 'general',
                'description' => 'Administrator email address',
                'is_public' => false,
            ],
            [
                'key' => 'contact_email',
                'value' => '<EMAIL>',
                'type' => 'email',
                'group' => 'contact',
                'description' => 'Contact email address',
                'is_public' => true,
            ],
            [
                'key' => 'contact_phone',
                'value' => '+****************',
                'type' => 'text',
                'group' => 'contact',
                'description' => 'Contact phone number',
                'is_public' => true,
            ],
            [
                'key' => 'contact_address',
                'value' => '123 Business Street, Suite 100, City, State 12345',
                'type' => 'textarea',
                'group' => 'contact',
                'description' => 'Business address',
                'is_public' => true,
            ],

            // Social Media Settings
            [
                'key' => 'facebook_url',
                'value' => 'https://facebook.com/digizall',
                'type' => 'url',
                'group' => 'social',
                'description' => 'Facebook page URL',
                'is_public' => true,
            ],
            [
                'key' => 'twitter_url',
                'value' => 'https://twitter.com/digizall',
                'type' => 'url',
                'group' => 'social',
                'description' => 'Twitter profile URL',
                'is_public' => true,
            ],
            [
                'key' => 'linkedin_url',
                'value' => 'https://linkedin.com/company/digizall',
                'type' => 'url',
                'group' => 'social',
                'description' => 'LinkedIn company URL',
                'is_public' => true,
            ],
            [
                'key' => 'instagram_url',
                'value' => 'https://instagram.com/digizall',
                'type' => 'url',
                'group' => 'social',
                'description' => 'Instagram profile URL',
                'is_public' => true,
            ],

            // SEO Settings
            [
                'key' => 'default_meta_title',
                'value' => 'DIGIZALL - Digital Solutions for Modern Business',
                'type' => 'text',
                'group' => 'seo',
                'description' => 'Default meta title',
                'is_public' => true,
            ],
            [
                'key' => 'default_meta_description',
                'value' => 'Professional web development, digital marketing, and technology consulting services. Transform your business with our comprehensive digital solutions.',
                'type' => 'textarea',
                'group' => 'seo',
                'description' => 'Default meta description',
                'is_public' => true,
            ],
            [
                'key' => 'default_meta_keywords',
                'value' => 'web development, digital marketing, SEO, technology consulting, business solutions',
                'type' => 'text',
                'group' => 'seo',
                'description' => 'Default meta keywords',
                'is_public' => true,
            ],

            // Business Settings
            [
                'key' => 'business_hours',
                'value' => 'Monday - Friday: 9:00 AM - 6:00 PM',
                'type' => 'text',
                'group' => 'business',
                'description' => 'Business operating hours',
                'is_public' => true,
            ],
            [
                'key' => 'timezone',
                'value' => 'America/New_York',
                'type' => 'text',
                'group' => 'business',
                'description' => 'Business timezone',
                'is_public' => false,
            ],
            [
                'key' => 'currency',
                'value' => 'USD',
                'type' => 'text',
                'group' => 'business',
                'description' => 'Default currency',
                'is_public' => false,
            ],

            // Email Settings
            [
                'key' => 'smtp_host',
                'value' => 'smtp.gmail.com',
                'type' => 'text',
                'group' => 'email',
                'description' => 'SMTP host',
                'is_public' => false,
            ],
            [
                'key' => 'smtp_port',
                'value' => '587',
                'type' => 'number',
                'group' => 'email',
                'description' => 'SMTP port',
                'is_public' => false,
            ],
            [
                'key' => 'email_notifications',
                'value' => '1',
                'type' => 'boolean',
                'group' => 'email',
                'description' => 'Enable email notifications',
                'is_public' => false,
            ],

            // Analytics Settings
            [
                'key' => 'google_analytics_id',
                'value' => '',
                'type' => 'text',
                'group' => 'analytics',
                'description' => 'Google Analytics tracking ID',
                'is_public' => false,
            ],
            [
                'key' => 'facebook_pixel_id',
                'value' => '',
                'type' => 'text',
                'group' => 'analytics',
                'description' => 'Facebook Pixel ID',
                'is_public' => false,
            ],

            // Maintenance Settings
            [
                'key' => 'maintenance_mode',
                'value' => '0',
                'type' => 'boolean',
                'group' => 'maintenance',
                'description' => 'Enable maintenance mode',
                'is_public' => false,
            ],
            [
                'key' => 'maintenance_message',
                'value' => 'We are currently performing scheduled maintenance. Please check back soon.',
                'type' => 'textarea',
                'group' => 'maintenance',
                'description' => 'Maintenance mode message',
                'is_public' => false,
            ],
        ];

        foreach ($settings as $setting) {
            Setting::updateOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }

        $this->command->info('Settings seeded successfully!');
    }
}
