<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\ServiceCategory;

class ServiceCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name' => 'Web Development',
                'slug' => 'web-development',
                'description' => 'Custom website development, web applications, and e-commerce solutions',
                'icon' => 'fas fa-code',
                'color' => '#3B82F6',
                'is_active' => true,
                'sort_order' => 1,
            ],
            [
                'name' => 'Digital Marketing',
                'slug' => 'digital-marketing',
                'description' => 'SEO, social media marketing, PPC campaigns, and content marketing',
                'icon' => 'fas fa-bullhorn',
                'color' => '#10B981',
                'is_active' => true,
                'sort_order' => 2,
            ],
            [
                'name' => 'Mobile App Development',
                'slug' => 'mobile-app-development',
                'description' => 'iOS and Android mobile application development',
                'icon' => 'fas fa-mobile-alt',
                'color' => '#8B5CF6',
                'is_active' => true,
                'sort_order' => 3,
            ],
            [
                'name' => 'UI/UX Design',
                'slug' => 'ui-ux-design',
                'description' => 'User interface and user experience design services',
                'icon' => 'fas fa-paint-brush',
                'color' => '#F59E0B',
                'is_active' => true,
                'sort_order' => 4,
            ],
            [
                'name' => 'Cloud Services',
                'slug' => 'cloud-services',
                'description' => 'Cloud hosting, migration, and infrastructure management',
                'icon' => 'fas fa-cloud',
                'color' => '#06B6D4',
                'is_active' => true,
                'sort_order' => 5,
            ],
            [
                'name' => 'Consulting',
                'slug' => 'consulting',
                'description' => 'Technology consulting and digital transformation services',
                'icon' => 'fas fa-lightbulb',
                'color' => '#EF4444',
                'is_active' => true,
                'sort_order' => 6,
            ],
        ];

        foreach ($categories as $category) {
            ServiceCategory::updateOrCreate(
                ['slug' => $category['slug']],
                $category
            );
        }

        $this->command->info('Service categories seeded successfully!');
    }
}
