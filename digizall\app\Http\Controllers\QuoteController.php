<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Requests\QuoteRequest;
use App\Models\Service;
use App\Models\MetaTag;
use App\Models\QuoteRequest as Quote;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;

class QuoteController extends Controller
{
    /**
     * Show the quote request form.
     */
    public function index(Request $request)
    {
        // Get services for the form
        $services = Service::active()->orderBy('title')->get();

        // Get pre-selected service if provided
        $selectedService = $request->get('service');
        $selectedPlan = $request->get('plan');

        // Get meta tags
        $metaTags = MetaTag::getForPage('quote');

        return view('quote.index', compact('services', 'selectedService', 'selectedPlan', 'metaTags'));
    }

    /**
     * Handle quote request submission.
     */
    public function store(QuoteRequest $request)
    {
        try {
            // Create quote record
            $quote = Quote::create([
                'name' => $request->name,
                'email' => $request->email,
                'phone' => $request->phone,
                'company' => $request->company,
                'website' => $request->website,
                'service' => $request->service,
                'budget' => $request->budget,
                'timeline' => $request->timeline,
                'project_type' => $request->project_type,
                'description' => $request->description,
                'features' => $request->features ? json_encode($request->features) : null,
                'additional_info' => $request->additional_info,
                'preferred_contact' => $request->preferred_contact,
                'marketing_consent' => $request->boolean('marketing_consent'),
                'status' => 'pending',
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ]);

            // Send notification emails
            $this->sendQuoteNotifications($quote);

            return redirect()->route('quote.success')
                ->with('success', 'Your quote request has been submitted successfully! We\'ll get back to you within 24 hours.');

        } catch (\Exception $e) {
            Log::error('Quote submission failed: ' . $e->getMessage());

            return back()
                ->withInput()
                ->with('error', 'There was an error submitting your quote request. Please try again or contact us directly.');
        }
    }

    /**
     * Show quote success page.
     */
    public function success()
    {
        return view('quote.success');
    }

    /**
     * Send quote notification emails.
     */
    private function sendQuoteNotifications(Quote $quote)
    {
        try {
            // Send confirmation email to client
            Mail::send('emails.quote.client-confirmation', compact('quote'), function ($message) use ($quote) {
                $message->to($quote->email, $quote->name)
                    ->subject('Quote Request Confirmation - DIGIZALL')
                    ->from(config('mail.from.address'), config('mail.from.name'));
            });

            // Send notification email to admin
            Mail::send('emails.quote.admin-notification', compact('quote'), function ($message) use ($quote) {
                $message->to(config('mail.admin_email', '<EMAIL>'))
                    ->subject('New Quote Request - ' . $quote->service)
                    ->from(config('mail.from.address'), config('mail.from.name'));
            });

        } catch (\Exception $e) {
            Log::error('Failed to send quote emails: ' . $e->getMessage());
        }
    }
}
