@extends('layouts.admin')

@section('title', 'View Blog Post')
@section('page-title', 'View Blog Post')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-semibold text-gray-900">{{ $post->title }}</h1>
            <p class="text-gray-600">{{ $post->excerpt }}</p>
        </div>
        <div class="flex space-x-2">
            <a href="{{ route('blog.show', $post->slug) }}" target="_blank"
               class="inline-flex items-center px-4 py-2 bg-green-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-green-700">
                <i class="fas fa-external-link-alt mr-2"></i>
                View Live
            </a>
            <a href="{{ route('admin.blog.edit', $post) }}" 
               class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700">
                <i class="fas fa-edit mr-2"></i>
                Edit Post
            </a>
            <a href="{{ route('admin.blog.index') }}" 
               class="inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Posts
            </a>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Post Content -->
            <div class="bg-white rounded-lg shadow overflow-hidden">
                @if($post->featured_image)
                    <div class="aspect-w-16 aspect-h-9">
                        <img src="{{ asset('storage/' . $post->featured_image) }}" 
                             alt="{{ $post->title }}" 
                             class="w-full h-64 object-cover">
                    </div>
                @endif
                
                <div class="p-6">
                    <div class="prose max-w-none">
                        {!! nl2br(e($post->content)) !!}
                    </div>
                </div>
            </div>

            <!-- SEO Preview -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">SEO Preview</h3>
                
                <div class="border rounded-lg p-4 bg-gray-50">
                    <div class="text-blue-600 text-lg hover:underline cursor-pointer">
                        {{ $post->meta_title ?: $post->title }}
                    </div>
                    <div class="text-green-700 text-sm">
                        {{ url('/blog/' . $post->slug) }}
                    </div>
                    <div class="text-gray-600 text-sm mt-1">
                        {{ $post->meta_description ?: $post->excerpt }}
                    </div>
                </div>
            </div>

            <!-- Comments Section (if implemented) -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Comments</h3>
                <p class="text-gray-500">Comments feature not implemented yet.</p>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Post Information -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Post Information</h3>
                
                <div class="space-y-3">
                    <div>
                        <span class="text-sm font-medium text-gray-500">Status:</span>
                        <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                            {{ $post->status === 'published' ? 'bg-green-100 text-green-800' : 
                               ($post->status === 'draft' ? 'bg-gray-100 text-gray-800' : 'bg-blue-100 text-blue-800') }}">
                            {{ ucfirst($post->status) }}
                        </span>
                    </div>
                    
                    <div>
                        <span class="text-sm font-medium text-gray-500">Author:</span>
                        <span class="ml-2 text-sm text-gray-900">{{ $post->author->name ?? 'Unknown' }}</span>
                    </div>
                    
                    <div>
                        <span class="text-sm font-medium text-gray-500">Category:</span>
                        <span class="ml-2 text-sm text-gray-900">{{ $post->category->name ?? 'Uncategorized' }}</span>
                    </div>
                    
                    <div>
                        <span class="text-sm font-medium text-gray-500">Created:</span>
                        <span class="ml-2 text-sm text-gray-900">{{ $post->created_at->format('M j, Y g:i A') }}</span>
                    </div>
                    
                    <div>
                        <span class="text-sm font-medium text-gray-500">Updated:</span>
                        <span class="ml-2 text-sm text-gray-900">{{ $post->updated_at->format('M j, Y g:i A') }}</span>
                    </div>
                    
                    @if($post->published_at)
                        <div>
                            <span class="text-sm font-medium text-gray-500">Published:</span>
                            <span class="ml-2 text-sm text-gray-900">{{ $post->published_at->format('M j, Y g:i A') }}</span>
                        </div>
                    @endif
                    
                    <div>
                        <span class="text-sm font-medium text-gray-500">Reading Time:</span>
                        <span class="ml-2 text-sm text-gray-900">{{ $post->reading_time }} minutes</span>
                    </div>
                    
                    <div>
                        <span class="text-sm font-medium text-gray-500">Views:</span>
                        <span class="ml-2 text-sm text-gray-900">{{ number_format($post->views_count) }}</span>
                    </div>
                    
                    @if($post->is_featured)
                        <div>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                <i class="fas fa-star mr-1"></i>
                                Featured Post
                            </span>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Tags -->
            @if($post->tags->count() > 0)
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Tags</h3>
                    
                    <div class="flex flex-wrap gap-2">
                        @foreach($post->tags as $tag)
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                {{ $tag->name }}
                            </span>
                        @endforeach
                    </div>
                </div>
            @endif

            <!-- Quick Actions -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
                
                <div class="space-y-3">
                    @if($post->status === 'draft')
                        <form method="POST" action="{{ route('admin.blog.toggle-status', $post) }}" class="w-full">
                            @csrf
                            <button type="submit" 
                                    class="w-full inline-flex justify-center items-center px-4 py-2 bg-green-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-green-700">
                                <i class="fas fa-eye mr-2"></i>
                                Publish Post
                            </button>
                        </form>
                    @elseif($post->status === 'published')
                        <form method="POST" action="{{ route('admin.blog.toggle-status', $post) }}" class="w-full">
                            @csrf
                            <button type="submit" 
                                    class="w-full inline-flex justify-center items-center px-4 py-2 bg-yellow-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-yellow-700">
                                <i class="fas fa-eye-slash mr-2"></i>
                                Unpublish Post
                            </button>
                        </form>
                    @endif
                    
                    <form method="POST" action="{{ route('admin.blog.toggle-featured', $post) }}" class="w-full">
                        @csrf
                        <button type="submit" 
                                class="w-full inline-flex justify-center items-center px-4 py-2 bg-purple-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-purple-700">
                            <i class="fas fa-star mr-2"></i>
                            {{ $post->is_featured ? 'Remove from Featured' : 'Mark as Featured' }}
                        </button>
                    </form>
                    
                    <form method="POST" action="{{ route('admin.blog.destroy', $post) }}" 
                          class="w-full" 
                          onsubmit="return confirm('Are you sure you want to delete this post? This action cannot be undone.')">
                        @csrf
                        @method('DELETE')
                        <button type="submit" 
                                class="w-full inline-flex justify-center items-center px-4 py-2 bg-red-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-red-700">
                            <i class="fas fa-trash mr-2"></i>
                            Delete Post
                        </button>
                    </form>
                </div>
            </div>

            <!-- Social Sharing -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Share Post</h3>
                
                <div class="space-y-2">
                    <a href="https://twitter.com/intent/tweet?url={{ urlencode(route('blog.show', $post->slug)) }}&text={{ urlencode($post->title) }}" 
                       target="_blank"
                       class="w-full inline-flex justify-center items-center px-4 py-2 bg-blue-400 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-500">
                        <i class="fab fa-twitter mr-2"></i>
                        Share on Twitter
                    </a>
                    
                    <a href="https://www.facebook.com/sharer/sharer.php?u={{ urlencode(route('blog.show', $post->slug)) }}" 
                       target="_blank"
                       class="w-full inline-flex justify-center items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700">
                        <i class="fab fa-facebook mr-2"></i>
                        Share on Facebook
                    </a>
                    
                    <a href="https://www.linkedin.com/sharing/share-offsite/?url={{ urlencode(route('blog.show', $post->slug)) }}" 
                       target="_blank"
                       class="w-full inline-flex justify-center items-center px-4 py-2 bg-blue-700 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-800">
                        <i class="fab fa-linkedin mr-2"></i>
                        Share on LinkedIn
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
