<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class AttendanceLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'date',
        'time_in',
        'time_out',
        'break_minutes',
        'total_hours',
        'status',
        'notes',
        'ip_address'
    ];

    protected $casts = [
        'date' => 'date',
        'time_in' => 'datetime',
        'time_out' => 'datetime'
    ];

    /**
     * Get the user that owns the attendance log.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope for today's attendance.
     */
    public function scopeToday($query)
    {
        return $query->whereDate('date', today());
    }

    /**
     * Scope for this week's attendance.
     */
    public function scopeThisWeek($query)
    {
        return $query->whereBetween('date', [
            now()->startOfWeek(),
            now()->endOfWeek()
        ]);
    }

    /**
     * Scope for this month's attendance.
     */
    public function scopeThisMonth($query)
    {
        return $query->whereMonth('date', now()->month)
                    ->whereYear('date', now()->year);
    }

    /**
     * Scope for present status.
     */
    public function scopePresent($query)
    {
        return $query->where('status', 'present');
    }

    /**
     * Get status badge color.
     */
    public function getStatusBadgeColorAttribute(): string
    {
        return match($this->status) {
            'present' => 'bg-green-100 text-green-800',
            'absent' => 'bg-red-100 text-red-800',
            'late' => 'bg-yellow-100 text-yellow-800',
            'half_day' => 'bg-blue-100 text-blue-800',
            'on_leave' => 'bg-purple-100 text-purple-800',
            default => 'bg-gray-100 text-gray-800'
        };
    }

    /**
     * Get formatted status.
     */
    public function getFormattedStatusAttribute(): string
    {
        return ucwords(str_replace('_', ' ', $this->status));
    }

    /**
     * Get formatted time in.
     */
    public function getFormattedTimeInAttribute(): string
    {
        return $this->time_in ? $this->time_in->format('H:i') : '-';
    }

    /**
     * Get formatted time out.
     */
    public function getFormattedTimeOutAttribute(): string
    {
        return $this->time_out ? $this->time_out->format('H:i') : '-';
    }

    /**
     * Get total hours formatted.
     */
    public function getTotalHoursFormattedAttribute(): string
    {
        if (!$this->total_hours) return '-';

        $hours = floor($this->total_hours / 60);
        $minutes = $this->total_hours % 60;

        return sprintf('%02d:%02d', $hours, $minutes);
    }

    /**
     * Calculate total hours worked.
     */
    public function calculateTotalHours(): void
    {
        if ($this->time_in && $this->time_out) {
            $totalMinutes = $this->time_out->diffInMinutes($this->time_in);
            $this->total_hours = max(0, $totalMinutes - $this->break_minutes);
            $this->save();
        }
    }

    /**
     * Clock in.
     */
    public function clockIn(): void
    {
        $this->update([
            'time_in' => now(),
            'status' => 'present'
        ]);
    }

    /**
     * Clock out.
     */
    public function clockOut(): void
    {
        $this->update(['time_out' => now()]);
        $this->calculateTotalHours();
    }

    /**
     * Check if user is currently clocked in.
     */
    public function isClockedIn(): bool
    {
        return !is_null($this->time_in) && is_null($this->time_out);
    }

    /**
     * Get today's attendance for user.
     */
    public static function getTodayAttendance($userId)
    {
        return self::where('user_id', $userId)
                  ->whereDate('date', today())
                  ->first();
    }
}
