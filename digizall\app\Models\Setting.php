<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;

class Setting extends Model
{
    use HasFactory;

    protected $fillable = [
        'key',
        'value',
        'type',
        'group',
        'description',
        'is_public'
    ];

    protected $casts = [
        'is_public' => 'boolean'
    ];

    /**
     * Scope for public settings.
     */
    public function scopePublic($query)
    {
        return $query->where('is_public', true);
    }

    /**
     * Scope for settings by group.
     */
    public function scopeByGroup($query, $group)
    {
        return $query->where('group', $group);
    }

    /**
     * Get a setting value by key.
     */
    public static function get($key, $default = null)
    {
        $cacheKey = 'setting_' . $key;

        return Cache::remember($cacheKey, 3600, function () use ($key, $default) {
            $setting = self::where('key', $key)->first();

            if (!$setting) {
                return $default;
            }

            return self::castValue($setting->value, $setting->type);
        });
    }

    /**
     * Set a setting value.
     */
    public static function set($key, $value, $type = 'text', $group = 'general')
    {
        $setting = self::updateOrCreate(
            ['key' => $key],
            [
                'value' => $value,
                'type' => $type,
                'group' => $group
            ]
        );

        // Clear cache
        Cache::forget('setting_' . $key);
        Cache::forget('settings_' . $group);

        return $setting;
    }

    /**
     * Get all settings by group.
     */
    public static function getByGroup($group)
    {
        $cacheKey = 'settings_' . $group;

        return Cache::remember($cacheKey, 3600, function () use ($group) {
            return self::where('group', $group)
                      ->pluck('value', 'key')
                      ->map(function ($value, $key) {
                          $setting = self::where('key', $key)->first();
                          return self::castValue($value, $setting->type ?? 'text');
                      });
        });
    }

    /**
     * Cast value based on type.
     */
    protected static function castValue($value, $type)
    {
        return match($type) {
            'boolean' => (bool) $value,
            'number' => is_numeric($value) ? (float) $value : $value,
            'json' => json_decode($value, true),
            default => $value
        };
    }

    /**
     * Get formatted value for display.
     */
    public function getFormattedValueAttribute()
    {
        return self::castValue($this->value, $this->type);
    }

    /**
     * Clear all settings cache.
     */
    public static function clearCache()
    {
        $groups = self::distinct('group')->pluck('group');

        foreach ($groups as $group) {
            Cache::forget('settings_' . $group);
        }

        $keys = self::pluck('key');
        foreach ($keys as $key) {
            Cache::forget('setting_' . $key);
        }
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::saved(function ($setting) {
            Cache::forget('setting_' . $setting->key);
            Cache::forget('settings_' . $setting->group);
        });

        static::deleted(function ($setting) {
            Cache::forget('setting_' . $setting->key);
            Cache::forget('settings_' . $setting->group);
        });
    }
}
