<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use App\Models\User;
use App\Models\Employee;
use App\Models\Department;
use App\Models\Attendance;

class AttendanceTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Create a department
        $this->department = Department::factory()->create();

        // Create a user and employee
        $this->user = User::factory()->create();
        $this->employee = Employee::factory()->create([
            'user_id' => $this->user->id,
            'department_id' => $this->department->id,
        ]);
    }

    public function test_attendance_dashboard_requires_authentication()
    {
        $response = $this->get('/app');
        $response->assertRedirect('/login');
    }

    public function test_employee_can_access_attendance_dashboard()
    {
        $response = $this->actingAs($this->user)->get('/app');
        $response->assertStatus(200);
    }

    public function test_employee_can_check_in()
    {
        $response = $this->actingAs($this->user)
                         ->postJson('/app/check-in');

        $response->assertStatus(200);
        $response->assertJson(['success' => true]);

        $this->assertDatabaseHas('attendances', [
            'employee_id' => $this->employee->id,
            'date' => today(),
        ]);
    }

    public function test_employee_cannot_check_in_twice()
    {
        // First check-in
        $this->actingAs($this->user)->postJson('/app/check-in');

        // Second check-in attempt
        $response = $this->actingAs($this->user)
                         ->postJson('/app/check-in');

        $response->assertStatus(400);
        $response->assertJson(['error' => 'Already checked in today']);
    }

    public function test_employee_can_check_out()
    {
        // First check in
        $this->actingAs($this->user)->postJson('/app/check-in');

        // Then check out
        $response = $this->actingAs($this->user)
                         ->postJson('/app/check-out');

        $response->assertStatus(200);
        $response->assertJson(['success' => true]);

        $attendance = Attendance::where('employee_id', $this->employee->id)
                               ->whereDate('date', today())
                               ->first();

        $this->assertNotNull($attendance->check_out);
    }

    public function test_employee_cannot_check_out_without_check_in()
    {
        $response = $this->actingAs($this->user)
                         ->postJson('/app/check-out');

        $response->assertStatus(400);
        $response->assertJson(['error' => 'No check-in record found for today']);
    }

    public function test_employee_can_start_break()
    {
        // First check in
        $this->actingAs($this->user)->postJson('/app/check-in');

        // Start break
        $response = $this->actingAs($this->user)
                         ->postJson('/app/start-break');

        $response->assertStatus(200);
        $response->assertJson(['success' => true]);

        $attendance = Attendance::where('employee_id', $this->employee->id)
                               ->whereDate('date', today())
                               ->first();

        $this->assertNotNull($attendance->break_start);
    }

    public function test_employee_can_end_break()
    {
        // Check in and start break
        $this->actingAs($this->user)->postJson('/app/check-in');
        $this->actingAs($this->user)->postJson('/app/start-break');

        // End break
        $response = $this->actingAs($this->user)
                         ->postJson('/app/end-break');

        $response->assertStatus(200);
        $response->assertJson(['success' => true]);

        $attendance = Attendance::where('employee_id', $this->employee->id)
                               ->whereDate('date', today())
                               ->first();

        $this->assertNotNull($attendance->break_end);
    }

    public function test_attendance_calculates_total_hours()
    {
        $attendance = Attendance::create([
            'employee_id' => $this->employee->id,
            'date' => today(),
            'check_in' => '09:00:00',
            'check_out' => '17:00:00',
            'break_start' => '12:00:00',
            'break_end' => '13:00:00',
        ]);

        // Should be 8 hours minus 1 hour break = 7 hours = 420 minutes
        $this->assertEquals(420, $attendance->total_hours);
    }

    public function test_employee_status_api_returns_correct_data()
    {
        $response = $this->actingAs($this->user)
                         ->getJson('/app/status');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'employee' => [
                'name',
                'employee_id',
                'department',
            ],
            'attendance'
        ]);
    }

    public function test_non_employee_user_sees_admin_dashboard()
    {
        $adminUser = User::factory()->create(['is_admin' => true]);

        $response = $this->actingAs($adminUser)->get('/app');
        $response->assertStatus(200);
        $response->assertSee('Department-wise');
    }
}
