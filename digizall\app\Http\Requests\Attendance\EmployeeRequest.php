<?php

namespace App\Http\Requests\Attendance;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class EmployeeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check() && (auth()->user()->isHR() || auth()->user()->isAdmin());
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $employeeId = $this->route('employee') ? $this->route('employee')->id : null;

        return [
            'employee_id' => [
                'required',
                'string',
                'max:20',
                Rule::unique('employees', 'employee_id')->ignore($employeeId)
            ],
            'user_id' => [
                'required',
                'exists:users,id',
                Rule::unique('employees', 'user_id')->ignore($employeeId)
            ],
            'department_id' => 'required|exists:departments,id',
            'position' => 'required|string|max:100',
            'hire_date' => 'required|date|before_or_equal:today',
            'salary' => 'nullable|numeric|min:0|max:999999.99',
            'employment_type' => 'required|in:full-time,part-time,contract,intern',
            'work_schedule' => 'required|in:standard,flexible,remote,hybrid',
            'manager_id' => 'nullable|exists:employees,id',
            'phone' => 'nullable|string|max:20',
            'emergency_contact_name' => 'nullable|string|max:100',
            'emergency_contact_phone' => 'nullable|string|max:20',
            'emergency_contact_relationship' => 'nullable|string|max:50',
            'address' => 'nullable|string|max:255',
            'city' => 'nullable|string|max:100',
            'state' => 'nullable|string|max:100',
            'postal_code' => 'nullable|string|max:20',
            'country' => 'nullable|string|max:100',
            'is_active' => 'boolean',
            'notes' => 'nullable|string|max:1000',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'employee_id' => 'employee ID',
            'user_id' => 'user account',
            'department_id' => 'department',
            'hire_date' => 'hire date',
            'employment_type' => 'employment type',
            'work_schedule' => 'work schedule',
            'manager_id' => 'manager',
            'emergency_contact_name' => 'emergency contact name',
            'emergency_contact_phone' => 'emergency contact phone',
            'emergency_contact_relationship' => 'emergency contact relationship',
            'postal_code' => 'postal code',
            'is_active' => 'active status',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'employee_id.required' => 'Employee ID is required.',
            'employee_id.unique' => 'This employee ID is already taken.',
            'user_id.required' => 'Please select a user account.',
            'user_id.exists' => 'The selected user account is invalid.',
            'user_id.unique' => 'This user already has an employee record.',
            'department_id.required' => 'Please select a department.',
            'department_id.exists' => 'The selected department is invalid.',
            'position.required' => 'Position is required.',
            'hire_date.required' => 'Hire date is required.',
            'hire_date.before_or_equal' => 'Hire date cannot be in the future.',
            'salary.numeric' => 'Salary must be a valid number.',
            'salary.min' => 'Salary cannot be negative.',
            'salary.max' => 'Salary cannot exceed 999,999.99.',
            'employment_type.required' => 'Please select an employment type.',
            'employment_type.in' => 'The selected employment type is invalid.',
            'work_schedule.required' => 'Please select a work schedule.',
            'work_schedule.in' => 'The selected work schedule is invalid.',
            'manager_id.exists' => 'The selected manager is invalid.',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Convert checkboxes to boolean
        $this->merge([
            'is_active' => $this->boolean('is_active', true), // Default to true
        ]);

        // Format employee ID
        if (!empty($this->employee_id)) {
            $this->merge([
                'employee_id' => strtoupper(trim($this->employee_id))
            ]);
        }

        // Format phone numbers
        if (!empty($this->phone)) {
            $this->merge([
                'phone' => preg_replace('/[^0-9+\-\s\(\)]/', '', $this->phone)
            ]);
        }

        if (!empty($this->emergency_contact_phone)) {
            $this->merge([
                'emergency_contact_phone' => preg_replace('/[^0-9+\-\s\(\)]/', '', $this->emergency_contact_phone)
            ]);
        }
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Validate that manager is not the same as employee
            if ($this->manager_id && $this->route('employee') && $this->manager_id == $this->route('employee')->id) {
                $validator->errors()->add('manager_id', 'An employee cannot be their own manager.');
            }

            // Validate hire date for interns (should be recent)
            if ($this->employment_type === 'intern' && $this->hire_date) {
                $hireDate = \Carbon\Carbon::parse($this->hire_date);
                if ($hireDate->diffInMonths(now()) > 12) {
                    $validator->warnings()->add('hire_date', 'Intern hire date is more than 12 months ago. Please verify this is correct.');
                }
            }

            // Validate salary for different employment types
            if ($this->employment_type === 'intern' && $this->salary > 50000) {
                $validator->warnings()->add('salary', 'Salary seems high for an intern position. Please verify.');
            }

            // Validate emergency contact information
            if (!empty($this->emergency_contact_name) && empty($this->emergency_contact_phone)) {
                $validator->warnings()->add('emergency_contact_phone', 'Emergency contact phone is recommended when emergency contact name is provided.');
            }
        });
    }
}
