@extends('layouts.admin')

@section('title', 'Performance Dashboard')
@section('page-title', 'Performance Dashboard')

@section('content')
<div class="space-y-6">
    <!-- Performance Overview -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Response Time -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                        <i class="fas fa-tachometer-alt text-white text-sm"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Avg Response Time</p>
                    <p class="text-2xl font-semibold text-gray-900">
                        {{ $report['summary']['avg_duration_ms'] ?? 'N/A' }}ms
                    </p>
                </div>
            </div>
        </div>

        <!-- Memory Usage -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                        <i class="fas fa-memory text-white text-sm"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Avg Memory Usage</p>
                    <p class="text-2xl font-semibold text-gray-900">
                        {{ $report['summary']['avg_memory_mb'] ?? 'N/A' }}MB
                    </p>
                </div>
            </div>
        </div>

        <!-- Cache Hit Rate -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                        <i class="fas fa-database text-white text-sm"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Cache Hit Rate</p>
                    <p class="text-2xl font-semibold text-gray-900">
                        {{ $report['summary']['cache_hit_rate'] ?? 'N/A' }}%
                    </p>
                </div>
            </div>
        </div>

        <!-- Database Health -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-{{ $databaseHealth['grade'] === 'A' ? 'green' : ($databaseHealth['grade'] === 'B' ? 'yellow' : 'red') }}-500 rounded-md flex items-center justify-center">
                        <i class="fas fa-server text-white text-sm"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Database Health</p>
                    <p class="text-2xl font-semibold text-gray-900">
                        {{ $databaseHealth['score'] }}/100 ({{ $databaseHealth['grade'] }})
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="bg-white rounded-lg shadow p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Quick Optimizations</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <button onclick="runOptimization('cache')" 
                    class="inline-flex items-center justify-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700">
                <i class="fas fa-sync mr-2"></i>
                Optimize Cache
            </button>
            <button onclick="runOptimization('database')" 
                    class="inline-flex items-center justify-center px-4 py-2 bg-green-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-green-700">
                <i class="fas fa-database mr-2"></i>
                Optimize Database
            </button>
            <button onclick="runOptimization('images')" 
                    class="inline-flex items-center justify-center px-4 py-2 bg-purple-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-purple-700">
                <i class="fas fa-images mr-2"></i>
                Optimize Images
            </button>
            <button onclick="runOptimization('all')" 
                    class="inline-flex items-center justify-center px-4 py-2 bg-red-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-red-700">
                <i class="fas fa-rocket mr-2"></i>
                Full Optimization
            </button>
        </div>
    </div>

    <!-- Performance Trends -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Response Time Trend -->
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Response Time Trend (7 Days)</h3>
            <div class="h-64">
                <canvas id="responseTimeChart"></canvas>
            </div>
        </div>

        <!-- Memory Usage Trend -->
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Memory Usage Trend (7 Days)</h3>
            <div class="h-64">
                <canvas id="memoryUsageChart"></canvas>
            </div>
        </div>
    </div>

    <!-- System Resources -->
    <div class="bg-white rounded-lg shadow p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">System Resources</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <!-- Memory -->
            <div>
                <div class="flex justify-between items-center mb-2">
                    <span class="text-sm font-medium text-gray-700">Memory Usage</span>
                    <span class="text-sm text-gray-500">
                        {{ $report['system_resources']['memory_usage']['current_mb'] }}MB / 
                        {{ $report['system_resources']['memory_usage']['limit_mb'] }}MB
                    </span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                    @php
                        $memoryPercent = is_numeric($report['system_resources']['memory_usage']['limit_mb']) 
                            ? ($report['system_resources']['memory_usage']['current_mb'] / $report['system_resources']['memory_usage']['limit_mb']) * 100 
                            : 0;
                    @endphp
                    <div class="bg-blue-600 h-2 rounded-full" style="width: {{ min($memoryPercent, 100) }}%"></div>
                </div>
            </div>

            <!-- Disk Usage -->
            <div>
                <div class="flex justify-between items-center mb-2">
                    <span class="text-sm font-medium text-gray-700">Disk Usage</span>
                    <span class="text-sm text-gray-500">
                        {{ $report['system_resources']['disk_usage']['used_percentage'] }}% used
                    </span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                    <div class="bg-green-600 h-2 rounded-full" style="width: {{ $report['system_resources']['disk_usage']['used_percentage'] }}%"></div>
                </div>
            </div>

            <!-- Cache Status -->
            <div>
                <div class="flex justify-between items-center mb-2">
                    <span class="text-sm font-medium text-gray-700">Cache Status</span>
                    <span class="text-sm text-gray-500">{{ $report['system_resources']['cache_stats']['driver'] }}</span>
                </div>
                <div class="flex items-center">
                    <div class="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                    <span class="text-sm text-gray-700">Active</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Slow Queries -->
    @if(!empty($report['slow_queries']))
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Recent Slow Queries</h3>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Duration</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Query</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Time</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @foreach(array_slice($report['slow_queries'], 0, 5) as $query)
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-red-600">
                                    {{ $query['duration_ms'] }}ms
                                </td>
                                <td class="px-6 py-4 text-sm text-gray-900">
                                    <code class="bg-gray-100 px-2 py-1 rounded text-xs">
                                        {{ \Str::limit($query['sql'], 80) }}
                                    </code>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ \Carbon\Carbon::parse($query['timestamp'])->diffForHumans() }}
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    @endif

    <!-- Database Health Issues -->
    @if(!empty($databaseHealth['issues']))
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
            <h3 class="text-lg font-medium text-yellow-800 mb-4">
                <i class="fas fa-exclamation-triangle mr-2"></i>
                Database Health Issues
            </h3>
            <ul class="list-disc list-inside space-y-2">
                @foreach($databaseHealth['issues'] as $issue)
                    <li class="text-yellow-700">{{ $issue }}</li>
                @endforeach
            </ul>
        </div>
    @endif
</div>

<!-- Optimization Modal -->
<div id="optimizationModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
            <div class="p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Running Optimization...</h3>
                <div class="flex items-center">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mr-3"></div>
                    <span id="optimizationStatus">Initializing...</span>
                </div>
                <div class="mt-4">
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div id="optimizationProgress" class="bg-blue-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Performance trends charts
document.addEventListener('DOMContentLoaded', function() {
    const trends = @json($report['trends'] ?? []);
    
    // Response Time Chart
    const responseTimeCtx = document.getElementById('responseTimeChart').getContext('2d');
    new Chart(responseTimeCtx, {
        type: 'line',
        data: {
            labels: trends.map(t => t.date),
            datasets: [{
                label: 'Avg Response Time (ms)',
                data: trends.map(t => t.avg_duration_ms),
                borderColor: 'rgb(59, 130, 246)',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Memory Usage Chart
    const memoryUsageCtx = document.getElementById('memoryUsageChart').getContext('2d');
    new Chart(memoryUsageCtx, {
        type: 'line',
        data: {
            labels: trends.map(t => t.date),
            datasets: [{
                label: 'Avg Memory Usage (MB)',
                data: trends.map(t => t.avg_memory_mb),
                borderColor: 'rgb(16, 185, 129)',
                backgroundColor: 'rgba(16, 185, 129, 0.1)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
});

// Optimization functions
function runOptimization(type) {
    const modal = document.getElementById('optimizationModal');
    const status = document.getElementById('optimizationStatus');
    const progress = document.getElementById('optimizationProgress');
    
    modal.classList.remove('hidden');
    status.textContent = `Running ${type} optimization...`;
    progress.style.width = '25%';
    
    fetch('{{ route("admin.performance.optimize") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({ type: type })
    })
    .then(response => response.json())
    .then(data => {
        progress.style.width = '100%';
        
        if (data.success) {
            status.textContent = 'Optimization completed successfully!';
            setTimeout(() => {
                modal.classList.add('hidden');
                location.reload();
            }, 2000);
        } else {
            status.textContent = 'Optimization failed: ' + data.message;
            setTimeout(() => {
                modal.classList.add('hidden');
            }, 3000);
        }
    })
    .catch(error => {
        progress.style.width = '100%';
        status.textContent = 'Optimization failed: ' + error.message;
        setTimeout(() => {
            modal.classList.add('hidden');
        }, 3000);
    });
}
</script>
@endpush
