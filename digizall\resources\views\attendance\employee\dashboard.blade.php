@extends('layouts.attendance')

@section('title', 'Employee Dashboard')

@section('content')
<div class="space-y-6">
    <!-- Welcome Header -->
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Welcome, {{ $employee->full_name }}!</h1>
                <p class="text-gray-600">{{ $employee->position }} • {{ $employee->department->name }}</p>
                <p class="text-sm text-gray-500">Employee ID: {{ $employee->employee_id }}</p>
            </div>
            <div class="text-right">
                <div class="text-sm text-gray-500">Today's Date</div>
                <div class="text-lg font-semibold text-gray-900">{{ now()->format('F d, Y') }}</div>
                <div class="text-sm text-gray-500">{{ now()->format('l') }}</div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <!-- Check In/Out -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="text-center">
                @if(!$todayAttendance || !$todayAttendance->check_in)
                    <button onclick="checkIn()" class="w-full bg-green-600 text-white py-3 px-4 rounded-lg hover:bg-green-700 transition-colors">
                        <i class="fas fa-sign-in-alt text-2xl mb-2"></i>
                        <div class="font-semibold">Check In</div>
                    </button>
                @elseif(!$todayAttendance->check_out)
                    <button onclick="checkOut()" class="w-full bg-red-600 text-white py-3 px-4 rounded-lg hover:bg-red-700 transition-colors">
                        <i class="fas fa-sign-out-alt text-2xl mb-2"></i>
                        <div class="font-semibold">Check Out</div>
                    </button>
                @else
                    <div class="text-gray-500 py-3">
                        <i class="fas fa-check-circle text-2xl mb-2"></i>
                        <div class="font-semibold">Day Complete</div>
                    </div>
                @endif
            </div>
        </div>

        <!-- Break -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="text-center">
                @if($todayAttendance && $todayAttendance->check_in && !$todayAttendance->check_out)
                    @if(!$todayAttendance->break_start)
                        <button onclick="startBreak()" class="w-full bg-yellow-600 text-white py-3 px-4 rounded-lg hover:bg-yellow-700 transition-colors">
                            <i class="fas fa-coffee text-2xl mb-2"></i>
                            <div class="font-semibold">Start Break</div>
                        </button>
                    @elseif(!$todayAttendance->break_end)
                        <button onclick="endBreak()" class="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors">
                            <i class="fas fa-play text-2xl mb-2"></i>
                            <div class="font-semibold">End Break</div>
                        </button>
                    @else
                        <div class="text-gray-500 py-3">
                            <i class="fas fa-check text-2xl mb-2"></i>
                            <div class="font-semibold">Break Taken</div>
                        </div>
                    @endif
                @else
                    <div class="text-gray-400 py-3">
                        <i class="fas fa-coffee text-2xl mb-2"></i>
                        <div class="font-semibold">Break</div>
                        <div class="text-xs">Check in first</div>
                    </div>
                @endif
            </div>
        </div>

        <!-- Current Status -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="text-center">
                <div class="text-2xl mb-2">
                    @if($employee->current_status === 'Working')
                        <i class="fas fa-user-clock text-green-600"></i>
                    @elseif($employee->current_status === 'On break')
                        <i class="fas fa-coffee text-yellow-600"></i>
                    @elseif($employee->current_status === 'Checked out')
                        <i class="fas fa-user-check text-blue-600"></i>
                    @else
                        <i class="fas fa-user-times text-gray-600"></i>
                    @endif
                </div>
                <div class="font-semibold text-gray-900">{{ $employee->current_status }}</div>
                @if($todayAttendance && $todayAttendance->check_in)
                    <div class="text-xs text-gray-500 mt-1">
                        Since {{ $todayAttendance->check_in->format('H:i') }}
                    </div>
                @endif
            </div>
        </div>

        <!-- Today's Hours -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="text-center">
                <i class="fas fa-clock text-2xl text-blue-600 mb-2"></i>
                <div class="font-semibold text-gray-900">Today's Hours</div>
                <div class="text-lg font-bold text-blue-600">
                    @if($todayAttendance && $todayAttendance->total_hours)
                        {{ $todayAttendance->formatted_total_hours }}
                    @else
                        0h 0m
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Today's Details -->
    @if($todayAttendance)
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Today's Activity</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div class="text-center p-4 bg-gray-50 rounded-lg">
                    <div class="text-sm text-gray-500">Check In</div>
                    <div class="text-lg font-semibold text-gray-900">
                        {{ $todayAttendance->check_in ? $todayAttendance->check_in->format('H:i') : '--:--' }}
                    </div>
                </div>
                <div class="text-center p-4 bg-gray-50 rounded-lg">
                    <div class="text-sm text-gray-500">Check Out</div>
                    <div class="text-lg font-semibold text-gray-900">
                        {{ $todayAttendance->check_out ? $todayAttendance->check_out->format('H:i') : '--:--' }}
                    </div>
                </div>
                <div class="text-center p-4 bg-gray-50 rounded-lg">
                    <div class="text-sm text-gray-500">Break Duration</div>
                    <div class="text-lg font-semibold text-gray-900">
                        {{ $todayAttendance->formatted_break_duration }}
                    </div>
                </div>
                <div class="text-center p-4 bg-gray-50 rounded-lg">
                    <div class="text-sm text-gray-500">Status</div>
                    <div class="text-lg font-semibold">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                            {{ $todayAttendance->status === 'present' ? 'bg-green-100 text-green-800' : 
                               ($todayAttendance->status === 'late' ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800') }}">
                            {{ ucfirst($todayAttendance->status) }}
                        </span>
                    </div>
                </div>
            </div>
        </div>
    @endif

    <!-- Monthly Statistics -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Monthly Stats -->
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">This Month's Summary</h3>
            <div class="grid grid-cols-2 gap-4">
                <div class="text-center p-4 bg-green-50 rounded-lg">
                    <div class="text-2xl font-bold text-green-600">{{ $stats['present_days'] }}</div>
                    <div class="text-sm text-gray-600">Present Days</div>
                </div>
                <div class="text-center p-4 bg-red-50 rounded-lg">
                    <div class="text-2xl font-bold text-red-600">{{ $stats['absent_days'] }}</div>
                    <div class="text-sm text-gray-600">Absent Days</div>
                </div>
                <div class="text-center p-4 bg-yellow-50 rounded-lg">
                    <div class="text-2xl font-bold text-yellow-600">{{ $stats['late_days'] }}</div>
                    <div class="text-sm text-gray-600">Late Days</div>
                </div>
                <div class="text-center p-4 bg-blue-50 rounded-lg">
                    <div class="text-2xl font-bold text-blue-600">
                        {{ floor($stats['total_hours'] / 60) }}h {{ $stats['total_hours'] % 60 }}m
                    </div>
                    <div class="text-sm text-gray-600">Total Hours</div>
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Recent Activity</h3>
                <a href="{{ route('attendance.history') }}" class="text-sm text-blue-600 hover:text-blue-500">View All</a>
            </div>
            @if($recentAttendances->count() > 0)
                <div class="space-y-3">
                    @foreach($recentAttendances->take(5) as $attendance)
                        <div class="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0">
                            <div>
                                <div class="text-sm font-medium text-gray-900">{{ $attendance->date->format('M d, Y') }}</div>
                                <div class="text-xs text-gray-500">{{ $attendance->date->format('l') }}</div>
                            </div>
                            <div class="text-right">
                                <div class="text-sm text-gray-900">
                                    {{ $attendance->check_in ? $attendance->check_in->format('H:i') : '--:--' }} - 
                                    {{ $attendance->check_out ? $attendance->check_out->format('H:i') : '--:--' }}
                                </div>
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium
                                    {{ $attendance->status_color === 'green' ? 'bg-green-100 text-green-800' : 
                                       ($attendance->status_color === 'yellow' ? 'bg-yellow-100 text-yellow-800' : 
                                       ($attendance->status_color === 'red' ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-800')) }}">
                                    {{ ucfirst($attendance->status) }}
                                </span>
                            </div>
                        </div>
                    @endforeach
                </div>
            @else
                <p class="text-gray-500 text-sm">No attendance records yet.</p>
            @endif
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function checkIn() {
    // Get location if available
    if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(function(position) {
            performCheckIn(position.coords.latitude, position.coords.longitude);
        }, function() {
            performCheckIn();
        });
    } else {
        performCheckIn();
    }
}

function performCheckIn(latitude = null, longitude = null) {
    const data = {};
    if (latitude && longitude) {
        data.latitude = latitude;
        data.longitude = longitude;
    }

    fetch('{{ route("attendance.check-in") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.error || 'Failed to check in');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Failed to check in');
    });
}

function checkOut() {
    if (confirm('Are you sure you want to check out?')) {
        fetch('{{ route("attendance.check-out") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.error || 'Failed to check out');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Failed to check out');
        });
    }
}

function startBreak() {
    fetch('{{ route("attendance.start-break") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.error || 'Failed to start break');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Failed to start break');
    });
}

function endBreak() {
    fetch('{{ route("attendance.end-break") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.error || 'Failed to end break');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Failed to end break');
    });
}
</script>
@endpush
