<!-- Top Bar -->
<div class="bg-secondary-800 text-white py-2 hidden lg:block">
    <div class="container-custom">
        <div class="flex justify-between items-center text-sm">
            <div class="flex items-center space-x-6">
                <div class="flex items-center space-x-2">
                    <i class="fas fa-map-marker-alt text-primary-400"></i>
                    <span>UK, USA, AU, PK</span>
                </div>
                <div class="flex items-center space-x-2">
                    <i class="fas fa-phone text-primary-400"></i>
                    <span>+92-308-4281241</span>
                </div>
                <div class="flex items-center space-x-2">
                    <i class="fas fa-envelope text-primary-400"></i>
                    <span><EMAIL></span>
                </div>
            </div>
            <div class="flex items-center space-x-4">
                <div class="flex space-x-3">
                    <a href="#" class="text-white hover:text-primary-400 transition-colors">
                        <i class="fab fa-facebook-f"></i>
                    </a>
                    <a href="#" class="text-white hover:text-primary-400 transition-colors">
                        <i class="fab fa-twitter"></i>
                    </a>
                    <a href="#" class="text-white hover:text-primary-400 transition-colors">
                        <i class="fab fa-linkedin-in"></i>
                    </a>
                    <a href="#" class="text-white hover:text-primary-400 transition-colors">
                        <i class="fab fa-youtube"></i>
                    </a>
                </div>
                <div class="border-l border-secondary-600 pl-4">
                    <a href="{{ route('login') }}" class="text-white hover:text-primary-400 transition-colors mr-3">Sign in</a>
                    <a href="{{ route('register') }}" class="text-white hover:text-primary-400 transition-colors">Sign up</a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Main Header -->
<header class="bg-white shadow-lg sticky top-0 z-50" x-data="{ mobileMenuOpen: false, servicesDropdownOpen: false }">
    <div class="container-custom">
        <div class="flex items-center justify-between py-4">
            <!-- Logo -->
            <div class="flex items-center">
                <a href="{{ route('home') }}" class="flex items-center">
                    <img src="{{ asset('images/logo.png') }}" alt="DIGIZALL" class="h-10 w-auto">
                </a>
            </div>

            <!-- Desktop Navigation -->
            <nav class="hidden lg:flex items-center space-x-8">
                <a href="{{ route('home') }}" class="text-gray-700 hover:text-primary-600 font-medium transition-colors {{ request()->routeIs('home') ? 'text-primary-600' : '' }}">
                    Home
                </a>
                
                <!-- Services Dropdown -->
                <div class="relative" @mouseenter="servicesDropdownOpen = true" @mouseleave="servicesDropdownOpen = false">
                    <a href="{{ route('services.index') }}" class="text-gray-700 hover:text-primary-600 font-medium transition-colors flex items-center {{ request()->routeIs('services.*') ? 'text-primary-600' : '' }}">
                        Services
                        <i class="fas fa-chevron-down ml-1 text-xs"></i>
                    </a>
                    
                    <!-- Dropdown Menu -->
                    <div x-show="servicesDropdownOpen" 
                         x-transition:enter="transition ease-out duration-200"
                         x-transition:enter-start="opacity-0 transform scale-95"
                         x-transition:enter-end="opacity-100 transform scale-100"
                         x-transition:leave="transition ease-in duration-150"
                         x-transition:leave-start="opacity-100 transform scale-100"
                         x-transition:leave-end="opacity-0 transform scale-95"
                         class="absolute top-full left-0 mt-2 w-80 bg-white rounded-lg shadow-xl border border-gray-100 py-4">
                        
                        <div class="grid grid-cols-1 gap-1">
                            <div class="px-4 py-2">
                                <h3 class="text-sm font-semibold text-gray-900 mb-2">Web Development</h3>
                                <div class="space-y-1">
                                    <a href="{{ route('services.show', 'website-design') }}" class="block px-3 py-2 text-sm text-gray-600 hover:text-primary-600 hover:bg-gray-50 rounded">Website Design</a>
                                    <a href="{{ route('services.show', 'web-application-development') }}" class="block px-3 py-2 text-sm text-gray-600 hover:text-primary-600 hover:bg-gray-50 rounded">Web Application Development</a>
                                </div>
                            </div>
                            
                            <div class="px-4 py-2">
                                <h3 class="text-sm font-semibold text-gray-900 mb-2">Digital Marketing</h3>
                                <div class="space-y-1">
                                    <a href="{{ route('services.show', 'search-engine-optimization') }}" class="block px-3 py-2 text-sm text-gray-600 hover:text-primary-600 hover:bg-gray-50 rounded">Search Engine Optimization</a>
                                    <a href="{{ route('services.show', 'social-media-marketing') }}" class="block px-3 py-2 text-sm text-gray-600 hover:text-primary-600 hover:bg-gray-50 rounded">Social Media Marketing</a>
                                </div>
                            </div>
                            
                            <div class="px-4 py-2">
                                <h3 class="text-sm font-semibold text-gray-900 mb-2">Content & Design</h3>
                                <div class="space-y-1">
                                    <a href="{{ route('services.show', 'content-writing') }}" class="block px-3 py-2 text-sm text-gray-600 hover:text-primary-600 hover:bg-gray-50 rounded">Content Writing</a>
                                    <a href="{{ route('services.show', 'graphic-design') }}" class="block px-3 py-2 text-sm text-gray-600 hover:text-primary-600 hover:bg-gray-50 rounded">Graphic Design</a>
                                </div>
                            </div>
                        </div>
                        
                        <div class="border-t border-gray-100 mt-4 pt-4 px-4">
                            <a href="{{ route('services.index') }}" class="block text-center bg-primary-600 text-white py-2 px-4 rounded-lg hover:bg-primary-700 transition-colors text-sm font-medium">
                                View All Services
                            </a>
                        </div>
                    </div>
                </div>

                <a href="{{ route('portfolio') }}" class="text-gray-700 hover:text-primary-600 font-medium transition-colors {{ request()->routeIs('portfolio') ? 'text-primary-600' : '' }}">
                    Portfolio
                </a>
                
                <a href="{{ route('testimonials') }}" class="text-gray-700 hover:text-primary-600 font-medium transition-colors {{ request()->routeIs('testimonials') ? 'text-primary-600' : '' }}">
                    Testimonials
                </a>
                
                <a href="{{ route('about') }}" class="text-gray-700 hover:text-primary-600 font-medium transition-colors {{ request()->routeIs('about') ? 'text-primary-600' : '' }}">
                    About
                </a>
                
                <a href="{{ route('blog.index') }}" class="text-gray-700 hover:text-primary-600 font-medium transition-colors {{ request()->routeIs('blog.*') ? 'text-primary-600' : '' }}">
                    Blog
                </a>
            </nav>

            <!-- CTA Buttons -->
            <div class="hidden lg:flex items-center space-x-4">
                <a href="{{ route('quote') }}" class="btn-primary">
                    Get Quote
                </a>
            </div>

            <!-- Mobile Menu Button -->
            <button @click="mobileMenuOpen = !mobileMenuOpen" class="lg:hidden text-gray-700 hover:text-primary-600 focus:outline-none">
                <i class="fas fa-bars text-xl" x-show="!mobileMenuOpen"></i>
                <i class="fas fa-times text-xl" x-show="mobileMenuOpen"></i>
            </button>
        </div>

        <!-- Mobile Menu -->
        <div x-show="mobileMenuOpen" 
             x-transition:enter="transition ease-out duration-200"
             x-transition:enter-start="opacity-0 transform -translate-y-2"
             x-transition:enter-end="opacity-100 transform translate-y-0"
             x-transition:leave="transition ease-in duration-150"
             x-transition:leave-start="opacity-100 transform translate-y-0"
             x-transition:leave-end="opacity-0 transform -translate-y-2"
             class="lg:hidden border-t border-gray-200 py-4">
            
            <div class="space-y-4">
                <a href="{{ route('home') }}" class="block text-gray-700 hover:text-primary-600 font-medium">Home</a>
                <a href="{{ route('services.index') }}" class="block text-gray-700 hover:text-primary-600 font-medium">Services</a>
                <a href="{{ route('portfolio') }}" class="block text-gray-700 hover:text-primary-600 font-medium">Portfolio</a>
                <a href="{{ route('testimonials') }}" class="block text-gray-700 hover:text-primary-600 font-medium">Testimonials</a>
                <a href="{{ route('about') }}" class="block text-gray-700 hover:text-primary-600 font-medium">About</a>
                <a href="{{ route('blog.index') }}" class="block text-gray-700 hover:text-primary-600 font-medium">Blog</a>
                <div class="pt-4 border-t border-gray-200">
                    <a href="{{ route('quote') }}" class="block w-full text-center btn-primary">Get Quote</a>
                </div>
            </div>
        </div>
    </div>
</header>
