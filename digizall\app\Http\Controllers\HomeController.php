<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Service;
use App\Models\Testimonial;
use App\Models\BlogPost;
use App\Models\Setting;

class HomeController extends Controller
{
    public function index()
    {
        // Get featured services
        $featuredServices = Service::active()
            ->featured()
            ->orderBy('sort_order')
            ->limit(6)
            ->get();

        // Get all active services for the services section
        $services = Service::active()
            ->orderBy('sort_order')
            ->limit(6)
            ->get();

        // Get featured testimonials
        $testimonials = Testimonial::active()
            ->featured()
            ->orderBy('sort_order')
            ->get();

        // Get latest blog posts
        $latestPosts = BlogPost::published()
            ->with(['category', 'author'])
            ->orderBy('published_at', 'desc')
            ->limit(3)
            ->get();

        // Get stats from settings
        $stats = [
            [
                'number' => Setting::get('projects_completed', 150),
                'label' => 'Finished Projects',
                'icon' => 'fas fa-check-circle',
                'suffix' => '+'
            ],
            [
                'number' => Setting::get('happy_clients', 120),
                'label' => 'Happy Clients',
                'icon' => 'fas fa-users',
                'suffix' => '+'
            ],
            [
                'number' => 100,
                'label' => 'Satisfaction',
                'icon' => 'fas fa-heart',
                'suffix' => '%'
            ],
            [
                'number' => Setting::get('years_experience', 8),
                'label' => 'Years of Expertise',
                'icon' => 'fas fa-calendar-alt',
                'suffix' => '+'
            ]
        ];

        return view('home', compact(
            'featuredServices',
            'services',
            'testimonials',
            'latestPosts',
            'stats'
        ));
    }
}
