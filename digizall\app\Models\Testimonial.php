<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Testimonial extends Model
{
    use HasFactory;

    protected $fillable = [
        'client_name',
        'client_position',
        'client_company',
        'testimonial',
        'client_image',
        'rating',
        'is_featured',
        'is_active',
        'sort_order'
    ];

    protected $casts = [
        'rating' => 'integer',
        'is_featured' => 'boolean',
        'is_active' => 'boolean'
    ];

    /**
     * Scope for active testimonials.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for featured testimonials.
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Get the client's image URL.
     */
    public function getClientImageUrlAttribute(): string
    {
        return $this->client_image
            ? asset('storage/' . $this->client_image)
            : 'https://ui-avatars.com/api/?name=' . urlencode($this->client_name) . '&color=7F9CF5&background=EBF4FF';
    }

    /**
     * Get the client's full title.
     */
    public function getClientFullTitleAttribute(): string
    {
        $title = $this->client_name;
        if ($this->client_position) {
            $title .= ', ' . $this->client_position;
        }
        if ($this->client_company) {
            $title .= ' at ' . $this->client_company;
        }
        return $title;
    }

    /**
     * Get star rating HTML.
     */
    public function getStarRatingHtmlAttribute(): string
    {
        $html = '';
        for ($i = 1; $i <= 5; $i++) {
            if ($i <= $this->rating) {
                $html .= '<i class="fas fa-star text-yellow-400"></i>';
            } else {
                $html .= '<i class="far fa-star text-gray-300"></i>';
            }
        }
        return $html;
    }

    /**
     * Get truncated testimonial.
     */
    public function getTruncatedTestimonialAttribute(): string
    {
        return Str::limit($this->testimonial, 150);
    }
}
