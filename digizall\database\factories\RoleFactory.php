<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Role>
 */
class RoleFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->unique()->slug(2),
            'display_name' => $this->faker->jobTitle(),
            'description' => $this->faker->sentence(),
            'permissions' => $this->faker->randomElements([
                'create_posts', 'edit_posts', 'delete_posts',
                'manage_users', 'view_analytics', 'manage_settings'
            ], $this->faker->numberBetween(1, 4)),
            'is_active' => true,
        ];
    }
}
