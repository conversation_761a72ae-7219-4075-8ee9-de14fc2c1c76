<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use App\Models\QuoteRequest as Quote;
use Illuminate\Support\Facades\Mail;

class QuoteTest extends TestCase
{
    use RefreshDatabase;

    public function test_quote_page_loads()
    {
        $response = $this->get('/quote');
        $response->assertStatus(200);
        $response->assertSee('Get Your Free Quote');
    }

    public function test_quote_form_submission_with_valid_data()
    {
        Mail::fake();

        $quoteData = [
            'name' => '<PERSON> Doe',
            'email' => '<EMAIL>',
            'phone' => '+1234567890',
            'company' => 'Test Company',
            'service' => 'web-development',
            'budget' => '5000-10000',
            'timeline' => '2-3-months',
            'project_type' => 'new-project',
            'description' => 'I need a new website for my business.',
            'preferred_contact' => 'email',
        ];

        $response = $this->post('/quote', $quoteData);

        $response->assertRedirect('/quote/success');
        $this->assertDatabaseHas('quotes', [
            'name' => '<PERSON>',
            'email' => '<EMAIL>',
            'service' => 'web-development',
        ]);
    }

    public function test_quote_form_validation_requires_name()
    {
        $quoteData = [
            'email' => '<EMAIL>',
            'service' => 'web-development',
            'budget' => '5000-10000',
            'timeline' => '2-3-months',
            'project_type' => 'new-project',
            'description' => 'Test description',
            'preferred_contact' => 'email',
        ];

        $response = $this->post('/quote', $quoteData);
        $response->assertSessionHasErrors('name');
    }

    public function test_quote_form_validation_requires_email()
    {
        $quoteData = [
            'name' => 'John Doe',
            'service' => 'web-development',
            'budget' => '5000-10000',
            'timeline' => '2-3-months',
            'project_type' => 'new-project',
            'description' => 'Test description',
            'preferred_contact' => 'email',
        ];

        $response = $this->post('/quote', $quoteData);
        $response->assertSessionHasErrors('email');
    }

    public function test_quote_form_validation_requires_valid_email()
    {
        $quoteData = [
            'name' => 'John Doe',
            'email' => 'invalid-email',
            'service' => 'web-development',
            'budget' => '5000-10000',
            'timeline' => '2-3-months',
            'project_type' => 'new-project',
            'description' => 'Test description',
            'preferred_contact' => 'email',
        ];

        $response = $this->post('/quote', $quoteData);
        $response->assertSessionHasErrors('email');
    }

    public function test_quote_success_page_loads()
    {
        $response = $this->get('/quote/success');
        $response->assertStatus(200);
        $response->assertSee('Quote Request Submitted Successfully');
    }

    public function test_quote_form_stores_ip_address()
    {
        Mail::fake();

        $quoteData = [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'service' => 'web-development',
            'budget' => '5000-10000',
            'timeline' => '2-3-months',
            'project_type' => 'new-project',
            'description' => 'Test description',
            'preferred_contact' => 'email',
        ];

        $response = $this->post('/quote', $quoteData);

        $this->assertDatabaseHas('quotes', [
            'name' => 'John Doe',
            'ip_address' => '127.0.0.1',
        ]);
    }

    public function test_quote_form_with_features()
    {
        Mail::fake();

        $quoteData = [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'service' => 'web-development',
            'budget' => '5000-10000',
            'timeline' => '2-3-months',
            'project_type' => 'new-project',
            'description' => 'Test description',
            'features' => ['responsive-design', 'cms-integration'],
            'preferred_contact' => 'email',
        ];

        $response = $this->post('/quote', $quoteData);

        $quote = Quote::where('email', '<EMAIL>')->first();
        $features = json_decode($quote->features, true);

        $this->assertContains('responsive-design', $features);
        $this->assertContains('cms-integration', $features);
    }
}
