<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class SEOMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);

        // Only process HTML responses
        if (!$this->isHtmlResponse($response)) {
            return $response;
        }

        // Add SEO headers
        $this->addSEOHeaders($response, $request);

        // Minify HTML in production
        if (config('app.env') === 'production') {
            $this->minifyHtml($response);
        }

        return $response;
    }

    /**
     * Check if response is HTML
     */
    private function isHtmlResponse($response): bool
    {
        $contentType = $response->headers->get('Content-Type', '');
        return str_contains($contentType, 'text/html') ||
               (empty($contentType) && is_string($response->getContent()));
    }

    /**
     * Add SEO-related headers
     */
    private function addSEOHeaders($response, $request): void
    {
        // Add security headers that also help with SEO
        $response->headers->set('X-Content-Type-Options', 'nosniff');
        $response->headers->set('X-Frame-Options', 'SAMEORIGIN');
        $response->headers->set('X-XSS-Protection', '1; mode=block');
        $response->headers->set('Referrer-Policy', 'strict-origin-when-cross-origin');

        // Add cache headers for static content
        if ($this->isStaticContent($request)) {
            $response->headers->set('Cache-Control', 'public, max-age=31536000'); // 1 year
        } else {
            $response->headers->set('Cache-Control', 'public, max-age=3600'); // 1 hour
        }

        // Add language header
        $response->headers->set('Content-Language', 'en');

        // Add canonical header for duplicate content prevention
        $canonical = $this->getCanonicalUrl($request);
        if ($canonical) {
            $response->headers->set('Link', '<' . $canonical . '>; rel="canonical"');
        }
    }

    /**
     * Check if request is for static content
     */
    private function isStaticContent($request): bool
    {
        $path = $request->path();
        $staticExtensions = ['css', 'js', 'png', 'jpg', 'jpeg', 'gif', 'svg', 'ico', 'woff', 'woff2', 'ttf', 'eot'];

        foreach ($staticExtensions as $ext) {
            if (str_ends_with($path, '.' . $ext)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Get canonical URL for the request
     */
    private function getCanonicalUrl($request): ?string
    {
        $url = $request->url();

        // Remove common tracking parameters
        $trackingParams = ['utm_source', 'utm_medium', 'utm_campaign', 'utm_term', 'utm_content', 'fbclid', 'gclid'];
        $queryParams = $request->query();

        foreach ($trackingParams as $param) {
            unset($queryParams[$param]);
        }

        if (!empty($queryParams)) {
            $url .= '?' . http_build_query($queryParams);
        }

        return $url;
    }

    /**
     * Minify HTML content
     */
    private function minifyHtml($response): void
    {
        $content = $response->getContent();

        if (!is_string($content)) {
            return;
        }

        // Basic HTML minification
        $content = preg_replace('/\s+/', ' ', $content); // Replace multiple spaces with single space
        $content = preg_replace('/>\s+</', '><', $content); // Remove spaces between tags
        $content = preg_replace('/\s+>/', '>', $content); // Remove spaces before closing tags
        $content = preg_replace('/<\s+/', '<', $content); // Remove spaces after opening tags

        // Remove HTML comments (but keep conditional comments)
        $content = preg_replace('/<!--(?!\s*(?:\[if [^\]]+]|<!|>))(?:(?!-->).)*-->/s', '', $content);

        $response->setContent(trim($content));
    }
}
