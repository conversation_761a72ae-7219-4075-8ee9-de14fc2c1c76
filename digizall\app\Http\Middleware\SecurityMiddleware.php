<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Services\SecurityService;

class SecurityMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check for suspicious IP addresses
        $ipCheck = SecurityService::checkSuspiciousIP($request->ip());
        if ($ipCheck['suspicious']) {
            SecurityService::logSecurityEvent('suspicious_ip_blocked', $ipCheck, 'warning');
            abort(403, 'Access denied from suspicious IP');
        }

        // Check for suspicious input patterns
        $inputData = array_merge($request->all(), $request->headers->all());
        $threats = SecurityService::detectSuspiciousInput($inputData);

        if (!empty($threats)) {
            SecurityService::logSecurityEvent('suspicious_input_detected', [
                'threats' => $threats,
                'url' => $request->fullUrl(),
                'method' => $request->method(),
            ], 'warning');

            // Block obvious attacks
            foreach ($threats as $threat) {
                if (in_array($threat['type'], ['sql_injection', 'xss', 'command_injection'])) {
                    abort(403, 'Malicious input detected');
                }
            }
        }

        // Add security headers to response
        $response = $next($request);

        $this->addSecurityHeaders($response);

        return $response;
    }

    /**
     * Add security headers to response
     */
    private function addSecurityHeaders($response): void
    {
        $headers = [
            'X-Content-Type-Options' => 'nosniff',
            'X-Frame-Options' => 'SAMEORIGIN',
            'X-XSS-Protection' => '1; mode=block',
            'Referrer-Policy' => 'strict-origin-when-cross-origin',
            'Permissions-Policy' => 'geolocation=(), microphone=(), camera=()',
            'Strict-Transport-Security' => 'max-age=31536000; includeSubDomains',
        ];

        foreach ($headers as $header => $value) {
            $response->headers->set($header, $value);
        }
    }
}
