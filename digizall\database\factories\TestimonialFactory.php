<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Testimonial>
 */
class TestimonialFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'client_name' => $this->faker->name(),
            'client_position' => $this->faker->jobTitle(),
            'client_company' => $this->faker->company(),
            'testimonial' => $this->faker->paragraph(3),
            'client_image' => null,
            'rating' => $this->faker->numberBetween(4, 5),
            'is_featured' => $this->faker->boolean(30),
            'is_active' => true,
            'sort_order' => $this->faker->numberBetween(1, 100),
        ];
    }
}
