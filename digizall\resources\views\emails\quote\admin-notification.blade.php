<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>New Quote Request - DIGIZALL</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
        .container { max-width: 700px; margin: 0 auto; padding: 20px; }
        .header { background: #dc3545; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
        .content { background: #ffffff; padding: 30px; border: 1px solid #e1e5e9; }
        .footer { background: #f8f9fa; padding: 15px; text-align: center; border-radius: 0 0 8px 8px; border: 1px solid #e1e5e9; border-top: none; }
        .urgent { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .client-info { background: #e8f5e8; padding: 20px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #28a745; }
        .project-details { background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0; }
        .features-list { background: #fff; border: 1px solid #dee2e6; padding: 15px; border-radius: 5px; }
        h1, h2, h3 { margin-top: 0; }
        .logo { font-size: 20px; font-weight: bold; }
        .priority { color: #dc3545; font-weight: bold; }
        table { width: 100%; border-collapse: collapse; margin: 15px 0; }
        th, td { padding: 8px 12px; text-align: left; border-bottom: 1px solid #dee2e6; }
        th { background: #f8f9fa; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="logo">🚨 DIGIZALL ADMIN ALERT</div>
            <h1>New Quote Request Received</h1>
            <p>{{ now()->format('F d, Y - g:i A') }}</p>
        </div>

        <!-- Content -->
        <div class="content">
            <div class="urgent">
                <h2>⚡ Action Required</h2>
                <p class="priority">A new quote request has been submitted and requires immediate attention. Please review and respond within 24 hours.</p>
            </div>

            <!-- Client Information -->
            <div class="client-info">
                <h3>👤 Client Information</h3>
                <table>
                    <tr><th>Name:</th><td>{{ $quote->name }}</td></tr>
                    <tr><th>Email:</th><td><a href="mailto:{{ $quote->email }}">{{ $quote->email }}</a></td></tr>
                    @if($quote->phone)
                        <tr><th>Phone:</th><td><a href="tel:{{ $quote->phone }}">{{ $quote->phone }}</a></td></tr>
                    @endif
                    @if($quote->company)
                        <tr><th>Company:</th><td>{{ $quote->company }}</td></tr>
                    @endif
                    @if($quote->website)
                        <tr><th>Website:</th><td><a href="{{ $quote->website }}" target="_blank">{{ $quote->website }}</a></td></tr>
                    @endif
                    <tr><th>Preferred Contact:</th><td>{{ ucfirst($quote->preferred_contact) }}</td></tr>
                    <tr><th>Marketing Consent:</th><td>{{ $quote->marketing_consent ? 'Yes' : 'No' }}</td></tr>
                </table>
            </div>

            <!-- Project Details -->
            <div class="project-details">
                <h3>🎯 Project Details</h3>
                <table>
                    <tr><th>Service:</th><td>{{ ucwords(str_replace('-', ' ', $quote->service)) }}</td></tr>
                    <tr><th>Project Type:</th><td>{{ ucwords(str_replace('-', ' ', $quote->project_type)) }}</td></tr>
                    <tr><th>Budget Range:</th><td>{{ ucwords(str_replace('-', ' ', $quote->budget)) }}</td></tr>
                    <tr><th>Timeline:</th><td>{{ ucwords(str_replace('-', ' ', $quote->timeline)) }}</td></tr>
                </table>

                <h4>📝 Project Description:</h4>
                <div style="background: #fff; padding: 15px; border: 1px solid #dee2e6; border-radius: 5px; white-space: pre-wrap;">{{ $quote->description }}</div>

                @if($quote->features)
                    <h4>✅ Required Features:</h4>
                    <div class="features-list">
                        @php
                            $features = json_decode($quote->features, true) ?? [];
                        @endphp
                        @if(count($features) > 0)
                            <ul>
                                @foreach($features as $feature)
                                    <li>{{ ucwords(str_replace('-', ' ', $feature)) }}</li>
                                @endforeach
                            </ul>
                        @else
                            <p><em>No specific features selected</em></p>
                        @endif
                    </div>
                @endif

                @if($quote->additional_info)
                    <h4>ℹ️ Additional Information:</h4>
                    <div style="background: #fff; padding: 15px; border: 1px solid #dee2e6; border-radius: 5px; white-space: pre-wrap;">{{ $quote->additional_info }}</div>
                @endif
            </div>

            <!-- Technical Information -->
            <div style="background: #f1f3f4; padding: 15px; border-radius: 5px; margin: 20px 0;">
                <h4>🔧 Technical Information</h4>
                <p><strong>IP Address:</strong> {{ $quote->ip_address }}</p>
                <p><strong>User Agent:</strong> {{ $quote->user_agent }}</p>
                <p><strong>Submitted:</strong> {{ $quote->created_at->format('F d, Y - g:i A T') }}</p>
            </div>

            <!-- Quick Actions -->
            <div style="text-align: center; margin: 30px 0; padding: 20px; background: #e3f2fd; border-radius: 5px;">
                <h3>🚀 Quick Actions</h3>
                <p>
                    <a href="mailto:{{ $quote->email }}?subject=Re: Your Quote Request - {{ ucwords(str_replace('-', ' ', $quote->service)) }}" 
                       style="display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 5px;">
                        📧 Reply via Email
                    </a>
                    @if($quote->phone)
                        <a href="tel:{{ $quote->phone }}" 
                           style="display: inline-block; padding: 10px 20px; background: #28a745; color: white; text-decoration: none; border-radius: 5px; margin: 5px;">
                            📞 Call Client
                        </a>
                    @endif
                    @if($quote->preferred_contact === 'whatsapp' && $quote->phone)
                        <a href="https://wa.me/{{ str_replace(['+', '-', ' '], '', $quote->phone) }}" target="_blank"
                           style="display: inline-block; padding: 10px 20px; background: #25d366; color: white; text-decoration: none; border-radius: 5px; margin: 5px;">
                            💬 WhatsApp
                        </a>
                    @endif
                </p>
            </div>

            <!-- Priority Assessment -->
            <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px;">
                <h4>⚠️ Priority Assessment</h4>
                @php
                    $priority = 'Medium';
                    $priorityColor = '#ffc107';
                    
                    if (in_array($quote->timeline, ['asap', '1-month'])) {
                        $priority = 'High';
                        $priorityColor = '#dc3545';
                    } elseif (in_array($quote->budget, ['25000-50000', 'over-50000'])) {
                        $priority = 'High';
                        $priorityColor = '#dc3545';
                    } elseif ($quote->timeline === 'flexible') {
                        $priority = 'Low';
                        $priorityColor = '#28a745';
                    }
                @endphp
                <p><strong>Suggested Priority:</strong> <span style="color: {{ $priorityColor }}; font-weight: bold;">{{ $priority }}</span></p>
                <p><strong>Reasoning:</strong> 
                    @if($priority === 'High')
                        Urgent timeline or high budget range - requires immediate attention
                    @elseif($priority === 'Low')
                        Flexible timeline - can be scheduled accordingly
                    @else
                        Standard priority - respond within normal timeframe
                    @endif
                </p>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p><strong>DIGIZALL Admin Panel</strong></p>
            <p>This is an automated notification. Please respond to the client promptly.</p>
        </div>
    </div>
</body>
</html>
