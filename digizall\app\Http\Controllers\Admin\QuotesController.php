<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\QuoteRequest as Quote;

class QuotesController extends Controller
{
    public function __construct()
    {
        $this->middleware('admin');
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Quote::query();

        // Search functionality
        if ($request->has('search') && $request->search) {
            $query->where(function($q) use ($request) {
                $q->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('email', 'like', '%' . $request->search . '%')
                  ->orWhere('company', 'like', '%' . $request->search . '%')
                  ->orWhere('service', 'like', '%' . $request->search . '%');
            });
        }

        // Status filter
        if ($request->has('status') && $request->status) {
            $query->where('status', $request->status);
        }

        // Service filter
        if ($request->has('service') && $request->service) {
            $query->where('service', $request->service);
        }

        // Date range filter
        if ($request->has('date_from') && $request->date_from) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }
        if ($request->has('date_to') && $request->date_to) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $quotes = $query->orderBy('created_at', 'desc')->paginate(15);

        // Get unique services for filter
        $services = Quote::distinct()->pluck('service')->filter()->sort();

        return view('admin.quotes.index', compact('quotes', 'services'));
    }

    /**
     * Display the specified resource.
     */
    public function show(Quote $quote)
    {
        return view('admin.quotes.show', compact('quote'));
    }

    /**
     * Update the status of the quote.
     */
    public function updateStatus(Request $request, Quote $quote)
    {
        $validated = $request->validate([
            'status' => 'required|in:pending,in_progress,quoted,accepted,rejected,completed',
            'notes' => 'nullable|string|max:1000',
        ]);

        $quote->update([
            'status' => $validated['status'],
            'admin_notes' => $validated['notes'],
            'updated_at' => now(),
        ]);

        return redirect()->back()->with('success', 'Quote status updated successfully.');
    }

    /**
     * Add notes to the quote.
     */
    public function addNotes(Request $request, Quote $quote)
    {
        $validated = $request->validate([
            'notes' => 'required|string|max:1000',
        ]);

        $currentNotes = $quote->admin_notes ?? '';
        $newNote = "[" . now()->format('Y-m-d H:i') . "] " . $validated['notes'];

        $quote->update([
            'admin_notes' => $currentNotes ? $currentNotes . "\n\n" . $newNote : $newNote,
        ]);

        return redirect()->back()->with('success', 'Notes added successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Quote $quote)
    {
        $quote->delete();

        return redirect()->route('admin.quotes.index')
                        ->with('success', 'Quote deleted successfully.');
    }

    /**
     * Export quotes to CSV.
     */
    public function export(Request $request)
    {
        $query = Quote::query();

        // Apply same filters as index
        if ($request->has('search') && $request->search) {
            $query->where(function($q) use ($request) {
                $q->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('email', 'like', '%' . $request->search . '%')
                  ->orWhere('company', 'like', '%' . $request->search . '%')
                  ->orWhere('service', 'like', '%' . $request->search . '%');
            });
        }

        if ($request->has('status') && $request->status) {
            $query->where('status', $request->status);
        }

        if ($request->has('service') && $request->service) {
            $query->where('service', $request->service);
        }

        if ($request->has('date_from') && $request->date_from) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }
        if ($request->has('date_to') && $request->date_to) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $quotes = $query->orderBy('created_at', 'desc')->get();

        $filename = 'quotes_' . now()->format('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($quotes) {
            $file = fopen('php://output', 'w');

            // CSV headers
            fputcsv($file, [
                'ID', 'Name', 'Email', 'Phone', 'Company', 'Website', 'Service',
                'Project Type', 'Budget', 'Timeline', 'Status', 'Submitted At'
            ]);

            // CSV data
            foreach ($quotes as $quote) {
                fputcsv($file, [
                    $quote->id,
                    $quote->name,
                    $quote->email,
                    $quote->phone,
                    $quote->company,
                    $quote->website,
                    $quote->service,
                    $quote->project_type,
                    $quote->budget,
                    $quote->timeline,
                    $quote->status,
                    $quote->created_at->format('Y-m-d H:i:s'),
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
