<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Attendance System Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration settings for the employee attendance system.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Working Hours
    |--------------------------------------------------------------------------
    |
    | Define the standard working hours for the organization.
    |
    */

    'working_hours' => [
        'standard_start_time' => env('ATTENDANCE_START_TIME', '09:00'),
        'standard_end_time' => env('ATTENDANCE_END_TIME', '17:00'),
        'standard_hours_per_day' => env('ATTENDANCE_HOURS_PER_DAY', 8),
        'lunch_break_duration' => env('ATTENDANCE_LUNCH_BREAK', 60), // minutes
        'short_break_duration' => env('ATTENDANCE_SHORT_BREAK', 15), // minutes
    ],

    /*
    |--------------------------------------------------------------------------
    | Check-in/Check-out Rules
    |--------------------------------------------------------------------------
    |
    | Rules and restrictions for employee check-in and check-out.
    |
    */

    'check_in_rules' => [
        'allowed_check_in_start' => env('ATTENDANCE_EARLY_CHECK_IN', '06:00'),
        'allowed_check_in_end' => env('ATTENDANCE_LATE_CHECK_IN', '12:00'),
        'grace_period_minutes' => env('ATTENDANCE_GRACE_PERIOD', 15),
        'require_location' => env('ATTENDANCE_REQUIRE_LOCATION', false),
        'require_photo' => env('ATTENDANCE_REQUIRE_PHOTO', false),
        'allow_multiple_check_ins' => env('ATTENDANCE_ALLOW_MULTIPLE_CHECK_INS', false),
    ],

    /*
    |--------------------------------------------------------------------------
    | Location Tracking
    |--------------------------------------------------------------------------
    |
    | Settings for location-based attendance tracking.
    |
    */

    'location_tracking' => [
        'enabled' => env('ATTENDANCE_LOCATION_TRACKING', false),
        'office_locations' => [
            [
                'name' => 'Main Office',
                'latitude' => env('OFFICE_LATITUDE', 40.7128),
                'longitude' => env('OFFICE_LONGITUDE', -74.0060),
                'radius' => env('OFFICE_RADIUS', 100), // meters
            ],
        ],
        'allow_remote_work' => env('ATTENDANCE_ALLOW_REMOTE_WORK', true),
        'require_approval_for_remote' => env('ATTENDANCE_REQUIRE_REMOTE_APPROVAL', false),
    ],

    /*
    |--------------------------------------------------------------------------
    | Overtime Configuration
    |--------------------------------------------------------------------------
    |
    | Settings for overtime calculation and approval.
    |
    */

    'overtime' => [
        'enabled' => env('ATTENDANCE_OVERTIME_ENABLED', true),
        'daily_threshold' => env('ATTENDANCE_OVERTIME_DAILY_THRESHOLD', 8), // hours
        'weekly_threshold' => env('ATTENDANCE_OVERTIME_WEEKLY_THRESHOLD', 40), // hours
        'require_approval' => env('ATTENDANCE_OVERTIME_REQUIRE_APPROVAL', true),
        'auto_approve_limit' => env('ATTENDANCE_OVERTIME_AUTO_APPROVE_LIMIT', 2), // hours
        'rates' => [
            'weekday' => env('ATTENDANCE_OVERTIME_WEEKDAY_RATE', 1.5),
            'weekend' => env('ATTENDANCE_OVERTIME_WEEKEND_RATE', 2.0),
            'holiday' => env('ATTENDANCE_OVERTIME_HOLIDAY_RATE', 2.5),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Break Management
    |--------------------------------------------------------------------------
    |
    | Configuration for break tracking and management.
    |
    */

    'breaks' => [
        'track_breaks' => env('ATTENDANCE_TRACK_BREAKS', true),
        'max_break_duration' => env('ATTENDANCE_MAX_BREAK_DURATION', 120), // minutes
        'require_break_approval' => env('ATTENDANCE_REQUIRE_BREAK_APPROVAL', false),
        'auto_break_deduction' => env('ATTENDANCE_AUTO_BREAK_DEDUCTION', true),
        'break_types' => [
            'lunch' => [
                'name' => 'Lunch Break',
                'default_duration' => 60, // minutes
                'paid' => false,
            ],
            'short' => [
                'name' => 'Short Break',
                'default_duration' => 15, // minutes
                'paid' => true,
            ],
            'personal' => [
                'name' => 'Personal Break',
                'default_duration' => 30, // minutes
                'paid' => false,
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Late Arrival & Early Departure
    |--------------------------------------------------------------------------
    |
    | Settings for handling late arrivals and early departures.
    |
    */

    'tardiness' => [
        'late_threshold_minutes' => env('ATTENDANCE_LATE_THRESHOLD', 15),
        'early_departure_threshold_minutes' => env('ATTENDANCE_EARLY_DEPARTURE_THRESHOLD', 30),
        'require_reason_for_late' => env('ATTENDANCE_REQUIRE_LATE_REASON', true),
        'require_reason_for_early_departure' => env('ATTENDANCE_REQUIRE_EARLY_REASON', true),
        'auto_deduct_late_time' => env('ATTENDANCE_AUTO_DEDUCT_LATE_TIME', true),
        'notifications' => [
            'notify_manager_on_late' => env('ATTENDANCE_NOTIFY_MANAGER_LATE', true),
            'notify_hr_on_excessive_tardiness' => env('ATTENDANCE_NOTIFY_HR_TARDINESS', true),
            'tardiness_threshold_count' => env('ATTENDANCE_TARDINESS_THRESHOLD', 3), // per month
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Reporting & Analytics
    |--------------------------------------------------------------------------
    |
    | Configuration for attendance reports and analytics.
    |
    */

    'reporting' => [
        'generate_daily_reports' => env('ATTENDANCE_DAILY_REPORTS', true),
        'generate_weekly_reports' => env('ATTENDANCE_WEEKLY_REPORTS', true),
        'generate_monthly_reports' => env('ATTENDANCE_MONTHLY_REPORTS', true),
        'email_reports_to_managers' => env('ATTENDANCE_EMAIL_REPORTS_MANAGERS', false),
        'email_reports_to_hr' => env('ATTENDANCE_EMAIL_REPORTS_HR', true),
        'report_schedule' => env('ATTENDANCE_REPORT_SCHEDULE', 'weekly'), // daily, weekly, monthly
        'include_charts' => env('ATTENDANCE_REPORTS_INCLUDE_CHARTS', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Notifications
    |--------------------------------------------------------------------------
    |
    | Notification settings for the attendance system.
    |
    */

    'notifications' => [
        'email_notifications' => env('ATTENDANCE_EMAIL_NOTIFICATIONS', true),
        'sms_notifications' => env('ATTENDANCE_SMS_NOTIFICATIONS', false),
        'push_notifications' => env('ATTENDANCE_PUSH_NOTIFICATIONS', false),
        'notification_types' => [
            'check_in_reminder' => env('ATTENDANCE_NOTIFY_CHECK_IN_REMINDER', true),
            'check_out_reminder' => env('ATTENDANCE_NOTIFY_CHECK_OUT_REMINDER', true),
            'overtime_alert' => env('ATTENDANCE_NOTIFY_OVERTIME_ALERT', true),
            'missing_attendance' => env('ATTENDANCE_NOTIFY_MISSING_ATTENDANCE', true),
            'schedule_changes' => env('ATTENDANCE_NOTIFY_SCHEDULE_CHANGES', true),
        ],
        'reminder_times' => [
            'check_in_reminder' => env('ATTENDANCE_CHECK_IN_REMINDER_TIME', '08:45'),
            'check_out_reminder' => env('ATTENDANCE_CHECK_OUT_REMINDER_TIME', '17:00'),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Integration Settings
    |--------------------------------------------------------------------------
    |
    | Settings for integrating with external systems.
    |
    */

    'integrations' => [
        'payroll_system' => [
            'enabled' => env('ATTENDANCE_PAYROLL_INTEGRATION', false),
            'export_format' => env('ATTENDANCE_PAYROLL_EXPORT_FORMAT', 'csv'),
            'auto_export' => env('ATTENDANCE_PAYROLL_AUTO_EXPORT', false),
            'export_schedule' => env('ATTENDANCE_PAYROLL_EXPORT_SCHEDULE', 'monthly'),
        ],
        'biometric_devices' => [
            'enabled' => env('ATTENDANCE_BIOMETRIC_ENABLED', false),
            'device_type' => env('ATTENDANCE_BIOMETRIC_DEVICE_TYPE', 'fingerprint'),
            'sync_interval' => env('ATTENDANCE_BIOMETRIC_SYNC_INTERVAL', 60), // minutes
        ],
        'access_control' => [
            'enabled' => env('ATTENDANCE_ACCESS_CONTROL', false),
            'sync_with_door_systems' => env('ATTENDANCE_SYNC_DOOR_SYSTEMS', false),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Security & Audit
    |--------------------------------------------------------------------------
    |
    | Security and audit settings for the attendance system.
    |
    */

    'security' => [
        'log_all_activities' => env('ATTENDANCE_LOG_ACTIVITIES', true),
        'require_manager_approval_for_edits' => env('ATTENDANCE_REQUIRE_MANAGER_APPROVAL', true),
        'allow_self_correction' => env('ATTENDANCE_ALLOW_SELF_CORRECTION', false),
        'correction_time_limit_hours' => env('ATTENDANCE_CORRECTION_TIME_LIMIT', 24),
        'ip_restriction' => [
            'enabled' => env('ATTENDANCE_IP_RESTRICTION', false),
            'allowed_ips' => explode(',', env('ATTENDANCE_ALLOWED_IPS', '')),
        ],
        'device_restriction' => [
            'enabled' => env('ATTENDANCE_DEVICE_RESTRICTION', false),
            'max_devices_per_user' => env('ATTENDANCE_MAX_DEVICES', 2),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Holiday & Leave Management
    |--------------------------------------------------------------------------
    |
    | Settings for handling holidays and leave requests.
    |
    */

    'holidays' => [
        'auto_mark_holidays' => env('ATTENDANCE_AUTO_MARK_HOLIDAYS', true),
        'require_attendance_on_holidays' => env('ATTENDANCE_REQUIRE_HOLIDAY_ATTENDANCE', false),
        'holiday_pay_rate' => env('ATTENDANCE_HOLIDAY_PAY_RATE', 2.0),
        'weekend_work' => [
            'allowed' => env('ATTENDANCE_ALLOW_WEEKEND_WORK', false),
            'require_approval' => env('ATTENDANCE_REQUIRE_WEEKEND_APPROVAL', true),
            'pay_rate' => env('ATTENDANCE_WEEKEND_PAY_RATE', 1.5),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Mobile App Settings
    |--------------------------------------------------------------------------
    |
    | Configuration for mobile attendance app.
    |
    */

    'mobile_app' => [
        'enabled' => env('ATTENDANCE_MOBILE_APP_ENABLED', true),
        'require_app_for_attendance' => env('ATTENDANCE_REQUIRE_MOBILE_APP', false),
        'offline_mode' => env('ATTENDANCE_MOBILE_OFFLINE_MODE', true),
        'sync_interval' => env('ATTENDANCE_MOBILE_SYNC_INTERVAL', 15), // minutes
        'features' => [
            'gps_tracking' => env('ATTENDANCE_MOBILE_GPS_TRACKING', false),
            'photo_capture' => env('ATTENDANCE_MOBILE_PHOTO_CAPTURE', false),
            'qr_code_scanning' => env('ATTENDANCE_MOBILE_QR_CODE', false),
            'face_recognition' => env('ATTENDANCE_MOBILE_FACE_RECOGNITION', false),
        ],
    ],

];
