@extends('layouts.app')

@section('title', 'Quote Request Submitted - DIGIZALL')
@section('meta_description', 'Thank you for your quote request. We have received your project details and will get back to you within 24 hours.')

@section('content')

<!-- Success Section -->
<section class="section-padding">
    <div class="container-custom">
        <div class="max-w-2xl mx-auto text-center">
            <!-- Success Icon -->
            <div class="w-24 h-24 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-8" data-aos="zoom-in">
                <i class="fas fa-check text-4xl text-green-600"></i>
            </div>

            <!-- Success Message -->
            <div data-aos="fade-up" data-aos-delay="200">
                <h1 class="heading-secondary text-gray-900 mb-6">
                    Quote Request Submitted Successfully!
                </h1>
                <p class="text-xl text-gray-600 mb-8">
                    Thank you for choosing DIGIZALL. We have received your project details and our team is already reviewing your requirements.
                </p>
            </div>

            <!-- What Happens Next -->
            <div class="bg-gray-50 rounded-xl p-8 mb-8" data-aos="fade-up" data-aos-delay="400">
                <h2 class="text-2xl font-bold text-gray-900 mb-6">What Happens Next?</h2>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <!-- Step 1 -->
                    <div class="text-center">
                        <div class="w-12 h-12 bg-primary-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 font-bold">
                            1
                        </div>
                        <h3 class="font-semibold text-gray-900 mb-2">Review</h3>
                        <p class="text-sm text-gray-600">Our team reviews your project requirements in detail</p>
                    </div>

                    <!-- Step 2 -->
                    <div class="text-center">
                        <div class="w-12 h-12 bg-primary-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 font-bold">
                            2
                        </div>
                        <h3 class="font-semibold text-gray-900 mb-2">Prepare</h3>
                        <p class="text-sm text-gray-600">We prepare a detailed quote with timeline and pricing</p>
                    </div>

                    <!-- Step 3 -->
                    <div class="text-center">
                        <div class="w-12 h-12 bg-primary-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 font-bold">
                            3
                        </div>
                        <h3 class="font-semibold text-gray-900 mb-2">Contact</h3>
                        <p class="text-sm text-gray-600">We'll contact you within 24 hours with your custom quote</p>
                    </div>
                </div>
            </div>

            <!-- Timeline -->
            <div class="bg-primary-50 border border-primary-200 rounded-lg p-6 mb-8" data-aos="fade-up" data-aos-delay="600">
                <div class="flex items-center justify-center mb-4">
                    <i class="fas fa-clock text-primary-600 mr-2"></i>
                    <span class="font-semibold text-primary-800">Expected Response Time</span>
                </div>
                <p class="text-primary-700">
                    <strong>Within 24 hours</strong> - We'll send you a detailed quote via your preferred contact method
                </p>
            </div>

            <!-- Contact Information -->
            <div class="bg-white border border-gray-200 rounded-xl p-6 mb-8" data-aos="fade-up" data-aos-delay="800">
                <h3 class="text-xl font-bold text-gray-900 mb-4">Need Immediate Assistance?</h3>
                <p class="text-gray-600 mb-6">
                    If you have urgent questions or need to discuss your project immediately, feel free to contact us directly.
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="tel:+923084281241" class="flex items-center justify-center px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors">
                        <i class="fas fa-phone mr-2"></i>
                        Call Us Now
                    </a>
                    <a href="https://wa.me/+923084281241" target="_blank" class="flex items-center justify-center px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                        <i class="fab fa-whatsapp mr-2"></i>
                        WhatsApp Chat
                    </a>
                    <a href="mailto:<EMAIL>" class="flex items-center justify-center px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
                        <i class="fas fa-envelope mr-2"></i>
                        Send Email
                    </a>
                </div>
            </div>

            <!-- Additional Actions -->
            <div class="flex flex-col sm:flex-row gap-4 justify-center" data-aos="fade-up" data-aos-delay="1000">
                <a href="{{ route('home') }}" class="btn-secondary">
                    <i class="fas fa-home mr-2"></i>
                    Back to Home
                </a>
                <a href="{{ route('portfolio') }}" class="btn-primary">
                    <i class="fas fa-eye mr-2"></i>
                    View Our Work
                </a>
                <a href="{{ route('services.index') }}" class="btn-secondary">
                    <i class="fas fa-cogs mr-2"></i>
                    Our Services
                </a>
            </div>
        </div>
    </div>
</section>

<!-- FAQ Section -->
<section class="section-padding bg-gray-50">
    <div class="container-custom">
        <div class="max-w-4xl mx-auto">
            <div class="text-center mb-12" data-aos="fade-up">
                <h2 class="heading-secondary mb-4">Frequently Asked Questions</h2>
                <p class="text-body">
                    Common questions about our quote process and what to expect next.
                </p>
            </div>

            <div class="space-y-6" x-data="{ openFaq: null }">
                <!-- FAQ 1 -->
                <div class="bg-white rounded-lg shadow-lg" data-aos="fade-up">
                    <button @click="openFaq = openFaq === 1 ? null : 1" 
                            class="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors">
                        <h3 class="font-semibold text-gray-900">How accurate will my quote be?</h3>
                        <i class="fas fa-chevron-down transform transition-transform" 
                           :class="{ 'rotate-180': openFaq === 1 }"></i>
                    </button>
                    <div x-show="openFaq === 1" 
                         x-transition:enter="transition ease-out duration-200"
                         x-transition:enter-start="opacity-0 transform -translate-y-2"
                         x-transition:enter-end="opacity-100 transform translate-y-0"
                         class="px-6 pb-4">
                        <p class="text-gray-600">Our quotes are highly accurate based on the information you provide. We include detailed breakdowns of costs, timelines, and deliverables. Final pricing may only change if project scope changes during development.</p>
                    </div>
                </div>

                <!-- FAQ 2 -->
                <div class="bg-white rounded-lg shadow-lg" data-aos="fade-up" data-aos-delay="100">
                    <button @click="openFaq = openFaq === 2 ? null : 2" 
                            class="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors">
                        <h3 class="font-semibold text-gray-900">What if I need to change my requirements?</h3>
                        <i class="fas fa-chevron-down transform transition-transform" 
                           :class="{ 'rotate-180': openFaq === 2 }"></i>
                    </button>
                    <div x-show="openFaq === 2" 
                         x-transition:enter="transition ease-out duration-200"
                         x-transition:enter-start="opacity-0 transform -translate-y-2"
                         x-transition:enter-end="opacity-100 transform translate-y-0"
                         class="px-6 pb-4">
                        <p class="text-gray-600">No problem! We understand that project requirements can evolve. Simply contact us with your updated requirements, and we'll provide a revised quote. We're flexible and work with you to achieve your goals.</p>
                    </div>
                </div>

                <!-- FAQ 3 -->
                <div class="bg-white rounded-lg shadow-lg" data-aos="fade-up" data-aos-delay="200">
                    <button @click="openFaq = openFaq === 3 ? null : 3" 
                            class="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors">
                        <h3 class="font-semibold text-gray-900">Do you offer payment plans?</h3>
                        <i class="fas fa-chevron-down transform transition-transform" 
                           :class="{ 'rotate-180': openFaq === 3 }"></i>
                    </button>
                    <div x-show="openFaq === 3" 
                         x-transition:enter="transition ease-out duration-200"
                         x-transition:enter-start="opacity-0 transform -translate-y-2"
                         x-transition:enter-end="opacity-100 transform translate-y-0"
                         class="px-6 pb-4">
                        <p class="text-gray-600">Yes! We offer flexible payment plans to suit different budgets. Typically, we work with milestone-based payments or monthly installments for larger projects. Payment options will be discussed in your quote.</p>
                    </div>
                </div>

                <!-- FAQ 4 -->
                <div class="bg-white rounded-lg shadow-lg" data-aos="fade-up" data-aos-delay="300">
                    <button @click="openFaq = openFaq === 4 ? null : 4" 
                            class="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors">
                        <h3 class="font-semibold text-gray-900">What happens after I approve the quote?</h3>
                        <i class="fas fa-chevron-down transform transition-transform" 
                           :class="{ 'rotate-180': openFaq === 4 }"></i>
                    </button>
                    <div x-show="openFaq === 4" 
                         x-transition:enter="transition ease-out duration-200"
                         x-transition:enter-start="opacity-0 transform -translate-y-2"
                         x-transition:enter-end="opacity-100 transform translate-y-0"
                         class="px-6 pb-4">
                        <p class="text-gray-600">Once you approve the quote, we'll send you a detailed contract and project timeline. After signing and initial payment, we'll assign your dedicated project team and begin the discovery phase to finalize all project details.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

@endsection
