<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Service>
 */
class ServiceFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $title = $this->faker->words(3, true);

        return [
            'title' => $title,
            'slug' => \Illuminate\Support\Str::slug($title),
            'short_description' => $this->faker->sentence(),
            'description' => $this->faker->paragraphs(3, true),
            'icon' => null,
            'image' => null,
            'category' => $this->faker->randomElement([
                'Web Development', 'Digital Marketing', 'SEO',
                'Content Writing', 'Graphic Design'
            ]),
            'process_steps' => [
                ['title' => 'Discovery', 'description' => 'Understanding your requirements'],
                ['title' => 'Planning', 'description' => 'Creating a detailed plan'],
                ['title' => 'Execution', 'description' => 'Implementing the solution'],
                ['title' => 'Delivery', 'description' => 'Delivering the final product']
            ],
            'faq' => [
                ['question' => 'How long does it take?', 'answer' => 'It depends on the project scope.'],
                ['question' => 'What is included?', 'answer' => 'All features mentioned in the plan.']
            ],
            'is_featured' => $this->faker->boolean(20),
            'is_active' => true,
            'sort_order' => $this->faker->numberBetween(1, 100),
            'meta_title' => $title,
            'meta_description' => $this->faker->sentence(),
        ];
    }
}
