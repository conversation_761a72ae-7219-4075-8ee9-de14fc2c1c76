@props([
    'plans' => [],
    'service' => null,
    'title' => 'Choose Your Plan',
    'subtitle' => 'Select the perfect plan for your needs',
    'showComparison' => false
])

<section class="section-padding bg-gray-50">
    <div class="container-custom">
        <!-- Section Header -->
        <div class="text-center mb-16" data-aos="fade-up">
            <h2 class="heading-secondary mb-4">{{ $title }}</h2>
            <p class="text-body max-w-2xl mx-auto">
                {{ $subtitle }}
            </p>
        </div>

        <!-- Pricing Toggle (if needed) -->
        @if($showComparison)
            <div class="flex justify-center mb-12" data-aos="fade-up">
                <div class="bg-white rounded-lg p-1 shadow-lg" x-data="{ billing: 'monthly' }">
                    <button @click="billing = 'monthly'" 
                            :class="billing === 'monthly' ? 'bg-primary-600 text-white' : 'text-gray-600'"
                            class="px-6 py-2 rounded-md font-medium transition-all duration-200">
                        Monthly
                    </button>
                    <button @click="billing = 'yearly'" 
                            :class="billing === 'yearly' ? 'bg-primary-600 text-white' : 'text-gray-600'"
                            class="px-6 py-2 rounded-md font-medium transition-all duration-200">
                        Yearly
                        <span class="ml-1 text-xs bg-green-100 text-green-600 px-2 py-1 rounded-full">Save 20%</span>
                    </button>
                </div>
            </div>
        @endif

        <!-- Pricing Cards -->
        <div class="grid grid-cols-1 md:grid-cols-{{ min(count($plans), 3) }} gap-8 max-w-6xl mx-auto">
            @foreach($plans as $plan)
                <div class="bg-white rounded-xl shadow-lg p-8 relative {{ $plan->is_popular ?? false ? 'border-2 border-primary-500 transform scale-105' : 'border border-gray-200' }}" 
                     data-aos="fade-up" data-aos-delay="{{ $loop->index * 100 }}">
                    
                    <!-- Popular Badge -->
                    @if($plan->is_popular ?? false)
                        <div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
                            <span class="bg-primary-500 text-white px-4 py-2 rounded-full text-sm font-semibold">
                                Most Popular
                            </span>
                        </div>
                    @endif

                    <!-- Plan Header -->
                    <div class="text-center mb-8">
                        <h3 class="text-2xl font-bold text-gray-900 mb-4">
                            {{ $plan->plan_name ?? $plan['name'] ?? 'Plan' }}
                        </h3>
                        
                        <!-- Price -->
                        <div class="text-4xl font-bold text-primary-600 mb-2">
                            @if(is_object($plan))
                                {{ $plan->formatted_price }}
                            @else
                                ${{ number_format($plan['price'] ?? 0, 0) }}
                            @endif
                        </div>
                        
                        <!-- Billing Period -->
                        <p class="text-gray-600">
                            @if(is_object($plan))
                                {{ $plan->billing_period_text }}
                            @else
                                {{ $plan['billing_period'] ?? 'one-time' }}
                            @endif
                        </p>
                    </div>

                    <!-- Features List -->
                    <ul class="space-y-4 mb-8">
                        @php
                            $features = is_object($plan) ? $plan->features : ($plan['features'] ?? []);
                        @endphp
                        
                        @foreach($features as $feature)
                            <li class="flex items-start space-x-3">
                                <i class="fas fa-check text-green-500 mt-1 flex-shrink-0"></i>
                                <span class="text-gray-600">{{ $feature }}</span>
                            </li>
                        @endforeach
                    </ul>

                    <!-- CTA Button -->
                    @php
                        $planName = is_object($plan) ? $plan->plan_name : ($plan['name'] ?? '');
                        $serviceSlug = $service ? $service->slug : '';
                        $ctaUrl = route('quote') . '?service=' . $serviceSlug . '&plan=' . urlencode($planName);
                    @endphp
                    
                    <a href="{{ $ctaUrl }}" 
                       class="block w-full text-center {{ ($plan->is_popular ?? $plan['is_popular'] ?? false) ? 'btn-primary' : 'btn-secondary' }}">
                        Get Started
                    </a>

                    <!-- Additional Info -->
                    @if(isset($plan['note']) || (is_object($plan) && $plan->note ?? false))
                        <p class="text-center text-sm text-gray-500 mt-4">
                            {{ is_object($plan) ? $plan->note : $plan['note'] }}
                        </p>
                    @endif
                </div>
            @endforeach
        </div>

        <!-- Additional Information -->
        <div class="text-center mt-12" data-aos="fade-up">
            <p class="text-gray-600 mb-4">
                All plans include our standard support and 30-day money-back guarantee.
            </p>
            <div class="flex flex-wrap justify-center gap-6 text-sm text-gray-500">
                <div class="flex items-center">
                    <i class="fas fa-shield-alt text-green-500 mr-2"></i>
                    <span>Secure & Reliable</span>
                </div>
                <div class="flex items-center">
                    <i class="fas fa-headset text-blue-500 mr-2"></i>
                    <span>24/7 Support</span>
                </div>
                <div class="flex items-center">
                    <i class="fas fa-undo text-purple-500 mr-2"></i>
                    <span>30-Day Guarantee</span>
                </div>
            </div>
        </div>
    </div>
</section>
