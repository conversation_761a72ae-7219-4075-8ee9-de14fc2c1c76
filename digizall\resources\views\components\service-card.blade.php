@props([
    'service',
    'featured' => false,
    'showDescription' => true,
    'showCta' => true
])

<div class="group relative bg-white rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 overflow-hidden {{ $featured ? 'ring-2 ring-primary-500' : '' }}" data-aos="fade-up">
    @if($featured)
        <div class="absolute top-4 right-4 z-10">
            <span class="bg-primary-600 text-white px-3 py-1 rounded-full text-xs font-semibold">
                Featured
            </span>
        </div>
    @endif

    <!-- Service Icon/Image -->
    <div class="relative p-8 text-center">
        @if($service->image)
            <div class="w-20 h-20 mx-auto mb-4 rounded-full overflow-hidden">
                <img src="{{ $service->image_url }}" alt="{{ $service->title }}" class="w-full h-full object-cover">
            </div>
        @elseif($service->icon)
            <div class="w-20 h-20 mx-auto mb-4 bg-primary-100 rounded-full flex items-center justify-center group-hover:bg-primary-200 transition-colors">
                <img src="{{ $service->icon_url }}" alt="{{ $service->title }}" class="w-10 h-10">
            </div>
        @else
            <div class="w-20 h-20 mx-auto mb-4 bg-primary-100 rounded-full flex items-center justify-center group-hover:bg-primary-200 transition-colors">
                <i class="fas fa-cogs text-2xl text-primary-600"></i>
            </div>
        @endif

        <!-- Service Title -->
        <h3 class="text-xl font-bold text-gray-900 mb-3 group-hover:text-primary-600 transition-colors">
            {{ $service->title }}
        </h3>

        <!-- Service Description -->
        @if($showDescription)
            <p class="text-gray-600 leading-relaxed mb-6">
                {{ $service->short_description }}
            </p>
        @endif

        <!-- CTA Button -->
        @if($showCta)
            <a href="{{ $service->url }}" class="inline-flex items-center text-primary-600 hover:text-primary-700 font-semibold transition-colors group">
                Learn More
                <i class="fas fa-arrow-right ml-2 transform group-hover:translate-x-1 transition-transform"></i>
            </a>
        @endif
    </div>

    <!-- Hover Effect Overlay -->
    <div class="absolute inset-0 bg-gradient-to-t from-primary-600 to-transparent opacity-0 group-hover:opacity-10 transition-opacity duration-300"></div>
</div>
