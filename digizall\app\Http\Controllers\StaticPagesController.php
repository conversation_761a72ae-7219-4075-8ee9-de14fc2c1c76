<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Testimonial;
use App\Models\Service;
use App\Models\Setting;
use App\Models\MetaTag;

class StaticPagesController extends Controller
{
    /**
     * About page
     */
    public function about()
    {
        $stats = [
            [
                'number' => Setting::get('projects_completed', 150),
                'label' => 'Projects Completed',
                'icon' => 'fas fa-check-circle',
                'suffix' => '+'
            ],
            [
                'number' => Setting::get('happy_clients', 120),
                'label' => 'Happy Clients',
                'icon' => 'fas fa-users',
                'suffix' => '+'
            ],
            [
                'number' => Setting::get('years_experience', 8),
                'label' => 'Years Experience',
                'icon' => 'fas fa-calendar-alt',
                'suffix' => '+'
            ],
            [
                'number' => Setting::get('team_members', 25),
                'label' => 'Team Members',
                'icon' => 'fas fa-user-friends',
                'suffix' => '+'
            ]
        ];

        $metaTags = MetaTag::getForPage('about');

        return view('static.about', compact('stats', 'metaTags'));
    }

    /**
     * Contact page
     */
    public function contact()
    {
        $metaTags = MetaTag::getForPage('contact');
        return view('static.contact', compact('metaTags'));
    }

    /**
     * Portfolio page
     */
    public function portfolio()
    {
        $metaTags = MetaTag::getForPage('portfolio');
        return view('static.portfolio', compact('metaTags'));
    }

    /**
     * Testimonials page
     */
    public function testimonials()
    {
        $testimonials = Testimonial::active()
            ->orderBy('sort_order')
            ->orderBy('created_at', 'desc')
            ->get();

        $metaTags = MetaTag::getForPage('testimonials');

        return view('static.testimonials', compact('testimonials', 'metaTags'));
    }

    /**
     * Privacy Policy page
     */
    public function privacyPolicy()
    {
        $metaTags = MetaTag::getForPage('privacy-policy');
        return view('static.privacy-policy', compact('metaTags'));
    }

    /**
     * Terms of Service page
     */
    public function termsOfService()
    {
        $metaTags = MetaTag::getForPage('terms-of-service');
        return view('static.terms-of-service', compact('metaTags'));
    }

    /**
     * Sitemap page
     */
    public function sitemap()
    {
        $services = Service::active()->orderBy('title')->get();
        $metaTags = MetaTag::getForPage('sitemap');

        return view('static.sitemap', compact('services', 'metaTags'));
    }
}
