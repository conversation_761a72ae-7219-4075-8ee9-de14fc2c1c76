<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use App\Models\Service;
use App\Models\BlogPost;
use App\Models\Testimonial;

class HomePageTest extends TestCase
{
    use RefreshDatabase;

    public function test_home_page_loads_successfully()
    {
        $response = $this->get('/');
        $response->assertStatus(200);
    }

    public function test_home_page_contains_required_sections()
    {
        $response = $this->get('/');

        $response->assertSee('DIGIZALL');
        $response->assertSee('Custom Website Design');
        $response->assertSee('OUR Services');
        $response->assertSee('Get Free Quote');
    }

    public function test_home_page_displays_featured_services()
    {
        // Create featured services
        $service = Service::factory()->create([
            'title' => 'Web Development',
            'is_featured' => true,
            'is_active' => true
        ]);

        $response = $this->get('/');
        $response->assertSee('Web Development');
    }

    public function test_home_page_displays_recent_blog_posts()
    {
        // Create a blog post
        $blogPost = BlogPost::factory()->create([
            'title' => 'Test Blog Post',
            'status' => 'published'
        ]);

        $response = $this->get('/');
        $response->assertSee('Latest Insights');
    }

    public function test_home_page_displays_testimonials()
    {
        // Create a testimonial
        $testimonial = Testimonial::factory()->create([
            'client_name' => 'John Doe',
            'testimonial' => 'Great service!',
            'is_featured' => true,
            'is_active' => true
        ]);

        $response = $this->get('/');
        $response->assertSee('What Our Clients Say');
    }

    public function test_navigation_links_are_present()
    {
        $response = $this->get('/');

        $response->assertSee('Home');
        $response->assertSee('Services');
        $response->assertSee('About');
        $response->assertSee('Blog');
        $response->assertSee('Contact');
    }

    public function test_cta_buttons_are_present()
    {
        $response = $this->get('/');

        $response->assertSee('Get Free Quote');
        $response->assertSee('View Our Work');
    }
}
