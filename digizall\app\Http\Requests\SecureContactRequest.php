<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use App\Services\SecurityService;

class SecureContactRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Check rate limiting
        if (SecurityService::checkRateLimit($this, 'contact')) {
            SecurityService::logSecurityEvent('contact_rate_limit_exceeded', [
                'ip' => $this->ip(),
                'remaining_attempts' => SecurityService::getRemainingAttempts($this, 'contact'),
            ]);
            return false;
        }

        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'name' => [
                'required',
                'string',
                'max:100',
                'regex:/^[a-zA-Z\s\-\.\']+$/',
            ],
            'email' => [
                'required',
                'email:rfc,dns',
                'max:255',
                'not_regex:/[<>"\']/',
            ],
            'phone' => [
                'nullable',
                'string',
                'max:20',
                'regex:/^[\+]?[0-9\s\-\(\)]+$/',
            ],
            'subject' => [
                'required',
                'string',
                'max:200',
                'not_regex:/<script|javascript:|data:|vbscript:/i',
            ],
            'message' => [
                'required',
                'string',
                'min:10',
                'max:2000',
                'not_regex:/<script|javascript:|data:|vbscript:/i',
            ],
            'preferred_contact' => [
                'required',
                'string',
                'in:email,phone',
            ],
            'g-recaptcha-response' => [
                'nullable',
                'string',
            ],
        ];
    }

    /**
     * Get custom validation messages
     */
    public function messages(): array
    {
        return [
            'name.regex' => 'Name can only contain letters, spaces, hyphens, dots, and apostrophes.',
            'email.not_regex' => 'Email contains invalid characters.',
            'phone.regex' => 'Phone number format is invalid.',
            'subject.not_regex' => 'Subject contains invalid characters.',
            'message.not_regex' => 'Message contains invalid characters.',
        ];
    }

    /**
     * Prepare the data for validation
     */
    protected function prepareForValidation(): void
    {
        // Sanitize input data
        $sanitized = SecurityService::sanitizeInput($this->all());
        $this->replace($sanitized);

        // Hit rate limiter
        SecurityService::hitRateLimit($this, 'contact');
    }

    /**
     * Handle a failed authorization attempt
     */
    protected function failedAuthorization()
    {
        if (SecurityService::checkRateLimit($this, 'contact')) {
            $remaining = SecurityService::getRemainingAttempts($this, 'contact');
            abort(429, "Too many contact requests. Please try again later. Remaining attempts: {$remaining}");
        }

        parent::failedAuthorization();
    }
}
