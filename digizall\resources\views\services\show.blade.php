@extends('layouts.app')

@section('title', $metaTags->meta_title ?? $service->meta_title ?? $service->title . ' - DIGIZALL')
@section('meta_description', $metaTags->meta_description ?? $service->meta_description ?? $service->short_description)
@section('meta_keywords', $metaTags->meta_keywords ?? $service->title . ', ' . $service->category . ', DIGIZALL')

@if($metaTags)
    @section('og_title', $metaTags->og_title)
    @section('og_description', $metaTags->og_description)
    @section('og_image', $metaTags->og_image_url)
@endif

@section('content')

<!-- Page Header -->
<section class="bg-gradient-to-r from-primary-600 to-secondary-600 text-white py-20">
    <div class="container-custom">
        <div class="text-center" data-aos="fade-up">
            <!-- Breadcrumb -->
            <nav class="mb-6">
                <ol class="flex items-center justify-center space-x-2 text-sm">
                    <li><a href="{{ route('home') }}" class="text-gray-200 hover:text-white">Home</a></li>
                    <li><i class="fas fa-chevron-right text-gray-400 mx-2"></i></li>
                    <li><a href="{{ route('services.index') }}" class="text-gray-200 hover:text-white">Services</a></li>
                    <li><i class="fas fa-chevron-right text-gray-400 mx-2"></i></li>
                    <li class="text-white">{{ $service->title }}</li>
                </ol>
            </nav>

            <h1 class="heading-primary text-white mb-4">
                {{ $service->title }}
            </h1>
            <p class="text-xl text-gray-200 max-w-2xl mx-auto">
                {{ $service->short_description }}
            </p>

            @if($service->category)
                <div class="mt-6">
                    <span class="inline-block px-4 py-2 bg-white bg-opacity-20 rounded-full text-sm font-medium">
                        {{ $service->category }}
                    </span>
                </div>
            @endif
        </div>
    </div>
</section>

<!-- Service Overview -->
<section class="section-padding">
    <div class="container-custom">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-12">
            <!-- Main Content -->
            <div class="lg:col-span-2">
                <!-- Service Description -->
                <div class="mb-12" data-aos="fade-up">
                    <h2 class="text-3xl font-bold text-gray-900 mb-6">Service Overview</h2>
                    <div class="prose prose-lg max-w-none text-gray-600">
                        {!! nl2br(e($service->description)) !!}
                    </div>
                </div>

                <!-- Service Features -->
                @if($service->features->count() > 0)
                    <div class="mb-12" data-aos="fade-up">
                        <h2 class="text-3xl font-bold text-gray-900 mb-8">What's Included</h2>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            @foreach($service->features as $feature)
                                <div class="flex items-start space-x-4">
                                    @if($feature->icon)
                                        <div class="flex-shrink-0 w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
                                            <img src="{{ $feature->icon_url }}" alt="{{ $feature->title }}" class="w-6 h-6">
                                        </div>
                                    @else
                                        <div class="flex-shrink-0 w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
                                            <i class="fas fa-check text-primary-600"></i>
                                        </div>
                                    @endif
                                    <div>
                                        <h3 class="font-semibold text-gray-900 mb-2">{{ $feature->title }}</h3>
                                        @if($feature->description)
                                            <p class="text-gray-600 text-sm">{{ $feature->description }}</p>
                                        @endif
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                @endif

                <!-- Process Steps -->
                @if($service->process_steps && count($service->process_steps) > 0)
                    <div class="mb-12" data-aos="fade-up">
                        <h2 class="text-3xl font-bold text-gray-900 mb-8">Our Process</h2>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                            @foreach($service->process_steps as $index => $step)
                                <div class="text-center">
                                    <div class="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                        <span class="text-primary-600 font-bold text-xl">{{ $index + 1 }}</span>
                                    </div>
                                    <h3 class="font-semibold text-gray-900 mb-2">{{ $step['title'] ?? 'Step ' . ($index + 1) }}</h3>
                                    <p class="text-gray-600 text-sm">{{ $step['description'] ?? '' }}</p>
                                </div>
                            @endforeach
                        </div>
                    </div>
                @endif
            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-1">
                <!-- Quick Contact -->
                <div class="bg-gray-50 rounded-xl p-6 mb-8" data-aos="fade-up">
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Get Started Today</h3>
                    <p class="text-gray-600 mb-6">Ready to discuss your project? Contact us for a free consultation.</p>
                    <div class="space-y-3">
                        <a href="{{ route('quote') }}" class="block w-full text-center btn-primary">
                            Get Free Quote
                        </a>
                        <a href="{{ route('contact') }}" class="block w-full text-center btn-secondary">
                            Contact Us
                        </a>
                    </div>
                </div>

                <!-- Service Info -->
                <div class="bg-white border border-gray-200 rounded-xl p-6" data-aos="fade-up" data-aos-delay="100">
                    <h3 class="text-lg font-bold text-gray-900 mb-4">Service Details</h3>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Category:</span>
                            <span class="font-medium text-gray-900">{{ $service->category }}</span>
                        </div>
                        @if($service->pricing->count() > 0)
                            <div class="flex justify-between">
                                <span class="text-gray-600">Starting From:</span>
                                <span class="font-medium text-primary-600">${{ number_format($service->pricing->min('price'), 0) }}</span>
                            </div>
                        @endif
                        <div class="flex justify-between">
                            <span class="text-gray-600">Delivery:</span>
                            <span class="font-medium text-gray-900">Custom Timeline</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Pricing Plans -->
@if($service->pricing->count() > 0)
    <section class="section-padding bg-gray-50">
        <div class="container-custom">
            <div class="text-center mb-16" data-aos="fade-up">
                <h2 class="heading-secondary mb-4">Choose Your Plan</h2>
                <p class="text-body max-w-2xl mx-auto">
                    Select the perfect plan for your {{ $service->title }} needs. All plans include our standard support and satisfaction guarantee.
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-{{ min($service->pricing->count(), 3) }} gap-8 max-w-6xl mx-auto">
                @foreach($service->pricing as $plan)
                    <div class="bg-white rounded-xl shadow-lg p-8 relative {{ $plan->is_popular ? 'border-2 border-primary-500 transform scale-105' : 'border border-gray-200' }}" data-aos="fade-up" data-aos-delay="{{ $loop->index * 100 }}">
                        @if($plan->is_popular)
                            <div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
                                <span class="bg-primary-500 text-white px-4 py-2 rounded-full text-sm font-semibold">
                                    Most Popular
                                </span>
                            </div>
                        @endif

                        <div class="text-center mb-8">
                            <h3 class="text-2xl font-bold text-gray-900 mb-4">{{ $plan->plan_name }}</h3>
                            <div class="text-4xl font-bold text-primary-600 mb-2">
                                {{ $plan->formatted_price }}
                            </div>
                            <p class="text-gray-600">{{ $plan->billing_period_text }}</p>
                        </div>

                        <ul class="space-y-4 mb-8">
                            @foreach($plan->features as $feature)
                                <li class="flex items-start space-x-3">
                                    <i class="fas fa-check text-green-500 mt-1 flex-shrink-0"></i>
                                    <span class="text-gray-600">{{ $feature }}</span>
                                </li>
                            @endforeach
                        </ul>

                        <a href="{{ route('quote') }}?service={{ $service->slug }}&plan={{ $plan->plan_name }}"
                           class="block w-full text-center {{ $plan->is_popular ? 'btn-primary' : 'btn-secondary' }}">
                            Get Started
                        </a>
                    </div>
                @endforeach
            </div>
        </div>
    </section>
@endif

<!-- FAQ Section -->
@if($service->faq && count($service->faq) > 0)
    <section class="section-padding">
        <div class="container-custom">
            <div class="max-w-4xl mx-auto">
                <div class="text-center mb-16" data-aos="fade-up">
                    <h2 class="heading-secondary mb-4">Frequently Asked Questions</h2>
                    <p class="text-body">
                        Get answers to common questions about our {{ $service->title }} service.
                    </p>
                </div>

                <div class="space-y-6" x-data="{ openFaq: null }">
                    @foreach($service->faq as $index => $faq)
                        <div class="bg-white border border-gray-200 rounded-lg" data-aos="fade-up" data-aos-delay="{{ $index * 100 }}">
                            <button @click="openFaq = openFaq === {{ $index }} ? null : {{ $index }}"
                                    class="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors">
                                <h3 class="font-semibold text-gray-900">{{ $faq['question'] ?? '' }}</h3>
                                <i class="fas fa-chevron-down transform transition-transform"
                                   :class="{ 'rotate-180': openFaq === {{ $index }} }"></i>
                            </button>
                            <div x-show="openFaq === {{ $index }}"
                                 x-transition:enter="transition ease-out duration-200"
                                 x-transition:enter-start="opacity-0 transform -translate-y-2"
                                 x-transition:enter-end="opacity-100 transform translate-y-0"
                                 class="px-6 pb-4">
                                <p class="text-gray-600">{{ $faq['answer'] ?? '' }}</p>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    </section>
@endif
            </div>
        </div>
    </div>
</section>

<!-- Related Services -->
@if($relatedServices->count() > 0)
    <section class="section-padding bg-gray-50">
        <div class="container-custom">
            <div class="text-center mb-16" data-aos="fade-up">
                <h2 class="heading-secondary mb-4">Related Services</h2>
                <p class="text-body">
                    Explore other services that complement {{ $service->title }}.
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                @foreach($relatedServices as $relatedService)
                    <div data-aos="fade-up" data-aos-delay="{{ $loop->index * 100 }}">
                        <x-service-card :service="$relatedService" />
                    </div>
                @endforeach
            </div>
        </div>
    </section>
@endif

<!-- CTA Section -->
<section class="section-padding {{ $relatedServices->count() > 0 ? '' : 'bg-gray-50' }}">
    <div class="container-custom">
        <div class="bg-gradient-to-r from-primary-600 to-secondary-600 rounded-2xl p-12 text-center text-white" data-aos="fade-up">
            <h2 class="heading-secondary text-white mb-6">
                Ready to Get Started with {{ $service->title }}?
            </h2>
            <p class="text-xl text-gray-200 mb-8 max-w-2xl mx-auto">
                Let's discuss your project requirements and create a customized solution that delivers results.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="{{ route('quote') }}?service={{ $service->slug }}" class="btn-primary bg-white text-primary-600 hover:bg-gray-100 px-8 py-4 text-lg font-semibold">
                    Get Free Quote
                    <i class="fas fa-arrow-right ml-2"></i>
                </a>
                <a href="{{ route('contact') }}" class="btn-secondary border-2 border-white text-white hover:bg-white hover:text-primary-600 px-8 py-4 text-lg font-semibold">
                    Contact Us
                </a>
            </div>
        </div>
    </div>
</section>

@endsection
