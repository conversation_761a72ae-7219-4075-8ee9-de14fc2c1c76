<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>@yield('title', 'Admin Panel') - DIGIZALL</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Styles -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])

    <style>
        .sidebar-link.active {
            background-color: #3b82f6;
            color: white;
        }
        .sidebar-link:hover {
            background-color: #e5e7eb;
        }
        .sidebar-link.active:hover {
            background-color: #2563eb;
        }
    </style>

    @stack('styles')
</head>
<body class="bg-gray-100">
    <div class="min-h-screen flex">
        <!-- Sidebar -->
        <div class="w-64 bg-white shadow-lg">
            <!-- Logo -->
            <div class="p-6 border-b border-gray-200">
                <h1 class="text-2xl font-bold text-primary-600">DIGIZALL</h1>
                <p class="text-sm text-gray-500">Admin Panel</p>
            </div>

            <!-- Navigation -->
            <nav class="mt-6">
                <div class="px-4">
                    <a href="{{ route('admin.dashboard') }}" 
                       class="sidebar-link flex items-center px-4 py-3 text-gray-700 rounded-lg mb-2 {{ request()->routeIs('admin.dashboard*') ? 'active' : '' }}">
                        <i class="fas fa-tachometer-alt mr-3"></i>
                        Dashboard
                    </a>

                    <a href="{{ route('admin.services.index') }}" 
                       class="sidebar-link flex items-center px-4 py-3 text-gray-700 rounded-lg mb-2 {{ request()->routeIs('admin.services*') ? 'active' : '' }}">
                        <i class="fas fa-cogs mr-3"></i>
                        Services
                    </a>

                    <a href="{{ route('admin.blog.index') }}" 
                       class="sidebar-link flex items-center px-4 py-3 text-gray-700 rounded-lg mb-2 {{ request()->routeIs('admin.blog*') ? 'active' : '' }}">
                        <i class="fas fa-blog mr-3"></i>
                        Blog Posts
                    </a>

                    <a href="{{ route('admin.quotes.index') }}" 
                       class="sidebar-link flex items-center px-4 py-3 text-gray-700 rounded-lg mb-2 {{ request()->routeIs('admin.quotes*') ? 'active' : '' }}">
                        <i class="fas fa-quote-left mr-3"></i>
                        Quotes
                        @php
                            $pendingQuotes = \App\Models\QuoteRequest::where('status', 'pending')->count();
                        @endphp
                        @if($pendingQuotes > 0)
                            <span class="ml-auto bg-red-500 text-white text-xs px-2 py-1 rounded-full">{{ $pendingQuotes }}</span>
                        @endif
                    </a>

                    <a href="{{ route('admin.contacts.index') }}" 
                       class="sidebar-link flex items-center px-4 py-3 text-gray-700 rounded-lg mb-2 {{ request()->routeIs('admin.contacts*') ? 'active' : '' }}">
                        <i class="fas fa-envelope mr-3"></i>
                        Contacts
                        @php
                            $pendingContacts = \App\Models\ContactMessage::where('status', 'pending')->count();
                        @endphp
                        @if($pendingContacts > 0)
                            <span class="ml-auto bg-red-500 text-white text-xs px-2 py-1 rounded-full">{{ $pendingContacts }}</span>
                        @endif
                    </a>
                </div>

                <!-- System Section -->
                <div class="px-4 mt-8">
                    <h3 class="text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3">System</h3>

                    <a href="{{ route('admin.performance.index') }}"
                       class="sidebar-link flex items-center px-4 py-3 text-gray-700 rounded-lg mb-2 {{ request()->routeIs('admin.performance.*') ? 'active' : '' }}">
                        <i class="fas fa-tachometer-alt mr-3"></i>
                        Performance
                    </a>

                    <a href="{{ route('admin.assets.index') }}"
                       class="sidebar-link flex items-center px-4 py-3 text-gray-700 rounded-lg mb-2 {{ request()->routeIs('admin.assets*') ? 'active' : '' }}">
                        <i class="fas fa-file-code mr-3"></i>
                        Assets
                    </a>

                    <a href="{{ route('home') }}" target="_blank"
                       class="sidebar-link flex items-center px-4 py-3 text-gray-700 rounded-lg mb-2">
                        <i class="fas fa-external-link-alt mr-3"></i>
                        View Website
                    </a>

                    <form method="POST" action="{{ route('logout') }}" class="inline">
                        @csrf
                        <button type="submit" class="sidebar-link flex items-center px-4 py-3 text-gray-700 rounded-lg mb-2 w-full text-left">
                            <i class="fas fa-sign-out-alt mr-3"></i>
                            Logout
                        </button>
                    </form>
                </div>
            </nav>
        </div>

        <!-- Main Content -->
        <div class="flex-1 flex flex-col">
            <!-- Top Bar -->
            <header class="bg-white shadow-sm border-b border-gray-200">
                <div class="px-6 py-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <h2 class="text-2xl font-semibold text-gray-800">@yield('page-title', 'Dashboard')</h2>
                            @if(isset($breadcrumbs))
                                <nav class="text-sm text-gray-500 mt-1">
                                    @foreach($breadcrumbs as $breadcrumb)
                                        @if(!$loop->last)
                                            <a href="{{ $breadcrumb['url'] }}" class="hover:text-gray-700">{{ $breadcrumb['title'] }}</a>
                                            <span class="mx-2">/</span>
                                        @else
                                            <span>{{ $breadcrumb['title'] }}</span>
                                        @endif
                                    @endforeach
                                </nav>
                            @endif
                        </div>
                        
                        <div class="flex items-center space-x-4">
                            <div class="text-sm text-gray-600">
                                Welcome, <strong>{{ auth()->user()->name }}</strong>
                            </div>
                            <div class="w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center text-white font-semibold">
                                {{ substr(auth()->user()->name, 0, 1) }}
                            </div>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Page Content -->
            <main class="flex-1 p-6">
                <!-- Flash Messages -->
                @if(session('success'))
                    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6" role="alert">
                        <div class="flex items-center">
                            <i class="fas fa-check-circle mr-2"></i>
                            <span>{{ session('success') }}</span>
                        </div>
                    </div>
                @endif

                @if(session('error'))
                    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6" role="alert">
                        <div class="flex items-center">
                            <i class="fas fa-exclamation-circle mr-2"></i>
                            <span>{{ session('error') }}</span>
                        </div>
                    </div>
                @endif

                @if(session('warning'))
                    <div class="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-6" role="alert">
                        <div class="flex items-center">
                            <i class="fas fa-exclamation-triangle mr-2"></i>
                            <span>{{ session('warning') }}</span>
                        </div>
                    </div>
                @endif

                @if(session('info'))
                    <div class="bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded mb-6" role="alert">
                        <div class="flex items-center">
                            <i class="fas fa-info-circle mr-2"></i>
                            <span>{{ session('info') }}</span>
                        </div>
                    </div>
                @endif

                @yield('content')
            </main>
        </div>
    </div>

    @stack('scripts')
</body>
</html>
