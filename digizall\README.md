# DIGIZALL - Your Digital Success Partner

A comprehensive digital solutions website built with Laravel 10, featuring a modern frontend, admin panel, and employee attendance portal.

## 🚀 Features

### Frontend Website
- **Modern Responsive Design** - Mobile-first approach with Tailwind CSS
- **Service Showcase** - Comprehensive service pages with pricing and features
- **Blog System** - Full-featured blog with categories, tags, and search
- **Quote Request System** - Advanced quote forms with validation
- **Contact Forms** - Secure contact forms with spam protection
- **Testimonials** - Client testimonials with ratings
- **SEO Optimized** - Complete SEO implementation with meta tags and structured data

### Admin Panel
- **Dashboard** - Comprehensive admin dashboard with statistics
- **Quote Management** - Manage quote requests with status tracking
- **Contact Management** - Handle contact messages and inquiries
- **Blog Management** - Create and manage blog posts and categories
- **Service Management** - Manage services, features, and pricing
- **User Management** - Role-based user management system
- **Performance Monitoring** - Real-time performance metrics and optimization
- **Security Features** - Advanced security with rate limiting and threat detection

### Attendance Portal (app.digizall.com)
- **Employee Dashboard** - Personal attendance tracking
- **Check-in/Check-out** - GPS-based attendance logging
- **Attendance History** - Detailed attendance reports
- **Leave Management** - Leave request system
- **HR Dashboard** - Comprehensive HR management tools
- **Department Management** - Organize employees by departments

## 🛠 Technology Stack

- **Backend**: Laravel 10.x
- **Frontend**: Alpine.js, Tailwind CSS
- **Database**: MySQL
- **Authentication**: Laravel's built-in authentication
- **Caching**: Redis/File-based caching
- **Asset Compilation**: Vite
- **Image Processing**: Intervention Image
- **Email**: Laravel Mail with queue support

## 📋 Requirements

- PHP 8.1 or higher
- Composer
- Node.js 16+ and npm
- MySQL 5.7+ or MariaDB 10.3+
- Redis (optional, for caching)

## 🚀 Installation

### 1. Clone the Repository
```bash
git clone https://github.com/your-username/digizall.git
cd digizall
```

### 2. Install Dependencies
```bash
# Install PHP dependencies
composer install

# Install Node.js dependencies
npm install
```

### 3. Environment Configuration
```bash
# Copy environment file
cp .env.example .env

# Generate application key
php artisan key:generate
```

### 4. Database Setup
```bash
# Configure your database in .env file
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=digizall
DB_USERNAME=your_username
DB_PASSWORD=your_password

# Run migrations
php artisan migrate

# Seed the database (optional)
php artisan db:seed
```

### 5. Build Assets
```bash
# Development
npm run dev

# Production
npm run build
```

### 6. Storage Setup
```bash
# Create storage link
php artisan storage:link

# Set permissions (Linux/Mac)
chmod -R 775 storage bootstrap/cache
```

## ⚙️ Configuration

### Environment Variables

#### Basic Configuration
```env
APP_NAME="DIGIZALL"
APP_ENV=production
APP_KEY=base64:your-app-key
APP_DEBUG=false
APP_URL=https://yourdomain.com
```

#### Database Configuration
```env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=digizall
DB_USERNAME=your_username
DB_PASSWORD=your_password
```

#### Mail Configuration
```env
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="DIGIZALL"
```

#### Cache Configuration
```env
CACHE_DRIVER=redis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379
```

### Admin User Setup
```bash
# Create admin user
php artisan tinker
>>> $user = new App\Models\User();
>>> $user->name = 'Admin User';
>>> $user->email = '<EMAIL>';
>>> $user->password = bcrypt('secure-password');
>>> $user->is_admin = true;
>>> $user->save();
```

## 🔧 Artisan Commands

### Performance Optimization
```bash
# Complete performance optimization
php artisan performance:optimize --all

# Cache optimization only
php artisan performance:optimize --cache

# Database optimization only
php artisan performance:optimize --database

# Image optimization only
php artisan performance:optimize --images

# Generate performance report
php artisan performance:optimize --report
```

### Asset Optimization
```bash
# Complete asset optimization
php artisan assets:optimize

# CSS optimization only
php artisan assets:optimize --css

# JavaScript optimization only
php artisan assets:optimize --js

# Image optimization only
php artisan assets:optimize --images

# Generate WebP images
php artisan assets:optimize --webp

# Show optimization statistics
php artisan assets:optimize --stats
```

### Cache Management
```bash
# Clear all caches
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear

# Optimize for production
php artisan config:cache
php artisan route:cache
php artisan view:cache
```

## 🚀 Deployment

### Shared Hosting Deployment

#### 1. Prepare Files
```bash
# Build production assets
npm run build

# Optimize for production
php artisan config:cache
php artisan route:cache
php artisan view:cache
```

#### 2. Upload Files
- Upload all files to your hosting account
- Move `public` folder contents to your domain's public folder
- Update `index.php` paths to point to the Laravel installation

#### 3. Set Permissions
```bash
chmod -R 755 storage bootstrap/cache
```

#### 4. Environment Setup
- Upload `.env` file with production settings
- Ensure database credentials are correct
- Set `APP_ENV=production` and `APP_DEBUG=false`

## 🧪 Testing

### Run Tests
```bash
# Run all tests
php artisan test

# Run specific test suite
php artisan test --testsuite=Feature

# Run with coverage
php artisan test --coverage
```

### Test Categories
- **Feature Tests**: End-to-end functionality testing
- **Security Tests**: Security vulnerability testing
- **Performance Tests**: Performance and optimization testing
- **Admin Panel Tests**: Admin functionality testing

## 📊 Performance Monitoring

### Built-in Monitoring
- Real-time performance metrics
- Database query optimization
- Cache hit rate monitoring
- Memory usage tracking
- Response time analysis

### Access Performance Dashboard
Visit `/admin/performance` to view:
- Performance trends
- Slow query analysis
- System resource usage
- Optimization recommendations

## 🔒 Security Features

### Implemented Security Measures
- **CSRF Protection**: All forms protected against CSRF attacks
- **XSS Prevention**: Input sanitization and output escaping
- **SQL Injection Protection**: Parameterized queries and validation
- **Rate Limiting**: API and form submission rate limiting
- **Security Headers**: Comprehensive security headers
- **Input Validation**: Strict input validation and sanitization
- **File Upload Security**: Secure file upload handling

## 📈 SEO Features

### Implemented SEO
- **Meta Tags**: Dynamic meta titles and descriptions
- **Open Graph**: Social media optimization
- **Structured Data**: JSON-LD structured data
- **Sitemap**: Automatic XML sitemap generation
- **Robots.txt**: Search engine directives
- **Canonical URLs**: Duplicate content prevention
- **Page Speed**: Optimized loading times

## 🎨 Customization

### Styling
- Modify `resources/css/app.css` for custom styles
- Update Tailwind configuration in `tailwind.config.js`
- Customize components in `resources/views/components/`

### Content Management
- Update site settings through admin panel
- Manage services, blog posts, and testimonials
- Customize email templates in `resources/views/emails/`

## 🐛 Troubleshooting

### Common Issues

#### Permission Errors
```bash
sudo chown -R www-data:www-data storage bootstrap/cache
chmod -R 775 storage bootstrap/cache
```

#### Cache Issues
```bash
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear
```

#### Database Connection Issues
- Verify database credentials in `.env`
- Ensure database server is running
- Check firewall settings

## 📞 Support

For support and questions:
- **Email**: <EMAIL>
- **Documentation**: Check this README and inline code comments
- **Issues**: Create GitHub issues for bugs and feature requests

## 📄 License

This project is proprietary software. All rights reserved.

---

**DIGIZALL** - Transforming businesses through innovative digital solutions.
