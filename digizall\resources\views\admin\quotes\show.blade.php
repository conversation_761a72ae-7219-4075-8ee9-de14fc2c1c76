@extends('layouts.admin')

@section('title', 'Quote Details')
@section('page-title', 'Quote Details')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-2xl font-semibold text-gray-900">Quote #{{ $quote->id }}</h1>
            <p class="text-gray-600">Submitted {{ $quote->created_at->format('F d, Y \a\t g:i A') }}</p>
        </div>
        <div class="flex space-x-3">
            <a href="{{ route('admin.quotes.index') }}" 
               class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Quotes
            </a>
            <a href="mailto:{{ $quote->email }}?subject=Re: Your Quote Request - {{ ucwords(str_replace('-', ' ', $quote->service)) }}" 
               class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-blue-700">
                <i class="fas fa-envelope mr-2"></i>
                Reply via Email
            </a>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Client Information -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Client Information</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-500">Name</label>
                        <p class="mt-1 text-sm text-gray-900">{{ $quote->name }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-500">Email</label>
                        <p class="mt-1 text-sm text-gray-900">
                            <a href="mailto:{{ $quote->email }}" class="text-blue-600 hover:text-blue-500">{{ $quote->email }}</a>
                        </p>
                    </div>
                    @if($quote->phone)
                        <div>
                            <label class="block text-sm font-medium text-gray-500">Phone</label>
                            <p class="mt-1 text-sm text-gray-900">
                                <a href="tel:{{ $quote->phone }}" class="text-blue-600 hover:text-blue-500">{{ $quote->phone }}</a>
                            </p>
                        </div>
                    @endif
                    @if($quote->company)
                        <div>
                            <label class="block text-sm font-medium text-gray-500">Company</label>
                            <p class="mt-1 text-sm text-gray-900">{{ $quote->company }}</p>
                        </div>
                    @endif
                    @if($quote->website)
                        <div>
                            <label class="block text-sm font-medium text-gray-500">Website</label>
                            <p class="mt-1 text-sm text-gray-900">
                                <a href="{{ $quote->website }}" target="_blank" class="text-blue-600 hover:text-blue-500">{{ $quote->website }}</a>
                            </p>
                        </div>
                    @endif
                    <div>
                        <label class="block text-sm font-medium text-gray-500">Preferred Contact</label>
                        <p class="mt-1 text-sm text-gray-900">{{ ucfirst($quote->preferred_contact) }}</p>
                    </div>
                </div>
            </div>

            <!-- Project Details -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Project Details</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-500">Service</label>
                        <p class="mt-1 text-sm text-gray-900">{{ ucwords(str_replace('-', ' ', $quote->service)) }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-500">Project Type</label>
                        <p class="mt-1 text-sm text-gray-900">{{ ucwords(str_replace('-', ' ', $quote->project_type)) }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-500">Budget Range</label>
                        <p class="mt-1 text-sm text-gray-900">{{ ucwords(str_replace('-', ' ', $quote->budget)) }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-500">Timeline</label>
                        <p class="mt-1 text-sm text-gray-900">{{ ucwords(str_replace('-', ' ', $quote->timeline)) }}</p>
                    </div>
                </div>

                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-500 mb-2">Project Description</label>
                    <div class="bg-gray-50 rounded-lg p-4">
                        <p class="text-sm text-gray-900 whitespace-pre-wrap">{{ $quote->description }}</p>
                    </div>
                </div>

                @if($quote->features)
                    @php
                        $features = json_decode($quote->features, true) ?? [];
                    @endphp
                    @if(count($features) > 0)
                        <div class="mb-6">
                            <label class="block text-sm font-medium text-gray-500 mb-2">Required Features</label>
                            <div class="bg-gray-50 rounded-lg p-4">
                                <ul class="list-disc list-inside space-y-1">
                                    @foreach($features as $feature)
                                        <li class="text-sm text-gray-900">{{ ucwords(str_replace('-', ' ', $feature)) }}</li>
                                    @endforeach
                                </ul>
                            </div>
                        </div>
                    @endif
                @endif

                @if($quote->additional_info)
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-2">Additional Information</label>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <p class="text-sm text-gray-900 whitespace-pre-wrap">{{ $quote->additional_info }}</p>
                        </div>
                    </div>
                @endif
            </div>

            <!-- Technical Information -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Technical Information</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-500">IP Address</label>
                        <p class="mt-1 text-sm text-gray-900 font-mono">{{ $quote->ip_address }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-500">Marketing Consent</label>
                        <p class="mt-1 text-sm text-gray-900">{{ $quote->marketing_consent ? 'Yes' : 'No' }}</p>
                    </div>
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-500">User Agent</label>
                        <p class="mt-1 text-xs text-gray-900 font-mono break-all">{{ $quote->user_agent }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Status Management -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Status Management</h3>
                
                <!-- Current Status -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-500 mb-2">Current Status</label>
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                        {{ $quote->status === 'pending' ? 'bg-yellow-100 text-yellow-800' : 
                           ($quote->status === 'in_progress' ? 'bg-blue-100 text-blue-800' :
                           ($quote->status === 'quoted' ? 'bg-purple-100 text-purple-800' :
                           ($quote->status === 'accepted' ? 'bg-green-100 text-green-800' : 
                           ($quote->status === 'rejected' ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-800')))) }}">
                        {{ ucfirst(str_replace('_', ' ', $quote->status)) }}
                    </span>
                </div>

                <!-- Update Status Form -->
                <form method="POST" action="{{ route('admin.quotes.status', $quote) }}" class="space-y-4">
                    @csrf
                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Update Status</label>
                        <select name="status" id="status" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                            <option value="pending" {{ $quote->status === 'pending' ? 'selected' : '' }}>Pending</option>
                            <option value="in_progress" {{ $quote->status === 'in_progress' ? 'selected' : '' }}>In Progress</option>
                            <option value="quoted" {{ $quote->status === 'quoted' ? 'selected' : '' }}>Quoted</option>
                            <option value="accepted" {{ $quote->status === 'accepted' ? 'selected' : '' }}>Accepted</option>
                            <option value="rejected" {{ $quote->status === 'rejected' ? 'selected' : '' }}>Rejected</option>
                            <option value="completed" {{ $quote->status === 'completed' ? 'selected' : '' }}>Completed</option>
                        </select>
                    </div>
                    <div>
                        <label for="notes" class="block text-sm font-medium text-gray-700 mb-1">Notes (optional)</label>
                        <textarea name="notes" id="notes" rows="3" 
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                                  placeholder="Add notes about this status change..."></textarea>
                    </div>
                    <button type="submit" class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700">
                        Update Status
                    </button>
                </form>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
                <div class="space-y-3">
                    <a href="mailto:{{ $quote->email }}?subject=Re: Your Quote Request - {{ ucwords(str_replace('-', ' ', $quote->service)) }}" 
                       class="w-full inline-flex items-center justify-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-medium text-white hover:bg-blue-700">
                        <i class="fas fa-envelope mr-2"></i>
                        Send Email
                    </a>
                    @if($quote->phone)
                        <a href="tel:{{ $quote->phone }}" 
                           class="w-full inline-flex items-center justify-center px-4 py-2 bg-green-600 border border-transparent rounded-md font-medium text-white hover:bg-green-700">
                            <i class="fas fa-phone mr-2"></i>
                            Call Client
                        </a>
                    @endif
                    @if($quote->preferred_contact === 'whatsapp' && $quote->phone)
                        <a href="https://wa.me/{{ str_replace(['+', '-', ' '], '', $quote->phone) }}" target="_blank"
                           class="w-full inline-flex items-center justify-center px-4 py-2 bg-green-500 border border-transparent rounded-md font-medium text-white hover:bg-green-600">
                            <i class="fab fa-whatsapp mr-2"></i>
                            WhatsApp
                        </a>
                    @endif
                </div>
            </div>

            <!-- Admin Notes -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Admin Notes</h3>
                
                @if($quote->admin_notes)
                    <div class="bg-gray-50 rounded-lg p-4 mb-4">
                        <p class="text-sm text-gray-900 whitespace-pre-wrap">{{ $quote->admin_notes }}</p>
                    </div>
                @endif

                <!-- Add Notes Form -->
                <form method="POST" action="{{ route('admin.quotes.notes', $quote) }}" class="space-y-4">
                    @csrf
                    <div>
                        <label for="add_notes" class="block text-sm font-medium text-gray-700 mb-1">Add Notes</label>
                        <textarea name="notes" id="add_notes" rows="3" 
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                                  placeholder="Add internal notes about this quote..."></textarea>
                    </div>
                    <button type="submit" class="w-full bg-gray-600 text-white py-2 px-4 rounded-md hover:bg-gray-700">
                        Add Notes
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection
