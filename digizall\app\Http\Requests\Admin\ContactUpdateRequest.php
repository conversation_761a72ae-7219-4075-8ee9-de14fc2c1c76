<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class ContactUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check() && auth()->user()->isAdmin();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'status' => 'required|in:pending,read,replied,resolved',
            'admin_notes' => 'nullable|string|max:2000',
            'priority' => 'nullable|in:low,normal,high,urgent',
            'assigned_to' => 'nullable|exists:users,id',
            'follow_up_date' => 'nullable|date|after_or_equal:today',
            'internal_tags' => 'nullable|array',
            'internal_tags.*' => 'string|max:50',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'admin_notes' => 'admin notes',
            'assigned_to' => 'assigned user',
            'follow_up_date' => 'follow-up date',
            'internal_tags' => 'internal tags',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'status.required' => 'Please select a status.',
            'status.in' => 'The selected status is invalid.',
            'admin_notes.max' => 'Admin notes may not be greater than 2000 characters.',
            'priority.in' => 'The selected priority is invalid.',
            'assigned_to.exists' => 'The selected user is invalid.',
            'follow_up_date.date' => 'The follow-up date must be a valid date.',
            'follow_up_date.after_or_equal' => 'The follow-up date must be today or in the future.',
            'internal_tags.array' => 'Internal tags must be an array.',
            'internal_tags.*.string' => 'Each internal tag must be a string.',
            'internal_tags.*.max' => 'Each internal tag may not be greater than 50 characters.',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Clean up internal tags
        if ($this->has('internal_tags') && is_array($this->internal_tags)) {
            $cleanTags = array_filter(
                array_map('trim', $this->internal_tags),
                function ($tag) {
                    return !empty($tag);
                }
            );

            $this->merge([
                'internal_tags' => array_values($cleanTags)
            ]);
        }

        // Set default priority if not provided
        if (empty($this->priority)) {
            $this->merge([
                'priority' => 'normal'
            ]);
        }
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Auto-set replied_at when status is changed to replied
            if ($this->status === 'replied' && !$this->route('contact')->replied_at) {
                $this->merge([
                    'replied_at' => now()
                ]);
            }

            // Validate that high priority contacts have follow-up dates
            if ($this->priority === 'high' || $this->priority === 'urgent') {
                if (empty($this->follow_up_date)) {
                    $validator->warnings()->add('follow_up_date', 'Consider setting a follow-up date for high priority contacts.');
                }
            }

            // Validate admin notes for certain status changes
            if (in_array($this->status, ['replied', 'resolved']) && empty($this->admin_notes)) {
                $validator->warnings()->add('admin_notes', 'Consider adding notes when marking as replied or resolved.');
            }
        });
    }
}
