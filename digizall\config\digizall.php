<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Application Name
    |--------------------------------------------------------------------------
    |
    | This value is the name of your application. This value is used when the
    | framework needs to place the application's name in a notification or
    | any other location as required by the application or its packages.
    |
    */

    'name' => env('APP_NAME', 'DIGIZALL'),

    /*
    |--------------------------------------------------------------------------
    | Application Tagline
    |--------------------------------------------------------------------------
    |
    | The tagline for your application that appears in various places
    | throughout the application.
    |
    */

    'tagline' => env('APP_TAGLINE', 'Digital Solutions for Modern Business'),

    /*
    |--------------------------------------------------------------------------
    | Company Information
    |--------------------------------------------------------------------------
    |
    | Basic company information used throughout the application.
    |
    */

    'company' => [
        'name' => env('COMPANY_NAME', 'DIGIZALL'),
        'email' => env('COMPANY_EMAIL', '<EMAIL>'),
        'phone' => env('COMPANY_PHONE', '+****************'),
        'address' => env('COMPANY_ADDRESS', '123 Business Street, Suite 100'),
        'city' => env('COMPANY_CITY', 'Business City'),
        'state' => env('COMPANY_STATE', 'Business State'),
        'postal_code' => env('COMPANY_POSTAL_CODE', '12345'),
        'country' => env('COMPANY_COUNTRY', 'United States'),
        'website' => env('COMPANY_WEBSITE', 'https://digizall.com'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Social Media Links
    |--------------------------------------------------------------------------
    |
    | Social media profile URLs for the company.
    |
    */

    'social' => [
        'facebook' => env('SOCIAL_FACEBOOK', 'https://facebook.com/digizall'),
        'twitter' => env('SOCIAL_TWITTER', 'https://twitter.com/digizall'),
        'linkedin' => env('SOCIAL_LINKEDIN', 'https://linkedin.com/company/digizall'),
        'instagram' => env('SOCIAL_INSTAGRAM', 'https://instagram.com/digizall'),
        'youtube' => env('SOCIAL_YOUTUBE', ''),
        'github' => env('SOCIAL_GITHUB', ''),
    ],

    /*
    |--------------------------------------------------------------------------
    | Business Hours
    |--------------------------------------------------------------------------
    |
    | Define the business operating hours.
    |
    */

    'business_hours' => [
        'monday' => ['open' => '09:00', 'close' => '18:00'],
        'tuesday' => ['open' => '09:00', 'close' => '18:00'],
        'wednesday' => ['open' => '09:00', 'close' => '18:00'],
        'thursday' => ['open' => '09:00', 'close' => '18:00'],
        'friday' => ['open' => '09:00', 'close' => '18:00'],
        'saturday' => ['open' => '10:00', 'close' => '16:00'],
        'sunday' => ['open' => null, 'close' => null], // Closed
    ],

    /*
    |--------------------------------------------------------------------------
    | SEO Configuration
    |--------------------------------------------------------------------------
    |
    | Default SEO settings for the application.
    |
    */

    'seo' => [
        'default_title' => env('SEO_DEFAULT_TITLE', 'DIGIZALL - Digital Solutions for Modern Business'),
        'default_description' => env('SEO_DEFAULT_DESCRIPTION', 'Professional web development, digital marketing, and technology consulting services. Transform your business with our comprehensive digital solutions.'),
        'default_keywords' => env('SEO_DEFAULT_KEYWORDS', 'web development, digital marketing, SEO, technology consulting, business solutions'),
        'default_image' => env('SEO_DEFAULT_IMAGE', '/images/og-image.jpg'),
        'twitter_handle' => env('SEO_TWITTER_HANDLE', '@digizall'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Features Configuration
    |--------------------------------------------------------------------------
    |
    | Enable or disable specific features of the application.
    |
    */

    'features' => [
        'blog' => env('FEATURE_BLOG', true),
        'services' => env('FEATURE_SERVICES', true),
        'testimonials' => env('FEATURE_TESTIMONIALS', true),
        'contact_form' => env('FEATURE_CONTACT_FORM', true),
        'quote_requests' => env('FEATURE_QUOTE_REQUESTS', true),
        'newsletter' => env('FEATURE_NEWSLETTER', true),
        'analytics' => env('FEATURE_ANALYTICS', true),
        'performance_monitoring' => env('FEATURE_PERFORMANCE_MONITORING', true),
        'attendance_system' => env('FEATURE_ATTENDANCE_SYSTEM', true),
        'user_registration' => env('FEATURE_USER_REGISTRATION', false),
        'comments' => env('FEATURE_COMMENTS', false),
        'multi_language' => env('FEATURE_MULTI_LANGUAGE', false),
    ],

    /*
    |--------------------------------------------------------------------------
    | Email Configuration
    |--------------------------------------------------------------------------
    |
    | Email settings specific to the application.
    |
    */

    'email' => [
        'admin_notifications' => env('EMAIL_ADMIN_NOTIFICATIONS', true),
        'contact_auto_reply' => env('EMAIL_CONTACT_AUTO_REPLY', true),
        'quote_auto_reply' => env('EMAIL_QUOTE_AUTO_REPLY', true),
        'newsletter_enabled' => env('EMAIL_NEWSLETTER_ENABLED', true),
        'admin_email' => env('ADMIN_EMAIL', '<EMAIL>'),
        'contact_email' => env('CONTACT_EMAIL', '<EMAIL>'),
        'no_reply_email' => env('NO_REPLY_EMAIL', '<EMAIL>'),
    ],

    /*
    |--------------------------------------------------------------------------
    | File Upload Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for file uploads throughout the application.
    |
    */

    'uploads' => [
        'max_file_size' => env('UPLOAD_MAX_FILE_SIZE', 2048), // KB
        'allowed_image_types' => ['jpeg', 'jpg', 'png', 'gif', 'webp'],
        'allowed_document_types' => ['pdf', 'doc', 'docx', 'txt'],
        'paths' => [
            'services' => 'uploads/services',
            'blog' => 'uploads/blog',
            'testimonials' => 'uploads/testimonials',
            'users' => 'uploads/users',
            'temp' => 'uploads/temp',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Cache Configuration
    |--------------------------------------------------------------------------
    |
    | Cache settings for application data.
    |
    */

    'cache' => [
        'settings_ttl' => env('CACHE_SETTINGS_TTL', 3600), // 1 hour
        'blog_posts_ttl' => env('CACHE_BLOG_POSTS_TTL', 1800), // 30 minutes
        'services_ttl' => env('CACHE_SERVICES_TTL', 3600), // 1 hour
        'testimonials_ttl' => env('CACHE_TESTIMONIALS_TTL', 7200), // 2 hours
        'navigation_ttl' => env('CACHE_NAVIGATION_TTL', 86400), // 24 hours
    ],

    /*
    |--------------------------------------------------------------------------
    | Security Configuration
    |--------------------------------------------------------------------------
    |
    | Security-related settings for the application.
    |
    */

    'security' => [
        'rate_limiting' => [
            'contact_form' => env('RATE_LIMIT_CONTACT_FORM', 5), // per hour
            'quote_requests' => env('RATE_LIMIT_QUOTE_REQUESTS', 3), // per hour
            'login_attempts' => env('RATE_LIMIT_LOGIN_ATTEMPTS', 5), // per minute
        ],
        'honeypot_enabled' => env('SECURITY_HONEYPOT_ENABLED', true),
        'csrf_protection' => env('SECURITY_CSRF_PROTECTION', true),
        'force_https' => env('SECURITY_FORCE_HTTPS', false),
        'content_security_policy' => env('SECURITY_CSP_ENABLED', false),
    ],

    /*
    |--------------------------------------------------------------------------
    | Analytics Configuration
    |--------------------------------------------------------------------------
    |
    | Analytics and tracking configuration.
    |
    */

    'analytics' => [
        'google_analytics_id' => env('GOOGLE_ANALYTICS_ID'),
        'google_tag_manager_id' => env('GOOGLE_TAG_MANAGER_ID'),
        'facebook_pixel_id' => env('FACEBOOK_PIXEL_ID'),
        'hotjar_id' => env('HOTJAR_ID'),
        'track_page_views' => env('ANALYTICS_TRACK_PAGE_VIEWS', true),
        'track_events' => env('ANALYTICS_TRACK_EVENTS', true),
        'track_conversions' => env('ANALYTICS_TRACK_CONVERSIONS', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | API Configuration
    |--------------------------------------------------------------------------
    |
    | API settings and rate limiting.
    |
    */

    'api' => [
        'enabled' => env('API_ENABLED', false),
        'version' => env('API_VERSION', 'v1'),
        'rate_limit' => env('API_RATE_LIMIT', 60), // requests per minute
        'auth_required' => env('API_AUTH_REQUIRED', true),
        'documentation_enabled' => env('API_DOCS_ENABLED', false),
    ],

    /*
    |--------------------------------------------------------------------------
    | Maintenance Mode
    |--------------------------------------------------------------------------
    |
    | Maintenance mode configuration.
    |
    */

    'maintenance' => [
        'enabled' => env('MAINTENANCE_MODE', false),
        'message' => env('MAINTENANCE_MESSAGE', 'We are currently performing scheduled maintenance. Please check back soon.'),
        'allowed_ips' => explode(',', env('MAINTENANCE_ALLOWED_IPS', '')),
        'retry_after' => env('MAINTENANCE_RETRY_AFTER', 3600), // seconds
    ],

    /*
    |--------------------------------------------------------------------------
    | Backup Configuration
    |--------------------------------------------------------------------------
    |
    | Backup settings for the application.
    |
    */

    'backup' => [
        'enabled' => env('BACKUP_ENABLED', false),
        'schedule' => env('BACKUP_SCHEDULE', 'daily'),
        'retention_days' => env('BACKUP_RETENTION_DAYS', 30),
        'include_uploads' => env('BACKUP_INCLUDE_UPLOADS', true),
        'notification_email' => env('BACKUP_NOTIFICATION_EMAIL'),
    ],

];
