<?php

namespace App\Services;

use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;

class AssetOptimizationService
{
    /**
     * Optimize CSS files
     */
    public static function optimizeCSS(): array
    {
        $cssPath = public_path('build/assets');
        $optimized = [];

        if (!File::exists($cssPath)) {
            return ['error' => 'CSS build directory not found'];
        }

        $cssFiles = File::glob($cssPath . '/*.css');

        foreach ($cssFiles as $file) {
            $originalSize = File::size($file);
            $content = File::get($file);

            // Minify CSS
            $minified = self::minifyCSS($content);
            
            // Write minified content back
            File::put($file, $minified);
            
            $newSize = File::size($file);
            $savings = $originalSize - $newSize;

            $optimized[] = [
                'file' => basename($file),
                'original_size' => $originalSize,
                'new_size' => $newSize,
                'savings' => $savings,
                'savings_percent' => $originalSize > 0 ? round(($savings / $originalSize) * 100, 2) : 0,
            ];
        }

        return $optimized;
    }

    /**
     * Optimize JavaScript files
     */
    public static function optimizeJS(): array
    {
        $jsPath = public_path('build/assets');
        $optimized = [];

        if (!File::exists($jsPath)) {
            return ['error' => 'JS build directory not found'];
        }

        $jsFiles = File::glob($jsPath . '/*.js');

        foreach ($jsFiles as $file) {
            $originalSize = File::size($file);
            $content = File::get($file);

            // Basic JS minification (for production, use proper tools like Terser)
            $minified = self::minifyJS($content);
            
            // Write minified content back
            File::put($file, $minified);
            
            $newSize = File::size($file);
            $savings = $originalSize - $newSize;

            $optimized[] = [
                'file' => basename($file),
                'original_size' => $originalSize,
                'new_size' => $newSize,
                'savings' => $savings,
                'savings_percent' => $originalSize > 0 ? round(($savings / $originalSize) * 100, 2) : 0,
            ];
        }

        return $optimized;
    }

    /**
     * Generate critical CSS for above-the-fold content
     */
    public static function generateCriticalCSS(): string
    {
        // This is a simplified version - in production, use tools like Critical or Penthouse
        $criticalCSS = "
        /* Critical CSS for above-the-fold content */
        body { margin: 0; font-family: 'Figtree', sans-serif; }
        .hero-section { min-height: 100vh; display: flex; align-items: center; }
        .container { max-width: 1200px; margin: 0 auto; padding: 0 1rem; }
        .btn-primary { background: #3b82f6; color: white; padding: 0.75rem 1.5rem; border-radius: 0.5rem; text-decoration: none; display: inline-block; }
        .nav { background: white; box-shadow: 0 1px 3px rgba(0,0,0,0.1); }
        .nav-link { color: #374151; text-decoration: none; padding: 0.5rem 1rem; }
        ";

        return self::minifyCSS($criticalCSS);
    }

    /**
     * Minify CSS content
     */
    private static function minifyCSS(string $css): string
    {
        // Remove comments
        $css = preg_replace('!/\*[^*]*\*+([^/][^*]*\*+)*/!', '', $css);
        
        // Remove unnecessary whitespace
        $css = preg_replace('/\s+/', ' ', $css);
        
        // Remove whitespace around specific characters
        $css = preg_replace('/\s*([{}:;,>+~])\s*/', '$1', $css);
        
        // Remove trailing semicolon before closing brace
        $css = preg_replace('/;(?=\s*})/', '', $css);
        
        // Remove any leading/trailing whitespace
        return trim($css);
    }

    /**
     * Basic JS minification
     */
    private static function minifyJS(string $js): string
    {
        // Remove single-line comments (but preserve URLs)
        $js = preg_replace('/(?<!:)\/\/.*$/m', '', $js);
        
        // Remove multi-line comments
        $js = preg_replace('/\/\*[\s\S]*?\*\//', '', $js);
        
        // Remove unnecessary whitespace (basic)
        $js = preg_replace('/\s+/', ' ', $js);
        
        // Remove whitespace around operators and punctuation
        $js = preg_replace('/\s*([{}();,=+\-*\/])\s*/', '$1', $js);
        
        return trim($js);
    }

    /**
     * Generate asset manifest for cache busting
     */
    public static function generateAssetManifest(): array
    {
        $manifest = [];
        $buildPath = public_path('build');

        if (!File::exists($buildPath)) {
            return $manifest;
        }

        // Get all asset files
        $files = File::allFiles($buildPath);

        foreach ($files as $file) {
            $relativePath = str_replace($buildPath . DIRECTORY_SEPARATOR, '', $file->getPathname());
            $relativePath = str_replace('\\', '/', $relativePath); // Normalize path separators
            
            $manifest[$relativePath] = [
                'path' => '/build/' . $relativePath,
                'hash' => md5_file($file->getPathname()),
                'size' => $file->getSize(),
                'modified' => $file->getMTime(),
            ];
        }

        // Save manifest file
        File::put($buildPath . '/manifest.json', json_encode($manifest, JSON_PRETTY_PRINT));

        return $manifest;
    }

    /**
     * Optimize images in public directory
     */
    public static function optimizePublicImages(): array
    {
        $imagePath = public_path('images');
        $optimized = [];

        if (!File::exists($imagePath)) {
            return ['error' => 'Images directory not found'];
        }

        $imageFiles = File::glob($imagePath . '/*.{jpg,jpeg,png,gif}', GLOB_BRACE);

        foreach ($imageFiles as $file) {
            $originalSize = File::size($file);
            
            // Use ImageOptimizationService for actual optimization
            try {
                $optimizedImage = ImageOptimizationService::optimizeImage($file);
                File::put($file, $optimizedImage->stream());
                
                $newSize = File::size($file);
                $savings = $originalSize - $newSize;

                $optimized[] = [
                    'file' => basename($file),
                    'original_size' => $originalSize,
                    'new_size' => $newSize,
                    'savings' => $savings,
                    'savings_percent' => $originalSize > 0 ? round(($savings / $originalSize) * 100, 2) : 0,
                ];
            } catch (\Exception $e) {
                $optimized[] = [
                    'file' => basename($file),
                    'error' => $e->getMessage(),
                ];
            }
        }

        return $optimized;
    }

    /**
     * Generate WebP versions of images
     */
    public static function generateWebPImages(): array
    {
        $imagePath = public_path('images');
        $generated = [];

        if (!File::exists($imagePath)) {
            return ['error' => 'Images directory not found'];
        }

        $imageFiles = File::glob($imagePath . '/*.{jpg,jpeg,png}', GLOB_BRACE);

        foreach ($imageFiles as $file) {
            try {
                $webpImage = ImageOptimizationService::convertToWebP($file);
                $webpPath = pathinfo($file, PATHINFO_DIRNAME) . '/' . pathinfo($file, PATHINFO_FILENAME) . '.webp';
                
                File::put($webpPath, $webpImage->stream());

                $generated[] = [
                    'original' => basename($file),
                    'webp' => basename($webpPath),
                    'original_size' => File::size($file),
                    'webp_size' => File::size($webpPath),
                    'savings' => File::size($file) - File::size($webpPath),
                ];
            } catch (\Exception $e) {
                $generated[] = [
                    'file' => basename($file),
                    'error' => $e->getMessage(),
                ];
            }
        }

        return $generated;
    }

    /**
     * Clean up old asset files
     */
    public static function cleanupOldAssets(): array
    {
        $buildPath = public_path('build');
        $cleaned = [];

        if (!File::exists($buildPath)) {
            return ['error' => 'Build directory not found'];
        }

        // Get all files older than 7 days
        $files = File::allFiles($buildPath);
        $cutoffTime = time() - (7 * 24 * 60 * 60); // 7 days ago

        foreach ($files as $file) {
            if ($file->getMTime() < $cutoffTime && $file->getFilename() !== 'manifest.json') {
                $size = $file->getSize();
                File::delete($file->getPathname());
                
                $cleaned[] = [
                    'file' => $file->getRelativePathname(),
                    'size' => $size,
                    'age_days' => round((time() - $file->getMTime()) / (24 * 60 * 60)),
                ];
            }
        }

        return $cleaned;
    }

    /**
     * Get asset optimization statistics
     */
    public static function getOptimizationStats(): array
    {
        $buildPath = public_path('build');
        $imagePath = public_path('images');

        $stats = [
            'css_files' => 0,
            'js_files' => 0,
            'image_files' => 0,
            'webp_files' => 0,
            'total_size' => 0,
            'build_exists' => File::exists($buildPath),
            'images_exists' => File::exists($imagePath),
        ];

        if ($stats['build_exists']) {
            $cssFiles = File::glob($buildPath . '/assets/*.css');
            $jsFiles = File::glob($buildPath . '/assets/*.js');
            
            $stats['css_files'] = count($cssFiles);
            $stats['js_files'] = count($jsFiles);
            
            foreach (array_merge($cssFiles, $jsFiles) as $file) {
                $stats['total_size'] += File::size($file);
            }
        }

        if ($stats['images_exists']) {
            $imageFiles = File::glob($imagePath . '/*.{jpg,jpeg,png,gif}', GLOB_BRACE);
            $webpFiles = File::glob($imagePath . '/*.webp');
            
            $stats['image_files'] = count($imageFiles);
            $stats['webp_files'] = count($webpFiles);
            
            foreach (array_merge($imageFiles, $webpFiles) as $file) {
                $stats['total_size'] += File::size($file);
            }
        }

        $stats['total_size_mb'] = round($stats['total_size'] / 1024 / 1024, 2);

        return $stats;
    }

    /**
     * Run complete asset optimization
     */
    public static function runCompleteOptimization(): array
    {
        $results = [
            'css_optimization' => self::optimizeCSS(),
            'js_optimization' => self::optimizeJS(),
            'image_optimization' => self::optimizePublicImages(),
            'webp_generation' => self::generateWebPImages(),
            'manifest_generation' => self::generateAssetManifest(),
            'cleanup' => self::cleanupOldAssets(),
            'stats' => self::getOptimizationStats(),
        ];

        return $results;
    }
}
