<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\BlogTag;

class BlogTagSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $tags = [
            ['name' => 'PHP', 'slug' => 'php', 'color' => '#777BB4', 'is_active' => true],
            ['name' => 'Laravel', 'slug' => 'laravel', 'color' => '#FF2D20', 'is_active' => true],
            ['name' => 'JavaScript', 'slug' => 'javascript', 'color' => '#F7DF1E', 'is_active' => true],
            ['name' => 'React', 'slug' => 'react', 'color' => '#61DAFB', 'is_active' => true],
            ['name' => 'Vue.js', 'slug' => 'vuejs', 'color' => '#4FC08D', 'is_active' => true],
            ['name' => 'Node.js', 'slug' => 'nodejs', 'color' => '#339933', 'is_active' => true],
            ['name' => 'CSS', 'slug' => 'css', 'color' => '#1572B6', 'is_active' => true],
            ['name' => 'HTML', 'slug' => 'html', 'color' => '#E34F26', 'is_active' => true],
            ['name' => 'SEO', 'slug' => 'seo', 'color' => '#4285F4', 'is_active' => true],
            ['name' => 'Marketing', 'slug' => 'marketing', 'color' => '#FF6B6B', 'is_active' => true],
            ['name' => 'Social Media', 'slug' => 'social-media', 'color' => '#1DA1F2', 'is_active' => true],
            ['name' => 'E-commerce', 'slug' => 'ecommerce', 'color' => '#FF9500', 'is_active' => true],
            ['name' => 'Mobile', 'slug' => 'mobile', 'color' => '#34C759', 'is_active' => true],
            ['name' => 'API', 'slug' => 'api', 'color' => '#007AFF', 'is_active' => true],
            ['name' => 'Database', 'slug' => 'database', 'color' => '#336791', 'is_active' => true],
            ['name' => 'Security', 'slug' => 'security', 'color' => '#DC3545', 'is_active' => true],
            ['name' => 'Performance', 'slug' => 'performance', 'color' => '#28A745', 'is_active' => true],
            ['name' => 'UI/UX', 'slug' => 'ui-ux', 'color' => '#6F42C1', 'is_active' => true],
            ['name' => 'WordPress', 'slug' => 'wordpress', 'color' => '#21759B', 'is_active' => true],
            ['name' => 'Cloud', 'slug' => 'cloud', 'color' => '#FF9900', 'is_active' => true],
            ['name' => 'DevOps', 'slug' => 'devops', 'color' => '#326CE5', 'is_active' => true],
            ['name' => 'AI/ML', 'slug' => 'ai-ml', 'color' => '#FF6F00', 'is_active' => true],
            ['name' => 'Blockchain', 'slug' => 'blockchain', 'color' => '#F7931A', 'is_active' => true],
            ['name' => 'Startup', 'slug' => 'startup', 'color' => '#8E44AD', 'is_active' => true],
            ['name' => 'Tips', 'slug' => 'tips', 'color' => '#17A2B8', 'is_active' => true],
            ['name' => 'Best Practices', 'slug' => 'best-practices', 'color' => '#20C997', 'is_active' => true],
            ['name' => 'Case Study', 'slug' => 'case-study', 'color' => '#FD7E14', 'is_active' => true],
            ['name' => 'Tutorial', 'slug' => 'tutorial', 'color' => '#6610F2', 'is_active' => true],
            ['name' => 'News', 'slug' => 'news', 'color' => '#E83E8C', 'is_active' => true],
            ['name' => 'Industry', 'slug' => 'industry', 'color' => '#6C757D', 'is_active' => true],
        ];

        foreach ($tags as $tag) {
            BlogTag::updateOrCreate(
                ['slug' => $tag['slug']],
                $tag
            );
        }

        $this->command->info('Blog tags seeded successfully!');
    }
}
