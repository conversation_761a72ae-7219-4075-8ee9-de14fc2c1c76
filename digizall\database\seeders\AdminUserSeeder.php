<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Role;
use Illuminate\Support\Facades\Hash;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create roles if they don't exist
        $adminRole = Role::firstOrCreate(['name' => 'admin'], [
            'display_name' => 'Administrator',
            'description' => 'Administrator with full system access',
            'permissions' => [
                'manage_users',
                'manage_content',
                'manage_services',
                'manage_blog',
                'manage_contacts',
                'manage_settings',
                'view_analytics',
                'manage_attendance',
            ],
            'is_active' => true,
        ]);

        $hrRole = Role::firstOrCreate(['name' => 'hr'], [
            'display_name' => 'HR Manager',
            'description' => 'HR Manager with attendance and employee management access',
            'permissions' => [
                'manage_attendance',
                'manage_employees',
                'view_reports',
                'manage_leave_requests',
            ],
            'is_active' => true,
        ]);

        $employeeRole = Role::firstOrCreate(['name' => 'employee'], [
            'display_name' => 'Employee',
            'description' => 'Employee with basic attendance access',
            'permissions' => [
                'check_in_out',
                'view_own_attendance',
                'request_leave',
            ],
            'is_active' => true,
        ]);

        // Create admin user
        $adminUser = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'DIGIZALL Admin',
                'email' => '<EMAIL>',
                'password' => Hash::make('admin123'),
                'email_verified_at' => now(),
                'phone' => '+****************',
                'employee_id' => 'ADMIN001',
                'department' => 'Administration',
                'position' => 'System Administrator',
                'hire_date' => now()->subYears(2),
                'hourly_rate' => 50.00,
                'work_start_time' => '09:00:00',
                'work_end_time' => '17:00:00',
                'is_active' => true,
                'last_login_at' => now(),
            ]
        );

        // Attach admin role
        if (!$adminUser->roles()->where('role_id', $adminRole->id)->exists()) {
            $adminUser->roles()->attach($adminRole->id);
        }

        // Create HR user
        $hrUser = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'HR Manager',
                'email' => '<EMAIL>',
                'password' => Hash::make('hr123'),
                'email_verified_at' => now(),
                'phone' => '+****************',
                'employee_id' => 'HR001',
                'department' => 'Human Resources',
                'position' => 'HR Manager',
                'hire_date' => now()->subYears(1),
                'hourly_rate' => 35.00,
                'work_start_time' => '09:00:00',
                'work_end_time' => '17:00:00',
                'is_active' => true,
                'last_login_at' => null,
            ]
        );

        // Attach HR role
        if (!$hrUser->roles()->where('role_id', $hrRole->id)->exists()) {
            $hrUser->roles()->attach($hrRole->id);
        }

        // Create sample employee user
        $employeeUser = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'John Doe',
                'email' => '<EMAIL>',
                'password' => Hash::make('employee123'),
                'email_verified_at' => now(),
                'phone' => '+****************',
                'employee_id' => 'EMP001',
                'department' => 'Development',
                'position' => 'Web Developer',
                'hire_date' => now()->subMonths(6),
                'hourly_rate' => 25.00,
                'work_start_time' => '09:00:00',
                'work_end_time' => '17:00:00',
                'is_active' => true,
                'last_login_at' => null,
            ]
        );

        // Attach employee role
        if (!$employeeUser->roles()->where('role_id', $employeeRole->id)->exists()) {
            $employeeUser->roles()->attach($employeeRole->id);
        }

        $this->command->info('Admin users and roles seeded successfully!');
        $this->command->info('Admin Login: <EMAIL> / admin123');
        $this->command->info('HR Login: <EMAIL> / hr123');
        $this->command->info('Employee Login: <EMAIL> / employee123');
    }
}
