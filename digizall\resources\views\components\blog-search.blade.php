@props([
    'placeholder' => 'Search articles...',
    'showFilters' => true,
    'categories' => [],
    'tags' => [],
    'currentCategory' => null,
    'currentTag' => null,
    'currentSearch' => null
])

<div class="bg-white rounded-xl shadow-lg p-6" x-data="{ showFilters: false }">
    <!-- Search Form -->
    <form method="GET" action="{{ route('blog.index') }}" class="space-y-4">
        <!-- Search Input -->
        <div class="relative">
            <input type="text" 
                   name="search" 
                   value="{{ $currentSearch }}"
                   placeholder="{{ $placeholder }}" 
                   class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <i class="fas fa-search text-gray-400"></i>
            </div>
        </div>

        @if($showFilters)
            <!-- Filter Toggle -->
            <div class="flex items-center justify-between">
                <button type="button" 
                        @click="showFilters = !showFilters"
                        class="flex items-center text-gray-600 hover:text-primary-600 transition-colors">
                    <i class="fas fa-filter mr-2"></i>
                    <span>Filters</span>
                    <i class="fas fa-chevron-down ml-2 transform transition-transform" 
                       :class="{ 'rotate-180': showFilters }"></i>
                </button>
                
                <!-- Clear Filters -->
                @if($currentCategory || $currentTag || $currentSearch)
                    <a href="{{ route('blog.index') }}" 
                       class="text-sm text-gray-500 hover:text-primary-600 transition-colors">
                        Clear all filters
                    </a>
                @endif
            </div>

            <!-- Filters Panel -->
            <div x-show="showFilters" 
                 x-transition:enter="transition ease-out duration-200"
                 x-transition:enter-start="opacity-0 transform -translate-y-2"
                 x-transition:enter-end="opacity-100 transform translate-y-0"
                 class="space-y-4 pt-4 border-t border-gray-200">
                
                <!-- Category Filter -->
                @if(count($categories) > 0)
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Category</label>
                        <select name="category" 
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                            <option value="">All Categories</option>
                            @foreach($categories as $category)
                                <option value="{{ $category->slug }}" 
                                        {{ $currentCategory === $category->slug ? 'selected' : '' }}>
                                    {{ $category->name }} ({{ $category->posts_count ?? 0 }})
                                </option>
                            @endforeach
                        </select>
                    </div>
                @endif

                <!-- Tag Filter -->
                @if(count($tags) > 0)
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Tag</label>
                        <select name="tag" 
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                            <option value="">All Tags</option>
                            @foreach($tags as $tag)
                                <option value="{{ $tag->slug }}" 
                                        {{ $currentTag === $tag->slug ? 'selected' : '' }}>
                                    #{{ $tag->name }} ({{ $tag->posts_count ?? 0 }})
                                </option>
                            @endforeach
                        </select>
                    </div>
                @endif

                <!-- Date Range Filter -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">From Date</label>
                        <input type="date" 
                               name="date_from" 
                               value="{{ request('date_from') }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">To Date</label>
                        <input type="date" 
                               name="date_to" 
                               value="{{ request('date_to') }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                    </div>
                </div>

                <!-- Sort Options -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Sort By</label>
                    <select name="sort" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                        <option value="latest" {{ request('sort') === 'latest' ? 'selected' : '' }}>Latest First</option>
                        <option value="oldest" {{ request('sort') === 'oldest' ? 'selected' : '' }}>Oldest First</option>
                        <option value="popular" {{ request('sort') === 'popular' ? 'selected' : '' }}>Most Popular</option>
                        <option value="title" {{ request('sort') === 'title' ? 'selected' : '' }}>Title A-Z</option>
                    </select>
                </div>
            </div>
        @endif

        <!-- Search Button -->
        <button type="submit" 
                class="w-full btn-primary">
            <i class="fas fa-search mr-2"></i>
            Search Articles
        </button>
    </form>

    <!-- Active Filters Display -->
    @if($currentCategory || $currentTag || $currentSearch)
        <div class="mt-4 pt-4 border-t border-gray-200">
            <div class="flex items-center justify-between mb-2">
                <span class="text-sm font-medium text-gray-700">Active Filters:</span>
                <a href="{{ route('blog.index') }}" 
                   class="text-xs text-gray-500 hover:text-primary-600 transition-colors">
                    Clear All
                </a>
            </div>
            <div class="flex flex-wrap gap-2">
                @if($currentSearch)
                    <span class="inline-flex items-center px-3 py-1 bg-primary-100 text-primary-800 rounded-full text-sm">
                        Search: "{{ $currentSearch }}"
                        <a href="{{ route('blog.index', array_filter(request()->except('search'))) }}" 
                           class="ml-2 text-primary-600 hover:text-primary-800">
                            <i class="fas fa-times"></i>
                        </a>
                    </span>
                @endif
                
                @if($currentCategory)
                    <span class="inline-flex items-center px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">
                        Category: {{ ucwords(str_replace('-', ' ', $currentCategory)) }}
                        <a href="{{ route('blog.index', array_filter(request()->except('category'))) }}" 
                           class="ml-2 text-blue-600 hover:text-blue-800">
                            <i class="fas fa-times"></i>
                        </a>
                    </span>
                @endif
                
                @if($currentTag)
                    <span class="inline-flex items-center px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">
                        Tag: #{{ ucwords(str_replace('-', ' ', $currentTag)) }}
                        <a href="{{ route('blog.index', array_filter(request()->except('tag'))) }}" 
                           class="ml-2 text-green-600 hover:text-green-800">
                            <i class="fas fa-times"></i>
                        </a>
                    </span>
                @endif
            </div>
        </div>
    @endif
</div>
