<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Department>
 */
class DepartmentFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $departments = [
            'Human Resources',
            'Information Technology',
            'Marketing',
            'Sales',
            'Finance',
            'Operations',
            'Customer Service',
            'Research & Development'
        ];

        $name = $this->faker->randomElement($departments);

        return [
            'name' => $name,
            'code' => strtoupper(substr(str_replace(' ', '', $name), 0, 3)) . $this->faker->numberBetween(1, 99),
            'description' => $this->faker->sentence(),
            'manager_id' => null, // Will be set separately if needed
            'is_active' => $this->faker->boolean(90), // 90% chance of being active
        ];
    }
}
