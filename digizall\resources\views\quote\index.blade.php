@extends('layouts.app')

@section('title', $metaTags->meta_title ?? 'Get Free Quote - DIGIZALL')
@section('meta_description', $metaTags->meta_description ?? 'Get a free quote for your digital project. Tell us about your requirements and we\'ll provide a customized solution for your business.')
@section('meta_keywords', $metaTags->meta_keywords ?? 'free quote, project estimate, digital services quote, web development quote')

@if($metaTags)
    @section('og_title', $metaTags->og_title)
    @section('og_description', $metaTags->og_description)
    @section('og_image', $metaTags->og_image_url)
@endif

@section('content')

<!-- Page Header -->
<section class="bg-gradient-to-r from-primary-600 to-secondary-600 text-white py-20">
    <div class="container-custom">
        <div class="text-center" data-aos="fade-up">
            <h1 class="heading-primary text-white mb-4">
                Get Your Free Quote
            </h1>
            <p class="text-xl text-gray-200 max-w-2xl mx-auto">
                Tell us about your project and we'll provide you with a detailed quote tailored to your specific needs and budget.
            </p>
        </div>
    </div>
</section>

<!-- Quote Form Section -->
<section class="section-padding">
    <div class="container-custom">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-12">
            <!-- Quote Form -->
            <div class="lg:col-span-2" data-aos="fade-right">
                <div class="bg-white rounded-xl shadow-lg p-8">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">Project Details</h2>
                    
                    @if(session('success'))
                        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
                            {{ session('success') }}
                        </div>
                    @endif

                    @if(session('error'))
                        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
                            {{ session('error') }}
                        </div>
                    @endif

                    <form action="{{ route('quote.store') }}" method="POST" class="space-y-6" x-data="quoteForm()">
                        @csrf
                        
                        <!-- Personal Information -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Full Name *</label>
                                <input type="text" id="name" name="name" value="{{ old('name') }}" required
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 @error('name') border-red-500 @enderror"
                                       placeholder="Enter your full name">
                                @error('name')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                            <div>
                                <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email Address *</label>
                                <input type="email" id="email" name="email" value="{{ old('email') }}" required
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 @error('email') border-red-500 @enderror"
                                       placeholder="Enter your email">
                                @error('email')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                                <input type="tel" id="phone" name="phone" value="{{ old('phone') }}"
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 @error('phone') border-red-500 @enderror"
                                       placeholder="Enter your phone number">
                                @error('phone')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                            <div>
                                <label for="company" class="block text-sm font-medium text-gray-700 mb-2">Company Name</label>
                                <input type="text" id="company" name="company" value="{{ old('company') }}"
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 @error('company') border-red-500 @enderror"
                                       placeholder="Enter your company name">
                                @error('company')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <div>
                            <label for="website" class="block text-sm font-medium text-gray-700 mb-2">Current Website (if any)</label>
                            <input type="url" id="website" name="website" value="{{ old('website') }}"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 @error('website') border-red-500 @enderror"
                                   placeholder="https://yourwebsite.com">
                            @error('website')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Project Information -->
                        <div class="border-t border-gray-200 pt-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Project Information</h3>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="service" class="block text-sm font-medium text-gray-700 mb-2">Service Required *</label>
                                    <select id="service" name="service" required x-model="selectedService"
                                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 @error('service') border-red-500 @enderror">
                                        <option value="">Select a service</option>
                                        @if($services->count() > 0)
                                            @foreach($services as $service)
                                                <option value="{{ $service->slug }}" {{ old('service', $selectedService) === $service->slug ? 'selected' : '' }}>
                                                    {{ $service->title }}
                                                </option>
                                            @endforeach
                                        @else
                                            <option value="website-design-development">Website Design & Development</option>
                                            <option value="web-application-development">Web Application Development</option>
                                            <option value="ecommerce-development">E-commerce Development</option>
                                            <option value="digital-marketing">Digital Marketing</option>
                                            <option value="seo-services">SEO Services</option>
                                            <option value="content-writing">Content Writing</option>
                                            <option value="graphic-design">Graphic Design</option>
                                            <option value="other">Other</option>
                                        @endif
                                    </select>
                                    @error('service')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>
                                <div>
                                    <label for="project_type" class="block text-sm font-medium text-gray-700 mb-2">Project Type *</label>
                                    <select id="project_type" name="project_type" required
                                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 @error('project_type') border-red-500 @enderror">
                                        <option value="">Select project type</option>
                                        <option value="new-project" {{ old('project_type') === 'new-project' ? 'selected' : '' }}>New Project</option>
                                        <option value="redesign" {{ old('project_type') === 'redesign' ? 'selected' : '' }}>Website Redesign</option>
                                        <option value="maintenance" {{ old('project_type') === 'maintenance' ? 'selected' : '' }}>Maintenance & Updates</option>
                                        <option value="consultation" {{ old('project_type') === 'consultation' ? 'selected' : '' }}>Consultation</option>
                                    </select>
                                    @error('project_type')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="budget" class="block text-sm font-medium text-gray-700 mb-2">Budget Range *</label>
                                    <select id="budget" name="budget" required
                                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 @error('budget') border-red-500 @enderror">
                                        <option value="">Select budget range</option>
                                        <option value="under-1000" {{ old('budget') === 'under-1000' ? 'selected' : '' }}>Under $1,000</option>
                                        <option value="1000-5000" {{ old('budget') === '1000-5000' ? 'selected' : '' }}>$1,000 - $5,000</option>
                                        <option value="5000-10000" {{ old('budget') === '5000-10000' ? 'selected' : '' }}>$5,000 - $10,000</option>
                                        <option value="10000-25000" {{ old('budget') === '10000-25000' ? 'selected' : '' }}>$10,000 - $25,000</option>
                                        <option value="25000-50000" {{ old('budget') === '25000-50000' ? 'selected' : '' }}>$25,000 - $50,000</option>
                                        <option value="over-50000" {{ old('budget') === 'over-50000' ? 'selected' : '' }}>Over $50,000</option>
                                    </select>
                                    @error('budget')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>
                                <div>
                                    <label for="timeline" class="block text-sm font-medium text-gray-700 mb-2">Timeline *</label>
                                    <select id="timeline" name="timeline" required
                                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 @error('timeline') border-red-500 @enderror">
                                        <option value="">Select timeline</option>
                                        <option value="asap" {{ old('timeline') === 'asap' ? 'selected' : '' }}>ASAP</option>
                                        <option value="1-month" {{ old('timeline') === '1-month' ? 'selected' : '' }}>Within 1 month</option>
                                        <option value="2-3-months" {{ old('timeline') === '2-3-months' ? 'selected' : '' }}>2-3 months</option>
                                        <option value="3-6-months" {{ old('timeline') === '3-6-months' ? 'selected' : '' }}>3-6 months</option>
                                        <option value="6-months-plus" {{ old('timeline') === '6-months-plus' ? 'selected' : '' }}>6+ months</option>
                                        <option value="flexible" {{ old('timeline') === 'flexible' ? 'selected' : '' }}>Flexible</option>
                                    </select>
                                    @error('timeline')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Project Description -->
                        <div>
                            <label for="description" class="block text-sm font-medium text-gray-700 mb-2">Project Description *</label>
                            <textarea id="description" name="description" rows="6" required
                                      class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 @error('description') border-red-500 @enderror"
                                      placeholder="Please describe your project in detail. Include your goals, target audience, specific requirements, and any other relevant information...">{{ old('description') }}</textarea>
                            @error('description')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                            <p class="mt-1 text-sm text-gray-500">Be as detailed as possible to help us provide an accurate quote.</p>
                        </div>

                        <!-- Required Features -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-3">Required Features (select all that apply)</label>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                                @php
                                    $features = [
                                        'responsive-design' => 'Responsive Design',
                                        'cms-integration' => 'CMS Integration',
                                        'ecommerce' => 'E-commerce Functionality',
                                        'payment-gateway' => 'Payment Gateway',
                                        'user-accounts' => 'User Accounts/Login',
                                        'search-functionality' => 'Search Functionality',
                                        'social-media' => 'Social Media Integration',
                                        'analytics' => 'Analytics Integration',
                                        'seo-optimization' => 'SEO Optimization',
                                        'multilingual' => 'Multilingual Support',
                                        'api-integration' => 'API Integration',
                                        'custom-forms' => 'Custom Forms',
                                    ];
                                @endphp
                                @foreach($features as $value => $label)
                                    <label class="flex items-center">
                                        <input type="checkbox" name="features[]" value="{{ $value }}"
                                               {{ in_array($value, old('features', [])) ? 'checked' : '' }}
                                               class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                                        <span class="ml-2 text-sm text-gray-700">{{ $label }}</span>
                                    </label>
                                @endforeach
                            </div>
                        </div>

                        <!-- Additional Information -->
                        <div>
                            <label for="additional_info" class="block text-sm font-medium text-gray-700 mb-2">Additional Information</label>
                            <textarea id="additional_info" name="additional_info" rows="4"
                                      class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                                      placeholder="Any additional information, special requirements, or questions...">{{ old('additional_info') }}</textarea>
                        </div>

                        <!-- Contact Preferences -->
                        <div class="border-t border-gray-200 pt-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Contact Preferences</h3>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-3">Preferred Contact Method *</label>
                                <div class="space-y-2">
                                    <label class="flex items-center">
                                        <input type="radio" name="preferred_contact" value="email"
                                               {{ old('preferred_contact', 'email') === 'email' ? 'checked' : '' }}
                                               class="border-gray-300 text-primary-600 focus:ring-primary-500">
                                        <span class="ml-2 text-sm text-gray-700">Email</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="radio" name="preferred_contact" value="phone"
                                               {{ old('preferred_contact') === 'phone' ? 'checked' : '' }}
                                               class="border-gray-300 text-primary-600 focus:ring-primary-500">
                                        <span class="ml-2 text-sm text-gray-700">Phone Call</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="radio" name="preferred_contact" value="whatsapp"
                                               {{ old('preferred_contact') === 'whatsapp' ? 'checked' : '' }}
                                               class="border-gray-300 text-primary-600 focus:ring-primary-500">
                                        <span class="ml-2 text-sm text-gray-700">WhatsApp</span>
                                    </label>
                                </div>
                                @error('preferred_contact')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div class="mt-4">
                                <label class="flex items-start">
                                    <input type="checkbox" name="marketing_consent" value="1"
                                           {{ old('marketing_consent') ? 'checked' : '' }}
                                           class="mt-1 rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                                    <span class="ml-2 text-sm text-gray-700">
                                        I agree to receive marketing communications from DIGIZALL about services and updates. You can unsubscribe at any time.
                                    </span>
                                </label>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="pt-6">
                            <button type="submit" class="w-full btn-primary text-lg py-4">
                                <i class="fas fa-paper-plane mr-2"></i>
                                Submit Quote Request
                            </button>
                            <p class="mt-3 text-sm text-gray-500 text-center">
                                We'll review your request and get back to you within 24 hours with a detailed quote.
                            </p>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-1" data-aos="fade-left">
                <div class="space-y-8">
                    <!-- Why Choose Us -->
                    <div class="bg-white rounded-xl shadow-lg p-6">
                        <h3 class="text-xl font-bold text-gray-900 mb-6">Why Choose DIGIZALL?</h3>
                        <div class="space-y-4">
                            <div class="flex items-start space-x-3">
                                <div class="flex-shrink-0 w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                                    <i class="fas fa-check text-primary-600 text-sm"></i>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900">Free Consultation</h4>
                                    <p class="text-sm text-gray-600">Detailed project analysis and recommendations</p>
                                </div>
                            </div>
                            <div class="flex items-start space-x-3">
                                <div class="flex-shrink-0 w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                                    <i class="fas fa-clock text-primary-600 text-sm"></i>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900">Quick Response</h4>
                                    <p class="text-sm text-gray-600">Response within 24 hours guaranteed</p>
                                </div>
                            </div>
                            <div class="flex items-start space-x-3">
                                <div class="flex-shrink-0 w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                                    <i class="fas fa-shield-alt text-primary-600 text-sm"></i>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900">No Obligation</h4>
                                    <p class="text-sm text-gray-600">Free quote with no strings attached</p>
                                </div>
                            </div>
                            <div class="flex items-start space-x-3">
                                <div class="flex-shrink-0 w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                                    <i class="fas fa-users text-primary-600 text-sm"></i>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900">Expert Team</h4>
                                    <p class="text-sm text-gray-600">Experienced professionals at your service</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Contact Information -->
                    <div class="bg-gradient-to-br from-primary-600 to-secondary-600 rounded-xl p-6 text-white">
                        <h3 class="text-xl font-bold mb-4">Need Help?</h3>
                        <p class="text-primary-100 mb-6 text-sm">
                            Have questions about our services or need assistance with the form? We're here to help!
                        </p>
                        <div class="space-y-3">
                            <a href="tel:+923084281241" class="flex items-center text-white hover:text-primary-200 transition-colors">
                                <i class="fas fa-phone mr-3"></i>
                                <span>+92-308-4281241</span>
                            </a>
                            <a href="mailto:<EMAIL>" class="flex items-center text-white hover:text-primary-200 transition-colors">
                                <i class="fas fa-envelope mr-3"></i>
                                <span><EMAIL></span>
                            </a>
                            <a href="https://wa.me/+923084281241" target="_blank" class="flex items-center text-white hover:text-primary-200 transition-colors">
                                <i class="fab fa-whatsapp mr-3"></i>
                                <span>WhatsApp Chat</span>
                            </a>
                        </div>
                    </div>

                    <!-- Process Steps -->
                    <div class="bg-gray-50 rounded-xl p-6">
                        <h3 class="text-xl font-bold text-gray-900 mb-6">Our Process</h3>
                        <div class="space-y-4">
                            <div class="flex items-start space-x-3">
                                <div class="flex-shrink-0 w-6 h-6 bg-primary-600 text-white rounded-full flex items-center justify-center text-xs font-bold">1</div>
                                <div>
                                    <h4 class="font-semibold text-gray-900 text-sm">Submit Request</h4>
                                    <p class="text-xs text-gray-600">Fill out the quote form with your project details</p>
                                </div>
                            </div>
                            <div class="flex items-start space-x-3">
                                <div class="flex-shrink-0 w-6 h-6 bg-primary-600 text-white rounded-full flex items-center justify-center text-xs font-bold">2</div>
                                <div>
                                    <h4 class="font-semibold text-gray-900 text-sm">Analysis</h4>
                                    <p class="text-xs text-gray-600">We analyze your requirements and prepare a detailed quote</p>
                                </div>
                            </div>
                            <div class="flex items-start space-x-3">
                                <div class="flex-shrink-0 w-6 h-6 bg-primary-600 text-white rounded-full flex items-center justify-center text-xs font-bold">3</div>
                                <div>
                                    <h4 class="font-semibold text-gray-900 text-sm">Proposal</h4>
                                    <p class="text-xs text-gray-600">Receive a comprehensive proposal with timeline and pricing</p>
                                </div>
                            </div>
                            <div class="flex items-start space-x-3">
                                <div class="flex-shrink-0 w-6 h-6 bg-primary-600 text-white rounded-full flex items-center justify-center text-xs font-bold">4</div>
                                <div>
                                    <h4 class="font-semibold text-gray-900 text-sm">Discussion</h4>
                                    <p class="text-xs text-gray-600">Schedule a call to discuss details and finalize the project</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

@endsection

@push('scripts')
<script>
function quoteForm() {
    return {
        selectedService: '{{ old('service', $selectedService) }}',
        init() {
            // Auto-select service if provided in URL
            if (this.selectedService) {
                this.$nextTick(() => {
                    document.getElementById('service').value = this.selectedService;
                });
            }
        }
    }
}
</script>
@endpush
